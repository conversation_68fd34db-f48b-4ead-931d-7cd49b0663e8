{"name": "expectations-outcomes-theme", "type": "module", "version": "0.0.1", "scripts": {"dev": "astro dev", "pagefind-search": "npx tsc ./pagefindtohtml/mrkdowntohtml.ts && mv ./pagefindtohtml/mrkdowntohtml.js ./pagefindtohtml/mrkdowntohtml.cjs && node ./pagefindtohtml/mrkdowntohtml.cjs", "latest-gitcommitdetails": "npx tsc ./gitcommit-details/gitLatest.json.ts && mv ./gitcommit-details/gitLatest.json.js ./gitcommit-details/gitLatest.json.cjs && node ./gitcommit-details/gitLatest.json.cjs", "build": "npx pagefind --site htmloutput && npx shx cp -r htmloutput public && pnpm run latest-gitcommitdetails && pnpm run surveilrcommits && astro build && pnpm run postbuild", "preview": "astro preview", "astro": "astro", "postbuild": "mkdir -p dist/content && cp -r src/content/lforms/ dist/content/lforms/", "generate-db-models": "deno task gen-psql && deno run --allow-read --allow-env --allow-run --allow-write --allow-sys --unstable support/services/createDBFromModels.ts", "generate-db-views": "deno run --allow-read --allow-env --allow-run --allow-write --allow-sys --unstable support/services/viewCreationInRSSD.ts", "sync-db-and-users": "pnpm run generate-db-models && pnpm run generate-db-views && deno run --allow-read --allow-write --allow-net --allow-env --allow-sys --allow-run --unstable support/services/syncZitadelUserToRssd.ts", "sync-users": "deno run --allow-read --allow-write --allow-net --allow-env --allow-sys --allow-run --unstable support/services/syncZitadelUserToRssd.ts --syncUsersOnly=true", "prepare-imap-db": "deno run -A imap-prepare.ts", "prepare-qualityfolio-db": "deno run -A qualityfolio-surveilr-prepare.ts", "postinstall": "playwright install chromium", "surveilrcommits": "node src/pages/api/fetch-git-commits.js"}, "dependencies": {"@astrojs/mdx": "^4.3.0", "@astrojs/node": "^9.3.0", "@astrojs/react": "^4.3.0", "@astrojs/tailwind": "^6.0.2", "@beoe/cache": "^0.1.0", "@beoe/pan-zoom": "^0.0.4", "@beoe/rehype-mermaid": "^0.4.2", "@sqltools/formatter": "^1.2.5", "@types/ag-grid": "^3.2.0", "@types/he": "^1.2.3", "@types/react": "^19.0.4", "@types/react-dom": "^19.0.2", "ag-grid-community": "32.1.0", "ag-grid-enterprise": "32.1.0", "ag-grid-react": "32.1.0", "astro": "^5.11.0", "astro-breadcrumbs": "^3.3.1", "astro-plantuml": "^0.1.4", "axios": "^1.7.9", "better-sqlite3": "^11.8.1", "drizzle-orm": "^0.39.3", "github-discussions-blog-loader": "^1.0.1", "googleapis": "^148.0.0", "gravatar-url": "^4.0.1", "gray-matter": "^4.0.3", "he": "^1.2.0", "js-cookie": "^3.0.5", "js-yaml": "^4.1.0", "jwt-decode": "^4.0.0", "markdown-it": "^14.1.0", "marked": "^15.0.6", "moment": "^2.30.1", "node-fetch": "^3.3.2", "oidc-react": "^3.4.1", "path": "^0.12.7", "playwright": "^1.51.0", "querystring": "^0.2.1", "react": "^19.0.0", "react-data-table-component": "^7.6.2", "react-dom": "^19.0.0", "react-markdown-editor-lite": "^1.3.4", "reading-time-estimator": "^1.11.0", "rehype-mermaid": "^3.0.0", "reveal.js": "^5.1.0", "rsuite": "^5.79.1", "sequelize": "^6.37.5", "sharp": "^0.33.5", "slugify": "^1.6.6", "sqlite3": "^5.1.7", "string-strip-html": "^13.4.8", "typescript": "^5.7.3", "zod": "^3.24.1"}, "devDependencies": {"@tailwindcss/forms": "^0.5.9", "@tailwindcss/typography": "^0.5.15", "@types/better-sqlite3": "^7.6.12", "@types/js-cookie": "^3.0.6", "@types/node": "^22.10.5", "pagefind": "^1.3.0", "sass-embedded": "^1.83.1", "tailwindcss": "^3.4.17"}}