# build output
dist/

# generated types
.astro/

# dependencies
node_modules/

gitcommit-details/githubLatestCommit.json
surveilr-commit-details/commits.json

# logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# environment variables
.env
.env.production

htmloutput/
public/htmloutput/

# demo db
database-query-renderer-demo/employee.db
user_demo_db/resource-surveillance.sqlite.db

# db
src/content/db/imap-mail-db/resource-surveillance.sqlite.db
src/content/db/lforms/resource-surveillance.sqlite.db
src/content/db/models/resource-surveillance.sqlite.db
src/content/db/users/resource-surveillance.sqlite.db
*.db
public/beoe

#box and g drive auth files
src/keys/