name: CI/CD Workflow
on:
  push:
    branches:
      - main
  workflow_dispatch:

jobs:
  build-and-deploy:
    runs-on: self-hosted

    steps:
      - name: Checkout code
        uses: actions/checkout@v2
        with:
          ref: main
          fetch-depth: 0
          
      - name: Node and pnpm versions
        run: |
          node --version
          pnpm --version

      - name: Execute Environment variables
        env:
          PUBLIC_GITHUB_TOKEN: ${{ secrets.PUBLIC_GITHUB_TOKEN }}
          PUBLIC_RELEASE_NOTES_GITHUB_TOKEN: ${{ secrets.PUBLIC_RELEASE_NOTES_GITHUB_TOKEN }}
          PUBLIC_GITHUB_REPO_NAME: ${{ vars.PUBLIC_GITHUB_REPO_NAME }}
          PUBLIC_GITHUB_OWNER_NAME: ${{ vars.PUBLIC_GITHUB_OWNER_NAME }}
          PUBLIC_ZITADEL_CLIENT_ID: ${{ vars.PUBLIC_ZITADEL_CLIENT_ID }}
          PUBLIC_ZITADEL_AUTHORITY: ${{ vars.PUBLIC_ZITADEL_AUTHORITY }}
          PUBLIC_ZITADEL_REDIRECT_URI: ${{ vars.PUBLIC_ZITADEL_REDIRECT_URI }}
          PUBLIC_ZITADEL_LOGOUT_REDIRECT_URI: ${{ vars.PUBLIC_ZITADEL_LOGOUT_REDIRECT_URI }}
          PUBLIC_ZITADEL_ORGANIZATION_ID: ${{ vars.PUBLIC_ZITADEL_ORGANIZATION_ID }}
          PUBLIC_ZITADEL_PROJECT_ID: ${{ vars.PUBLIC_ZITADEL_PROJECT_ID }}
          ENABLE_DEFAULT_AUTH: ${{ vars.ENABLE_DEFAULT_AUTH }}
          ENABLE_ZITADEL_AUTH: ${{ vars.ENABLE_ZITADEL_AUTH }}
          PUBLIC_ZITADEL_API_TOKEN: ${{ vars.PUBLIC_ZITADEL_API_TOKEN }}
          PUBLIC_QUALITYFOLIO_URL: ${{ vars.PUBLIC_QUALITYFOLIO_URL }}
          ENABLE_OPEN_OBSERVE: ${{ vars.ENABLE_OPEN_OBSERVE }}
          PUBLIC_TEAM_DB: ${{ vars.PUBLIC_TEAM_DB }}
          ENABLE_DB_TEAM: ${{ vars.ENABLE_DB_TEAM }}
          PUBLIC_LFORM_DB: ${{ vars.PUBLIC_LFORM_DB }}
          ENABLE_IMAP_DB_PREPARE: ${{ vars.ENABLE_IMAP_DB_PREPARE }}
          ENABLE_IMAP_VIEW: ${{ vars.ENABLE_IMAP_VIEW  }}
          PUBLIC_IMAP_DB: ${{ vars.PUBLIC_IMAP_DB }}
          IMAP_FOLDER: ${{ vars.IMAP_FOLDER }}
          IMAP_USER_NAME: ${{ vars.IMAP_USER_NAME }}
          IMAP_PASS: ${{ vars.IMAP_PASS }}
          IMAP_HOST: ${{ vars.IMAP_HOST }}
          PUBLIC_OPENOBSERVE_URL: ${{ vars.PUBLIC_OPENOBSERVE_URL }}
          PUBLIC_OPENOBSERVE_TOKEN: ${{ vars.PUBLIC_OPENOBSERVE_TOKEN }}
          PUBLIC_NMAP_DB: ${{ vars.PUBLIC_NMAP_DB }}
          PUBLIC_RSSD_DB: ${{ vars.PUBLIC_RSSD_DB }}
          PUBLIC_NOVU_API_URL: ${{ vars.PUBLIC_NOVU_API_URL }}
          PUBLIC_NOVU_SUBSCRIBER_ID: ${{ vars.PUBLIC_NOVU_SUBSCRIBER_ID }}
          PUBLIC_NOVU_API_KEY: ${{ vars.PUBLIC_NOVU_API_KEY }}
          PUBLIC_NOVU_CONTACTUS_TEMPLATE: ${{ vars.PUBLIC_NOVU_CONTACTUS_TEMPLATE }}
          PUBLIC_NOVU_CONTACTUS_ADMIN_EMAIL: ${{ vars.PUBLIC_NOVU_CONTACTUS_ADMIN_EMAIL }}
          PUBLIC_NOVU_ADMIN_TEMPLATE: ${{ vars.PUBLIC_NOVU_ADMIN_TEMPLATE }}
          PUBLIC_GITHUB_PAT: ${{ secrets.PUBLIC_GITHUB_PAT }}
          PUBLIC_GITHUB_OWNER: ${{ vars.PUBLIC_GITHUB_OWNER }}
          PUBLIC_GITHUB_REPO: ${{ vars.PUBLIC_GITHUB_REPO }}
          ENABLE_SUPPORT_AND_FEEDBACK: ${{ vars.ENABLE_SUPPORT_AND_FEEDBACK }}
          PUBLIC_PRODUCTION_URL: ${{ vars.PUBLIC_PRODUCTION_URL }}
          PUBLIC_NOVU_COMMENT_NOTIFICATION_TEMPLATE: ${{ vars.PUBLIC_NOVU_COMMENT_NOTIFICATION_TEMPLATE }}
          PUBLIC_SQLMAP_DB: ${{ vars.PUBLIC_SQLMAP_DB }}  
          PUBLIC_FLEETFOLIO_URL: ${{ vars.PUBLIC_FLEETFOLIO_URL }}
          PUBLIC_NOTIFICATION_FOR_ALL_MEMBERS: ${{ vars.PUBLIC_NOTIFICATION_FOR_ALL_MEMBERS }}
          PUBLIC_ZAP_DB: ${{ vars.PUBLIC_ZAP_DB }}
          BOX_CLIENT_ID: ${{ vars.BOX_CLIENT_ID }} 
          BOX_CLIENT_SECRET: ${{ vars.BOX_CLIENT_SECRET }}
          BOX_ACCESS_TOKEN: ${{ vars.BOX_ACCESS_TOKEN }}
          BOX_REFRESH_TOKEN: ${{ vars.BOX_REFRESH_TOKEN }}
          BOX_FOLDER_ID: ${{ vars.BOX_FOLDER_ID }}
          PUBLIC_BOX_API_URL: ${{ vars.PUBLIC_BOX_API_URL }}
          GDRIVE_JSON_PATH: ${{ vars.GDRIVE_JSON_PATH }}
          ENABLE_QUALITYFOLIO_PREPARE: ${{ vars.ENABLE_QUALITYFOLIO_PREPARE }}
          PUBLIC_QUALITYFOLIO_DB: ${{ vars.PUBLIC_QUALITYFOLIO_DB }}
          PUBLIC_CONTENT_GITHUB_REPO: ${{ vars.PUBLIC_CONTENT_GITHUB_REPO }}
          PUBLIC_CONTENT_GITHUB_OWNER: ${{ vars.PUBLIC_CONTENT_GITHUB_OWNER }}
        run: |
          bash -x support/ci-cd.sh      
          
      - name: Install dependencies
        run: |
          pnpm install

      - name: Generate pagefind HTML files
        run: |
          pnpm run pagefind-search          
          
      - name: Build using pnpm
        run: |
          pnpm run build
          
      - name: Generate qualityfolio DB
        run: |
          pnpm run prepare-qualityfolio-db
          
      - name: Archive build artifacts
        run: |
          tar -czvf expectations-outcomes-hub-theme.tar.gz dist src/content/db/qualityfolio/resource-surveillance.sqlite.db node_modules 
          
      - name: Copy build artifacts to the production server
        run: |
          scp -P22 -o StrictHostKeyChecking=no expectations-outcomes-hub-theme.tar.gz "${{ secrets.PRODUCTION_DEPLOYMENT_USER }}@${{ secrets.PRODUCTION_DEPLOYMENT_SERVER_IP }}:"

      - name: Deploy to production server
        run: |
          ssh -p22 -o StrictHostKeyChecking=no -t ${{ secrets.PRODUCTION_DEPLOYMENT_USER }}@${{ secrets.PRODUCTION_DEPLOYMENT_SERVER_IP }} "bash -i expectations-outcomes-hub-theme.sh"
