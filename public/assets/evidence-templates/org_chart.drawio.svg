<?xml version="1.0" encoding="UTF-8"?>
<!-- Do not edit this file with editors other than draw.io -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="1442px" height="684px" viewBox="-0.5 -0.5 1442 684" content="&lt;mxfile host=&quot;app.diagrams.net&quot; modified=&quot;2023-07-28T06:43:44.564Z&quot; agent=&quot;Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0.0.0 Safari/537.36&quot; etag=&quot;SfAhdOWFtjcZwmdHYpc1&quot; version=&quot;21.6.5&quot; type=&quot;device&quot;&gt;&lt;diagram name=&quot;Page-1&quot; id=&quot;97916047-d0de-89f5-080d-49f4d83e522f&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;" style="background-color: rgb(255, 255, 255);"><defs><linearGradient x1="0%" y1="0%" x2="0%" y2="100%" id="mx-gradient-ffcd28-1-ffa500-1-s-0"><stop offset="0%" style="stop-color: rgb(255, 205, 40); stop-opacity: 1;"/><stop offset="100%" style="stop-color: rgb(255, 165, 0); stop-opacity: 1;"/></linearGradient></defs><g><rect x="610.5" y="72.5" width="200" height="60" rx="9" ry="9" fill="#000000" stroke="none" pointer-events="all" transform="translate(2,3)" opacity="0.25"/><rect x="610.5" y="72.5" width="200" height="60" rx="9" ry="9" fill="#10739e" stroke="none" pointer-events="all"/><g fill="#FFFFFF" font-family="Helvetica" font-weight="bold" text-anchor="middle" font-size="14px"><text x="710" y="108">CEO</text></g><path d="M 550.5 261.5 L 580.5 261.5 L 610.51 262 M 610.49 263 L 580.5 262.5 L 550.5 262.5 M 610.49 263" fill="none" stroke="#23445d" stroke-width="4" stroke-miterlimit="1.42" pointer-events="all"/><rect x="330.5" y="231.5" width="220" height="61" rx="9.15" ry="9.15" fill="#000000" stroke="none" pointer-events="all" transform="translate(2,3)" opacity="0.25"/><rect x="330.5" y="231.5" width="220" height="61" rx="9.15" ry="9.15" fill="#10739e" stroke="none" pointer-events="all"/><g fill="#FFFFFF" font-family="Helvetica" font-weight="bold" text-anchor="middle" font-size="14px"><text x="440" y="267.5">Offshore Head </text></g><rect x="330.5" y="352.5" width="220" height="60" rx="9" ry="9" fill="#000000" stroke="none" pointer-events="all" transform="translate(2,3)" opacity="0.25"/><rect x="330.5" y="352.5" width="220" height="60" rx="9" ry="9" fill="#10739e" stroke="none" pointer-events="all"/><g fill="#FFFFFF" font-family="Helvetica" font-weight="bold" text-anchor="middle" font-size="14px"><text x="440" y="379.5">Administration/</text><text x="440" y="396.5">Finance</text></g><rect x="610.5" y="352.5" width="200" height="60" rx="9" ry="9" fill="#000000" stroke="none" pointer-events="all" transform="translate(2,3)" opacity="0.25"/><rect x="610.5" y="352.5" width="200" height="60" rx="9" ry="9" fill="#10739e" stroke="none" pointer-events="all"/><g fill="#FFFFFF" font-family="Helvetica" font-weight="bold" text-anchor="middle" font-size="14px"><text x="710" y="379.5">Infrastructure </text><text x="710" y="396.5">Manager</text></g><rect x="870.5" y="352.5" width="200" height="60" rx="9" ry="9" fill="#000000" stroke="none" pointer-events="all" transform="translate(2,3)" opacity="0.25"/><rect x="870.5" y="352.5" width="200" height="60" rx="9" ry="9" fill="#10739e" stroke="none" pointer-events="all"/><g fill="#FFFFFF" font-family="Helvetica" font-weight="bold" text-anchor="middle" font-size="14px"><text x="970" y="379.5">Technology /</text><text x="970" y="396.5">Privacy Manager</text></g><rect x="610.5" y="232.5" width="200" height="60" rx="9" ry="9" fill="#000000" stroke="none" pointer-events="all" transform="translate(2,3)" opacity="0.25"/><rect x="610.5" y="232.5" width="200" height="60" rx="9" ry="9" fill="#10739e" stroke="none" pointer-events="all"/><g fill="#FFFFFF" font-family="Helvetica" font-weight="bold" text-anchor="middle" font-size="14px"><text x="710" y="259.5">Implementation </text><text x="710" y="276.5"> Project Manager</text></g><rect x="1120.5" y="352.5" width="200" height="60" rx="9" ry="9" fill="#000000" stroke="none" pointer-events="all" transform="translate(2,3)" opacity="0.25"/><rect x="1120.5" y="352.5" width="200" height="60" rx="9" ry="9" fill="#10739e" stroke="none" pointer-events="all"/><g fill="#FFFFFF" font-family="Helvetica" font-weight="bold" text-anchor="middle" font-size="14px"><text x="1220" y="379.5">QA / Security </text><text x="1220" y="396.5">Manager </text></g><rect x="870.5" y="597.5" width="200" height="60" rx="9" ry="9" fill="#000000" stroke="none" pointer-events="all" transform="translate(2,3)" opacity="0.25"/><rect x="870.5" y="597.5" width="200" height="60" rx="9" ry="9" fill="#10739e" stroke="none" pointer-events="all"/><g fill="#FFFFFF" font-family="Helvetica" font-weight="bold" text-anchor="middle" font-size="14px"><text x="970" y="616">Development </text><text x="970" y="633">Team</text></g><rect x="1122.5" y="596.5" width="210" height="60" rx="9" ry="9" fill="#000000" stroke="none" pointer-events="all" transform="translate(2,3)" opacity="0.25"/><rect x="1122.5" y="596.5" width="210" height="60" rx="9" ry="9" fill="#10739e" stroke="none" pointer-events="all"/><g fill="#FFFFFF" font-family="Helvetica" font-weight="bold" text-anchor="middle" font-size="14px"><text x="1227" y="632">Testing Team</text></g><rect x="870.5" y="478.5" width="200" height="60" rx="9" ry="9" fill="#000000" stroke="none" pointer-events="all" transform="translate(2,3)" opacity="0.25"/><rect x="870.5" y="478.5" width="200" height="60" rx="9" ry="9" fill="#10739e" stroke="none" pointer-events="all"/><g fill="#FFFFFF" font-family="Helvetica" font-weight="bold" text-anchor="middle" font-size="14px"><text x="970" y="514">Technology Lead</text></g><rect x="1122.5" y="478.5" width="189" height="60" rx="9" ry="9" fill="#000000" stroke="none" pointer-events="all" transform="translate(2,3)" opacity="0.25"/><rect x="1122.5" y="478.5" width="189" height="60" rx="9" ry="9" fill="#10739e" stroke="none" pointer-events="all"/><g fill="#FFFFFF" font-family="Helvetica" font-weight="bold" text-anchor="middle" font-size="14px"><text x="1216.5" y="514">QA Lead</text></g><path d="M 610.5 112.5 L 450.5 112.5 Q 440.5 112.5 440.5 122.5 L 440.5 217.03" fill="none" stroke="#23445d" stroke-width="4" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 440.5 227.03 L 435.5 217.03 L 445.5 217.03 Z" fill="#23445d" stroke="#23445d" stroke-width="4" stroke-miterlimit="10" pointer-events="all"/><path d="M 440.5 292.5 L 440.5 313 Q 440.5 323 440.5 330.51 L 440.5 338.03" fill="none" stroke="#23445d" stroke-width="4" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 440.5 348.03 L 435.5 338.03 L 445.5 338.03 Z" fill="#23445d" stroke="#23445d" stroke-width="4" stroke-miterlimit="10" pointer-events="all"/><path d="M 1217.5 412.5 L 1217.5 442.5 Q 1217.5 452.5 1217.39 458.27 L 1217.28 464.03" fill="none" stroke="#23445d" stroke-width="4" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1217.09 474.03 L 1212.28 463.93 L 1222.28 464.13 Z" fill="#23445d" stroke="#23445d" stroke-width="4" stroke-miterlimit="10" pointer-events="all"/><path d="M 1217 538.5 L 1217 560 Q 1217 570 1217 578.26 L 1217 586.53" fill="none" stroke="#23445d" stroke-width="4" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1217 596.53 L 1212 586.53 L 1222 586.53 Z" fill="#23445d" stroke="#23445d" stroke-width="4" stroke-miterlimit="10" pointer-events="all"/><path d="M 710.5 132.5 L 710.5 152.5 Q 710.5 162.5 710.5 172.5 L 710.5 218.03" fill="none" stroke="#23445d" stroke-width="4" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 710.5 228.03 L 705.5 218.03 L 715.5 218.03 Z" fill="#23445d" stroke="#23445d" stroke-width="4" stroke-miterlimit="10" pointer-events="all"/><path d="M 710.5 292.5 L 710.5 312.5 Q 710.5 322.5 710.5 330.26 L 710.5 338.03" fill="none" stroke="#23445d" stroke-width="4" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 710.5 348.03 L 705.5 338.03 L 715.5 338.03 Z" fill="#23445d" stroke="#23445d" stroke-width="4" stroke-miterlimit="10" pointer-events="all"/><rect x="620.5" y="475.5" width="181" height="60" rx="9" ry="9" fill="#000000" stroke="none" pointer-events="all" transform="translate(2,3)" opacity="0.25"/><rect x="620.5" y="475.5" width="181" height="60" rx="9" ry="9" fill="#10739e" stroke="none" pointer-events="all"/><g fill="#FFFFFF" font-family="Helvetica" font-weight="bold" text-anchor="middle" font-size="14px"><text x="710.5" y="502.5">Infrastructure </text><text x="710.5" y="519.5">Manager</text></g><path d="M 710.5 412.5 L 710.5 442.5 Q 710.5 452.5 710.59 456.77 L 710.69 461.03" fill="none" stroke="#23445d" stroke-width="4" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 710.9 471.03 L 705.69 461.14 L 715.68 460.92 Z" fill="#23445d" stroke="#23445d" stroke-width="4" stroke-miterlimit="10" pointer-events="all"/><path d="M 970.5 412.5 L 970.5 436 Q 970.5 446 970.5 455.01 L 970.5 464.03" fill="none" stroke="#23445d" stroke-width="4" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 970.5 474.03 L 965.5 464.03 L 975.5 464.03 Z" fill="#23445d" stroke="#23445d" stroke-width="4" stroke-miterlimit="10" pointer-events="all"/><path d="M 970.5 538.5 L 970.5 559.5 Q 970.5 569.5 970.5 576.26 L 970.5 583.03" fill="none" stroke="#23445d" stroke-width="4" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 970.5 593.03 L 965.5 583.03 L 975.5 583.03 Z" fill="#23445d" stroke="#23445d" stroke-width="4" stroke-miterlimit="10" pointer-events="all"/><path d="M 810.5 262.5 L 960.5 262.5 Q 970.5 262.5 970.5 272.5 L 970.5 338.03" fill="none" stroke="#23445d" stroke-width="4" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 970.5 348.03 L 965.5 338.03 L 975.5 338.03 Z" fill="#23445d" stroke="#23445d" stroke-width="4" stroke-miterlimit="10" pointer-events="all"/><path d="M 810.5 262.5 L 1200.5 262.5 Q 1210.5 262.5 1210.5 272.5 L 1210.5 337.01" fill="none" stroke="#23445d" stroke-width="4" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1210.5 347.01 L 1205.5 337.01 L 1215.5 337.01 Z" fill="#23445d" stroke="#23445d" stroke-width="4" stroke-miterlimit="10" pointer-events="all"/><path d="M 0.5 32.5 L 0.5 2.5 L 1440.5 2.5 L 1440.5 32.5" fill="url(#mx-gradient-ffcd28-1-ffa500-1-s-0)" stroke="#d79b00" stroke-miterlimit="10" pointer-events="all"/><path d="M 0.5 32.5 L 0.5 682.5 L 1440.5 682.5 L 1440.5 32.5" fill="none" stroke="#d79b00" stroke-miterlimit="10" pointer-events="none"/><path d="M 0.5 32.5 L 1440.5 32.5" fill="none" stroke="#d79b00" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1438px; height: 1px; padding-top: 18px; margin-left: 2px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><font style="" size="1"><b style=""><u style="font-size: 30px;">Organizational Chart</u></b></font></div></div></div></foreignObject><text x="721" y="21" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Organizational Chart</text></switch></g><rect x="0.5" y="32.5" width="1440" height="150" fill-opacity="0.4" fill="#ffcc99" stroke="#36393d" stroke-opacity="0.4" stroke-width="2" stroke-dasharray="2 2" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1430px; height: 1px; padding-top: 108px; margin-left: 7px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left; max-height: 146px; overflow: hidden;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><font style="" size="1"><b style="font-size: 20px;">Board Level</b></font></div></div></div></foreignObject><text x="7" y="111" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Board Level</text></switch></g><rect x="0.5" y="182.5" width="1440" height="140" fill-opacity="0.4" fill="#b1ddf0" stroke="#10739e" stroke-opacity="0.4" stroke-width="2" stroke-dasharray="2 2" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1430px; height: 1px; padding-top: 253px; margin-left: 7px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left; max-height: 136px; overflow: hidden;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><font style="" size="1"><b style="font-size: 20px;">Manager Level</b></font></div></div></div></foreignObject><text x="7" y="256" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Manager Level</text></switch></g><rect x="0.5" y="322.5" width="1440" height="120" fill-opacity="0.4" fill="#b1ddf0" stroke="#10739e" stroke-opacity="0.4" stroke-width="2" stroke-dasharray="2 2" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1430px; height: 1px; padding-top: 383px; margin-left: 7px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left; max-height: 116px; overflow: hidden;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><font style="" size="1"><b style="font-size: 20px;">Executive Level</b></font></div></div></div></foreignObject><text x="7" y="386" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Executive Level</text></switch></g><rect x="0.5" y="442.5" width="1440" height="120" fill-opacity="0.4" fill="#b1ddf0" stroke="#10739e" stroke-opacity="0.4" stroke-width="2" stroke-dasharray="2 2" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1430px; height: 1px; padding-top: 503px; margin-left: 7px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left; max-height: 116px; overflow: hidden;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><font style="" size="1"><b style="font-size: 20px;">Executive Level</b></font></div></div></div></foreignObject><text x="7" y="506" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Executive Level</text></switch></g><rect x="0.5" y="562.5" width="1440" height="120" fill-opacity="0.4" fill="#b1ddf0" stroke="#10739e" stroke-opacity="0.4" stroke-width="2" stroke-dasharray="2 2" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1430px; height: 1px; padding-top: 623px; margin-left: 7px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left; max-height: 116px; overflow: hidden;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><font style="" size="1"><b style="font-size: 20px;">Support / Entry Level</b></font></div></div></div></foreignObject><text x="7" y="626" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Support / Entry Level</text></switch></g></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.drawio.com/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Text is not SVG - cannot display</text></a></switch></svg>