<?xml version="1.0" encoding="UTF-8"?>
<!-- Do not edit this file with editors other than draw.io -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="2351px" height="1105px" viewBox="-0.5 -0.5 2351 1105" content="&lt;mxfile host=&quot;app.diagrams.net&quot; modified=&quot;2023-07-28T10:08:15.532Z&quot; agent=&quot;Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0.0.0 Safari/537.36&quot; etag=&quot;62rQyMOvAes2hD3TPVa4&quot; version=&quot;21.6.5&quot; type=&quot;device&quot;&gt;&lt;diagram name=&quot;Page-1&quot; id=&quot;64c3da0e-402f-94eb-ee01-a36477274f13&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;" style="background-color: rgb(255, 255, 255);"><defs/><g><rect x="958" y="189" width="340" height="60" rx="9" ry="9" fill="#000000" stroke="none" pointer-events="all" transform="translate(2,3)" opacity="0.25"/><rect x="958" y="189" width="340" height="60" rx="9" ry="9" fill="#10739e" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 338px; height: 1px; padding-top: 219px; margin-left: 959px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 24px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">CEO</div></div></div></foreignObject><text x="1128" y="226" fill="#FFFFFF" font-family="Helvetica" font-size="24px" text-anchor="middle" font-weight="bold">CEO</text></switch></g><rect x="18" y="318.5" width="140" height="60" rx="9" ry="9" fill="#000000" stroke="none" pointer-events="all" transform="translate(2,3)" opacity="0.25"/><rect x="18" y="318.5" width="140" height="60" rx="9" ry="9" fill="#f2931e" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 348px; margin-left: 19px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">Project Manager </div></div></div></foreignObject><text x="88" y="353" fill="#FFFFFF" font-family="Helvetica" font-size="14px" text-anchor="middle" font-weight="bold">Project Manager </text></switch></g><rect x="2008" y="319" width="140" height="60" rx="9" ry="9" fill="#000000" stroke="#000000" pointer-events="all" transform="translate(2,3)" opacity="0.25"/><rect x="2008" y="319" width="140" height="60" rx="9" ry="9" fill="#6d8764" stroke="#3a5431" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 349px; margin-left: 2009px;"><div data-drawio-colors="color: #ffffff; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">Administration and Accounts Officer</div></div></div></foreignObject><text x="2078" y="353" fill="#ffffff" font-family="Helvetica" font-size="14px" text-anchor="middle" font-weight="bold">Administration and A...</text></switch></g><rect x="858" y="319" width="140" height="60" rx="9" ry="9" fill="#000000" stroke="#000000" pointer-events="all" transform="translate(2,3)" opacity="0.25"/><rect x="858" y="319" width="140" height="60" rx="9" ry="9" fill="#60a917" stroke="#2d7600" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 349px; margin-left: 859px;"><div data-drawio-colors="color: #ffffff; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">Consultants</div></div></div></foreignObject><text x="928" y="353" fill="#ffffff" font-family="Helvetica" font-size="14px" text-anchor="middle" font-weight="bold">Consultants</text></switch></g><rect x="1828" y="319" width="140" height="60" rx="9" ry="9" fill="#000000" stroke="#000000" pointer-events="all" transform="translate(2,3)" opacity="0.25"/><rect x="1828" y="319" width="140" height="60" rx="9" ry="9" fill="#76608a" stroke="#432d57" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 349px; margin-left: 1829px;"><div data-drawio-colors="color: #ffffff; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">Human Resources</div></div></div></foreignObject><text x="1898" y="353" fill="#ffffff" font-family="Helvetica" font-size="14px" text-anchor="middle" font-weight="bold">Human Resources</text></switch></g><rect x="107" y="428.5" width="120" height="60" rx="9" ry="9" fill="#000000" stroke="none" pointer-events="all" transform="translate(2,3)" opacity="0.25"/><rect x="107" y="428.5" width="120" height="60" rx="9" ry="9" fill="#12aab5" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 458px; margin-left: 108px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Team Lead</div></div></div></foreignObject><text x="167" y="462" fill="#FFFFFF" font-family="Helvetica" font-size="12px" text-anchor="middle">Team Lead</text></switch></g><rect x="697" y="429.5" width="120" height="60" rx="9" ry="9" fill="#000000" stroke="none" pointer-events="all" transform="translate(2,3)" opacity="0.25"/><rect x="697" y="429.5" width="120" height="60" rx="9" ry="9" fill="#12aab5" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 460px; margin-left: 698px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Team Lead</div></div></div></foreignObject><text x="757" y="463" fill="#FFFFFF" font-family="Helvetica" font-size="12px" text-anchor="middle">Team Lead</text></switch></g><rect x="227" y="522.5" width="120" height="60" rx="9" ry="9" fill="#000000" stroke="none" pointer-events="all" transform="translate(2,3)" opacity="0.25"/><rect x="227" y="522.5" width="120" height="60" rx="9" ry="9" fill="#999999" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 553px; margin-left: 228px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Developer 1</div></div></div></foreignObject><text x="287" y="556" fill="#FFFFFF" font-family="Helvetica" font-size="12px" text-anchor="middle">Developer 1</text></switch></g><rect x="227" y="834" width="120" height="60" rx="9" ry="9" fill="#000000" stroke="none" pointer-events="all" transform="translate(2,3)" opacity="0.25"/><rect x="227" y="834" width="120" height="60" rx="9" ry="9" fill="#999999" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 864px; margin-left: 228px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Developer 1</div></div></div></foreignObject><text x="287" y="868" fill="#FFFFFF" font-family="Helvetica" font-size="12px" text-anchor="middle">Developer 1</text></switch></g><path d="M 88 378.5 L 88 453 L 107 453 L 107 458.5" fill="none" stroke="#23445d" stroke-width="8" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1132.08 249.12 L 1132.08 283 L 757 283 L 757 429.5" fill="none" stroke="#23445d" stroke-width="8" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 167 489.5 L 167 552.5 L 227 552.5" fill="none" stroke="#23445d" stroke-width="8" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 167 489.5 L 167 696.48 L 229.04 696.48" fill="none" stroke="#23445d" stroke-width="8" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 167 799 L 167 1012.61 L 228.08 1012.61" fill="none" stroke="#23445d" stroke-width="8" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 167 799 L 167 867.6 L 226.88 867.6" fill="none" stroke="#23445d" stroke-width="8" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 88 378.5 L 88 768 L 107 768" fill="none" stroke="#23445d" stroke-width="8" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1131.74 247.62 L 1131.74 283 L 84.08 283 L 84.08 320.84" fill="none" stroke="#23445d" stroke-width="8" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1131.4 249.3 L 1131.4 284 L 928 284 L 928 319" fill="none" stroke="#23445d" stroke-width="8" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1132.08 246.78 L 1132.08 284 L 1132 319.5" fill="none" stroke="#23445d" stroke-width="8" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1130.72 247.98 L 1130.72 284 L 1898 284 L 1898 319" fill="none" stroke="#23445d" stroke-width="8" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1132.42 246.78 L 1132.42 284 L 2084.86 284 L 2084.86 320.38" fill="none" stroke="#23445d" stroke-width="8" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1132.42 248.4 L 1132.42 283 L 2258 283 L 2258 319" fill="none" stroke="#23445d" stroke-width="8" stroke-miterlimit="10" pointer-events="stroke"/><rect x="2188" y="319" width="140" height="60" rx="9" ry="9" fill="#000000" stroke="#000000" pointer-events="all" transform="translate(2,3)" opacity="0.25"/><rect x="2188" y="319" width="140" height="60" rx="9" ry="9" fill="#a0522d" stroke="#6d1f00" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 349px; margin-left: 2189px;"><div data-drawio-colors="color: #ffffff; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">Administration Assistants</div></div></div></foreignObject><text x="2258" y="353" fill="#ffffff" font-family="Helvetica" font-size="14px" text-anchor="middle" font-weight="bold">Administration Assis...</text></switch></g><rect x="107" y="738" width="120" height="60" rx="9" ry="9" fill="#000000" stroke="none" pointer-events="all" transform="translate(2,3)" opacity="0.25"/><rect x="107" y="738" width="120" height="60" rx="9" ry="9" fill="#12aab5" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 768px; margin-left: 108px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Team Lead</div></div></div></foreignObject><text x="167" y="772" fill="#FFFFFF" font-family="Helvetica" font-size="12px" text-anchor="middle">Team Lead</text></switch></g><rect x="817" y="522.5" width="120" height="60" rx="9" ry="9" fill="#000000" stroke="none" pointer-events="all" transform="translate(2,3)" opacity="0.25"/><rect x="817" y="522.5" width="120" height="60" rx="9" ry="9" fill="#999999" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 553px; margin-left: 818px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Developer 1</div></div></div></foreignObject><text x="877" y="556" fill="#FFFFFF" font-family="Helvetica" font-size="12px" text-anchor="middle">Developer 1</text></switch></g><path d="M 757 489.5 L 757 552.5 L 817 552.5" fill="none" stroke="#23445d" stroke-width="8" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 757 489.5 L 757 694.74 L 814.36 694.74" fill="none" stroke="#23445d" stroke-width="8" stroke-miterlimit="10" pointer-events="stroke"/><rect x="348" y="319" width="140" height="60" rx="9" ry="9" fill="#000000" stroke="none" pointer-events="all" transform="translate(2,3)" opacity="0.25"/><rect x="348" y="319" width="140" height="60" rx="9" ry="9" fill="#f2931e" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 349px; margin-left: 349px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">Project Manager </div></div></div></foreignObject><text x="418" y="353" fill="#FFFFFF" font-family="Helvetica" font-size="14px" text-anchor="middle" font-weight="bold">Project Manager </text></switch></g><rect x="447" y="429.5" width="120" height="60" rx="9" ry="9" fill="#000000" stroke="none" pointer-events="all" transform="translate(2,3)" opacity="0.25"/><rect x="447" y="429.5" width="120" height="60" rx="9" ry="9" fill="#12aab5" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 460px; margin-left: 448px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Team Lead</div></div></div></foreignObject><text x="507" y="463" fill="#FFFFFF" font-family="Helvetica" font-size="12px" text-anchor="middle">Team Lead</text></switch></g><rect x="567" y="522.5" width="120" height="60" rx="9" ry="9" fill="#000000" stroke="none" pointer-events="all" transform="translate(2,3)" opacity="0.25"/><rect x="567" y="522.5" width="120" height="60" rx="9" ry="9" fill="#999999" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 553px; margin-left: 568px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Developer 1</div></div></div></foreignObject><text x="627" y="556" fill="#FFFFFF" font-family="Helvetica" font-size="12px" text-anchor="middle">Developer 1</text></switch></g><rect x="577" y="829.5" width="120" height="69" rx="10.35" ry="10.35" fill="#000000" stroke="none" pointer-events="all" transform="translate(2,3)" opacity="0.25"/><rect x="577" y="829.5" width="120" height="69" rx="10.35" ry="10.35" fill="#999999" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 864px; margin-left: 578px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Developer 1</div></div></div></foreignObject><text x="637" y="868" fill="#FFFFFF" font-family="Helvetica" font-size="12px" text-anchor="middle">Developer 1</text></switch></g><path d="M 418 379 L 418 463 L 447 463 L 447 459.5" fill="none" stroke="#23445d" stroke-width="8" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 507 489.5 L 507 552.5 L 567 552.5" fill="none" stroke="#23445d" stroke-width="8" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 507 489.5 L 507 695.64 L 567.12 695.64" fill="none" stroke="#23445d" stroke-width="8" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 507 799.5 L 507 1010.12 L 577.36 1010.12" fill="none" stroke="#23445d" stroke-width="8" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 507 799.5 L 507 862.83 L 577.6 862.83" fill="none" stroke="#23445d" stroke-width="8" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 418 379 L 418 769.5 L 447 769.5" fill="none" stroke="#23445d" stroke-width="8" stroke-miterlimit="10" pointer-events="stroke"/><rect x="447" y="739.5" width="120" height="60" rx="9" ry="9" fill="#000000" stroke="none" pointer-events="all" transform="translate(2,3)" opacity="0.25"/><rect x="447" y="739.5" width="120" height="60" rx="9" ry="9" fill="#12aab5" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 770px; margin-left: 448px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Team Lead</div></div></div></foreignObject><text x="507" y="773" fill="#FFFFFF" font-family="Helvetica" font-size="12px" text-anchor="middle">Team Lead</text></switch></g><path d="M 1132.42 246.78 L 1132.42 284 L 418 284 L 418 319" fill="none" stroke="#23445d" stroke-width="8" stroke-miterlimit="10" pointer-events="stroke"/><rect x="1062" y="319.5" width="140" height="60" rx="9" ry="9" fill="#000000" stroke="none" pointer-events="all" transform="translate(2,3)" opacity="0.25"/><rect x="1062" y="319.5" width="140" height="60" rx="9" ry="9" fill="#f2931e" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 350px; margin-left: 1063px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">Creative Manager</div></div></div></foreignObject><text x="1132" y="354" fill="#FFFFFF" font-family="Helvetica" font-size="14px" text-anchor="middle" font-weight="bold">Creative Manager</text></switch></g><rect x="1072" y="429.5" width="120" height="60" rx="9" ry="9" fill="#000000" stroke="none" pointer-events="all" transform="translate(2,3)" opacity="0.25"/><rect x="1072" y="429.5" width="120" height="60" rx="9" ry="9" fill="#12aab5" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 460px; margin-left: 1073px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Team Lead</div></div></div></foreignObject><text x="1132" y="463" fill="#FFFFFF" font-family="Helvetica" font-size="12px" text-anchor="middle">Team Lead</text></switch></g><rect x="1192" y="522.5" width="120" height="60" rx="9" ry="9" fill="#000000" stroke="none" pointer-events="all" transform="translate(2,3)" opacity="0.25"/><rect x="1192" y="522.5" width="120" height="60" rx="9" ry="9" fill="#999999" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 553px; margin-left: 1193px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Developer 1</div></div></div></foreignObject><text x="1252" y="556" fill="#FFFFFF" font-family="Helvetica" font-size="12px" text-anchor="middle">Developer 1</text></switch></g><rect x="1192" y="809" width="120" height="69" rx="10.35" ry="10.35" fill="#000000" stroke="none" pointer-events="all" transform="translate(2,3)" opacity="0.25"/><rect x="1192" y="809" width="120" height="69" rx="10.35" ry="10.35" fill="#999999" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 843px; margin-left: 1193px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Web and Graphics Designer</div></div></div></foreignObject><text x="1252" y="847" fill="#FFFFFF" font-family="Helvetica" font-size="12px" text-anchor="middle">Web and Graphics Des...</text></switch></g><path d="M 1131.16 382.38 L 1131.16 406.5 L 1132 429.5" fill="none" stroke="#23445d" stroke-width="8" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1132 489.5 L 1132 552.5 L 1192 552.5" fill="none" stroke="#23445d" stroke-width="8" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1132 489.5 L 1132 693 L 1200 693" fill="none" stroke="#23445d" stroke-width="8" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1132 489.5 L 1132 1014.33 L 1200.84 1014.33" fill="none" stroke="#23445d" stroke-width="8" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1132 489.5 L 1132 843.5 L 1192 843.5" fill="none" stroke="#23445d" stroke-width="8" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1132 489.5 L 1132 609 L 1132 729" fill="none" stroke="#23445d" stroke-width="8" stroke-miterlimit="10" pointer-events="stroke"/><rect x="1358" y="429.5" width="120" height="60" rx="9" ry="9" fill="#000000" stroke="none" pointer-events="all" transform="translate(2,3)" opacity="0.25"/><rect x="1358" y="429.5" width="120" height="60" rx="9" ry="9" fill="#12aab5" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 460px; margin-left: 1359px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Test Lead</div></div></div></foreignObject><text x="1418" y="463" fill="#FFFFFF" font-family="Helvetica" font-size="12px" text-anchor="middle">Test Lead</text></switch></g><rect x="1478" y="522" width="120" height="60" rx="9" ry="9" fill="#000000" stroke="none" pointer-events="all" transform="translate(2,3)" opacity="0.25"/><rect x="1478" y="522" width="120" height="60" rx="9" ry="9" fill="#999999" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 552px; margin-left: 1479px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Test Engineer 1</div></div></div></foreignObject><text x="1538" y="556" fill="#FFFFFF" font-family="Helvetica" font-size="12px" text-anchor="middle">Test Engineer 1</text></switch></g><path d="M 1418 489.5 L 1418 552 L 1478 552" fill="none" stroke="#23445d" stroke-width="8" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1418 489.5 L 1418 693 L 1478 693" fill="none" stroke="#23445d" stroke-width="8" stroke-miterlimit="10" pointer-events="stroke"/><rect x="1628" y="429.5" width="120" height="60" rx="9" ry="9" fill="#000000" stroke="none" pointer-events="all" transform="translate(2,3)" opacity="0.25"/><rect x="1628" y="429.5" width="120" height="60" rx="9" ry="9" fill="#12aab5" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 460px; margin-left: 1629px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Systems Team Lead</div></div></div></foreignObject><text x="1688" y="463" fill="#FFFFFF" font-family="Helvetica" font-size="12px" text-anchor="middle">Systems Team Lead</text></switch></g><rect x="1748" y="522" width="120" height="60" rx="9" ry="9" fill="#000000" stroke="none" pointer-events="all" transform="translate(2,3)" opacity="0.25"/><rect x="1748" y="522" width="120" height="60" rx="9" ry="9" fill="#999999" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 552px; margin-left: 1749px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Systems Engineer 1</div></div></div></foreignObject><text x="1808" y="556" fill="#FFFFFF" font-family="Helvetica" font-size="12px" text-anchor="middle">Systems Engineer 1</text></switch></g><path d="M 1688 489.5 L 1688 552 L 1748 552" fill="none" stroke="#23445d" stroke-width="8" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1688 489.5 L 1688 693 L 1754 693" fill="none" stroke="#23445d" stroke-width="8" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1131.06 248.16 L 1131.06 283 L 1418 283 L 1418 429.5" fill="none" stroke="#23445d" stroke-width="8" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1132.08 249.3 L 1132.08 283 L 1688 283 L 1688 429.5" fill="none" stroke="#23445d" stroke-width="8" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1132.08 247.44 L 1132.08 283 L 757 283 L 757 429.5" fill="none" stroke="#23445d" stroke-width="8" stroke-miterlimit="10" pointer-events="stroke"/><ellipse cx="1038" cy="833" rx="3" ry="3" fill="rgb(0, 0, 0)" stroke="none" pointer-events="all"/><rect x="1028" y="823" width="20" height="20" fill="none" stroke="none" pointer-events="all"/><ellipse cx="287" cy="910.5" rx="3" ry="3" fill="rgb(0, 0, 0)" stroke="none" pointer-events="all"/><rect x="277" y="900.5" width="20" height="20" fill="none" stroke="none" pointer-events="all"/><ellipse cx="287" cy="939.5" rx="3" ry="3" fill="rgb(0, 0, 0)" stroke="none" pointer-events="all"/><rect x="277" y="929.5" width="20" height="20" fill="none" stroke="none" pointer-events="all"/><ellipse cx="287" cy="924.5" rx="3" ry="3" fill="rgb(0, 0, 0)" stroke="none" pointer-events="all"/><rect x="277" y="914.5" width="20" height="20" fill="none" stroke="none" pointer-events="all"/><ellipse cx="287" cy="614.5" rx="3" ry="3" fill="rgb(0, 0, 0)" stroke="none" pointer-events="all"/><rect x="277" y="604.5" width="20" height="20" fill="none" stroke="none" pointer-events="all"/><ellipse cx="287" cy="600.5" rx="3" ry="3" fill="rgb(0, 0, 0)" stroke="none" pointer-events="all"/><rect x="277" y="590.5" width="20" height="20" fill="none" stroke="none" pointer-events="all"/><ellipse cx="287" cy="629.5" rx="3" ry="3" fill="rgb(0, 0, 0)" stroke="none" pointer-events="all"/><rect x="277" y="619.5" width="20" height="20" fill="none" stroke="none" pointer-events="all"/><ellipse cx="637" cy="924" rx="3" ry="3" fill="rgb(0, 0, 0)" stroke="none" pointer-events="all"/><rect x="627" y="914" width="20" height="20" fill="none" stroke="none" pointer-events="all"/><ellipse cx="637" cy="910" rx="3" ry="3" fill="rgb(0, 0, 0)" stroke="none" pointer-events="all"/><rect x="627" y="900" width="20" height="20" fill="none" stroke="none" pointer-events="all"/><ellipse cx="637" cy="939" rx="3" ry="3" fill="rgb(0, 0, 0)" stroke="none" pointer-events="all"/><rect x="627" y="929" width="20" height="20" fill="none" stroke="none" pointer-events="all"/><ellipse cx="877" cy="614" rx="3" ry="3" fill="rgb(0, 0, 0)" stroke="none" pointer-events="all"/><rect x="867" y="604" width="20" height="20" fill="none" stroke="none" pointer-events="all"/><ellipse cx="877" cy="600" rx="3" ry="3" fill="rgb(0, 0, 0)" stroke="none" pointer-events="all"/><rect x="867" y="590" width="20" height="20" fill="none" stroke="none" pointer-events="all"/><ellipse cx="877" cy="629" rx="3" ry="3" fill="rgb(0, 0, 0)" stroke="none" pointer-events="all"/><rect x="867" y="619" width="20" height="20" fill="none" stroke="none" pointer-events="all"/><ellipse cx="1252" cy="613.5" rx="3" ry="3" fill="rgb(0, 0, 0)" stroke="none" pointer-events="all"/><rect x="1242" y="603.5" width="20" height="20" fill="none" stroke="none" pointer-events="all"/><ellipse cx="1252" cy="599.5" rx="3" ry="3" fill="rgb(0, 0, 0)" stroke="none" pointer-events="all"/><rect x="1242" y="589.5" width="20" height="20" fill="none" stroke="none" pointer-events="all"/><ellipse cx="1252" cy="628.5" rx="3" ry="3" fill="rgb(0, 0, 0)" stroke="none" pointer-events="all"/><rect x="1242" y="618.5" width="20" height="20" fill="none" stroke="none" pointer-events="all"/><ellipse cx="1538" cy="613" rx="3" ry="3" fill="rgb(0, 0, 0)" stroke="none" pointer-events="all"/><rect x="1528" y="603" width="20" height="20" fill="none" stroke="none" pointer-events="all"/><ellipse cx="1538" cy="599" rx="3" ry="3" fill="rgb(0, 0, 0)" stroke="none" pointer-events="all"/><rect x="1528" y="589" width="20" height="20" fill="none" stroke="none" pointer-events="all"/><ellipse cx="1538" cy="628" rx="3" ry="3" fill="rgb(0, 0, 0)" stroke="none" pointer-events="all"/><rect x="1528" y="618" width="20" height="20" fill="none" stroke="none" pointer-events="all"/><ellipse cx="1818" cy="614" rx="3" ry="3" fill="rgb(0, 0, 0)" stroke="none" pointer-events="all"/><rect x="1808" y="604" width="20" height="20" fill="none" stroke="none" pointer-events="all"/><ellipse cx="1818" cy="600" rx="3" ry="3" fill="rgb(0, 0, 0)" stroke="none" pointer-events="all"/><rect x="1808" y="590" width="20" height="20" fill="none" stroke="none" pointer-events="all"/><ellipse cx="1818" cy="629" rx="3" ry="3" fill="rgb(0, 0, 0)" stroke="none" pointer-events="all"/><rect x="1808" y="619" width="20" height="20" fill="none" stroke="none" pointer-events="all"/><ellipse cx="1252" cy="918" rx="3" ry="3" fill="rgb(0, 0, 0)" stroke="none" pointer-events="all"/><rect x="1242" y="908" width="20" height="20" fill="none" stroke="none" pointer-events="all"/><ellipse cx="1252" cy="904" rx="3" ry="3" fill="rgb(0, 0, 0)" stroke="none" pointer-events="all"/><rect x="1242" y="894" width="20" height="20" fill="none" stroke="none" pointer-events="all"/><ellipse cx="1252" cy="933" rx="3" ry="3" fill="rgb(0, 0, 0)" stroke="none" pointer-events="all"/><rect x="1242" y="923" width="20" height="20" fill="none" stroke="none" pointer-events="all"/><path d="M 0 26 L 0 3 L 2350 3 L 2350 26" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 0 26 L 0 1103 L 2350 1103 L 2350 26" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/><path d="M 0 26 L 2350 26" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 2348px; height: 1px; padding-top: 15px; margin-left: 1px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; font-weight: bold; white-space: normal; overflow-wrap: normal;"><font style="font-size: 25px;">Netspective Communications LLC.   -   General Organizational Structure</font></div></div></div></foreignObject><text x="1175" y="18" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle" font-weight="bold">Netspective Communications LLC.   -   General Organizational Structure</text></switch></g><image x="9.5" y="32.5" width="252" height="84" xlink:href="data:image/png;base64,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" preserveAspectRatio="none" pointer-events="none"/><ellipse cx="627" cy="614.5" rx="3" ry="3" fill="rgb(0, 0, 0)" stroke="none" pointer-events="none"/><ellipse cx="627" cy="600.5" rx="3" ry="3" fill="rgb(0, 0, 0)" stroke="none" pointer-events="none"/><ellipse cx="627" cy="629.5" rx="3" ry="3" fill="rgb(0, 0, 0)" stroke="none" pointer-events="none"/><rect x="227" y="663" width="120" height="60" rx="9" ry="9" fill="#000000" stroke="none" pointer-events="none" transform="translate(2,3)" opacity="0.25"/><rect x="227" y="663" width="120" height="60" rx="9" ry="9" fill="#999999" stroke="none" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 693px; margin-left: 228px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">Developer n</div></div></div></foreignObject><text x="287" y="697" fill="#FFFFFF" font-family="Helvetica" font-size="12px" text-anchor="middle">Developer n</text></switch></g><rect x="567" y="663" width="120" height="60" rx="9" ry="9" fill="#000000" stroke="none" pointer-events="none" transform="translate(2,3)" opacity="0.25"/><rect x="567" y="663" width="120" height="60" rx="9" ry="9" fill="#999999" stroke="none" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 693px; margin-left: 568px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">Developer n</div></div></div></foreignObject><text x="627" y="697" fill="#FFFFFF" font-family="Helvetica" font-size="12px" text-anchor="middle">Developer n</text></switch></g><rect x="817" y="663" width="120" height="60" rx="9" ry="9" fill="#000000" stroke="none" pointer-events="none" transform="translate(2,3)" opacity="0.25"/><rect x="817" y="663" width="120" height="60" rx="9" ry="9" fill="#999999" stroke="none" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 693px; margin-left: 818px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">Developer n</div></div></div></foreignObject><text x="877" y="697" fill="#FFFFFF" font-family="Helvetica" font-size="12px" text-anchor="middle">Developer n</text></switch></g><rect x="1200" y="663" width="120" height="60" rx="9" ry="9" fill="#000000" stroke="none" pointer-events="none" transform="translate(2,3)" opacity="0.25"/><rect x="1200" y="663" width="120" height="60" rx="9" ry="9" fill="#999999" stroke="none" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 693px; margin-left: 1201px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">Developer n</div></div></div></foreignObject><text x="1260" y="697" fill="#FFFFFF" font-family="Helvetica" font-size="12px" text-anchor="middle">Developer n</text></switch></g><rect x="1478" y="663" width="120" height="60" rx="9" ry="9" fill="#000000" stroke="none" pointer-events="none" transform="translate(2,3)" opacity="0.25"/><rect x="1478" y="663" width="120" height="60" rx="9" ry="9" fill="#999999" stroke="none" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 693px; margin-left: 1479px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">Test Engineer n</div></div></div></foreignObject><text x="1538" y="697" fill="#FFFFFF" font-family="Helvetica" font-size="12px" text-anchor="middle">Test Engineer n</text></switch></g><rect x="1754" y="663" width="120" height="60" rx="9" ry="9" fill="#000000" stroke="none" pointer-events="none" transform="translate(2,3)" opacity="0.25"/><rect x="1754" y="663" width="120" height="60" rx="9" ry="9" fill="#999999" stroke="none" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 693px; margin-left: 1755px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">Systems Engineer n</div></div></div></foreignObject><text x="1814" y="697" fill="#FFFFFF" font-family="Helvetica" font-size="12px" text-anchor="middle">Systems Engineer n</text></switch></g><rect x="577" y="973" width="120" height="69" rx="10.35" ry="10.35" fill="#000000" stroke="none" pointer-events="none" transform="translate(2,3)" opacity="0.25"/><rect x="577" y="973" width="120" height="69" rx="10.35" ry="10.35" fill="#999999" stroke="none" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 1008px; margin-left: 578px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">Developer n</div></div></div></foreignObject><text x="637" y="1011" fill="#FFFFFF" font-family="Helvetica" font-size="12px" text-anchor="middle">Developer n</text></switch></g><rect x="227" y="973" width="120" height="69" rx="10.35" ry="10.35" fill="#000000" stroke="none" pointer-events="none" transform="translate(2,3)" opacity="0.25"/><rect x="227" y="973" width="120" height="69" rx="10.35" ry="10.35" fill="#999999" stroke="none" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 1008px; margin-left: 228px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">Developer n</div></div></div></foreignObject><text x="287" y="1011" fill="#FFFFFF" font-family="Helvetica" font-size="12px" text-anchor="middle">Developer n</text></switch></g><rect x="1200" y="973" width="120" height="69" rx="10.35" ry="10.35" fill="#000000" stroke="none" pointer-events="none" transform="translate(2,3)" opacity="0.25"/><rect x="1200" y="973" width="120" height="69" rx="10.35" ry="10.35" fill="#999999" stroke="none" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 1008px; margin-left: 1201px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">Web and Graphics Designer</div></div></div></foreignObject><text x="1260" y="1011" fill="#FFFFFF" font-family="Helvetica" font-size="12px" text-anchor="middle">Web and Graphics Des...</text></switch></g></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.drawio.com/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Text is not SVG - cannot display</text></a></switch></svg>