html, body {
  font-size: inherit !important;
}
h2 {
  font-size: inherit !important;
  color: inherit !important;

  font-size: inherit !important;
  font-style: inherit !important;
  line-height: inherit !important;
  letter-spacing: inherit !important;
}
main a {
  color: #028db7;
  text-decoration: none;
}
main a:hover {
  color: #000!important;
  text-decoration: none;
}
.policy_cmp {
  list-style: none !important;
}

.policy_cmp summary {
  list-style: none !important;
}

.layout {
  display: grid;
  grid-template-columns: 1fr 3fr 1fr; /* Sidebar, main content, right sidebar */
  gap: 2rem;
  margin: 2rem;
}
.sidebar {
  position: sticky;
  top: 0;
  /* background-color: #f4f4f4; */
  padding: 1rem;
  /* border-right: 1px solid #ccc; */
}
.right-sidebar {
  background-color: #f4f4f4;
  padding: 1rem;
  border-left: 1px solid #ccc;
}
.main-content {
  padding: 0 1rem;
  border-left: 1px solid #ccc;
  border-right: 1px solid #ccc;
  /* background-color: #fff; */
  /* box-shadow: 0 0 10px rgba(0, 0, 0, 0.1); */
}
.post-content {
  max-width: 800px;
  margin: 0 auto;
}

.sidebar-left-menu svg.arrow-ico {
    width: 30px;
    height: 30px;
    color: #606770;
}

details[open] > .fst_mn .caret {
  transform: rotate(90deg);
}

.sidebar-pane {
  z-index: var(--sl-z-index-menu);
  inset-block: 0;
  inset-inline-start: 0;
  padding-top: var(--sl-nav-height);
  width: 100%;
  background-color: var(--sl-color-black);
}

.sidebar {
  height: 100%;
  padding: 1rem var(--sl-sidebar-pad-x);
  flex-direction: column;
  gap: 1rem;
}

.sidebar-header {
  padding: 20px 0;
  text-align: center;
  border-bottom: 1px solid #eee;
}

.sidebar-header h1 {
  font-size: 20px;
  font-weight: bold;
}

.sidebar-links {
  list-style: none;
  padding: 10px 0;
}

.sidebar-links a {
  color: #000;
  text-decoration: none;
  font-size: 16px;
  padding: 10px 15px;
}

.sidebar-links a:hover {
  background-color: #eee;
}

.sidebar-links a.active {
  background-color: #000;
  color: #fff;
}

.summary {
  display: flex !important;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  user-select: none;
}

.policy_cmp {
  list-style: none !important;
}

.policy_cmp summary {
  list-style: none !important;
}

.caret {
  transition: transform 0.2s ease-in-out;
}

.sidebar-left-menu details summary {
  padding: 5px 0px 5px 10px;
  line-height: 20px;
}

.sidebar-left-menu details[open] summary {
  background: #f2f2f2 !important;
  color: #028db7 !important;
  border-radius: 4px;
}
.sidebar-left-menu .sub-folder ul.level-2 details summary {
  background: transparent !important;
}
.sidebar-left-menu .sub-folder ul.level-2 details[open] summary {
  background: #f2f2f2 !important;
}

.inner-menu-select {
  background: #f2f2f2 !important;
  color: #028db7 !important;
  border-radius: 4px;
}

.sidebar-left-menu li {
  margin-top: 0px !important;
}

.sidebar-left-menu ul ul li a {
  padding: 5px 10px 5px 20px;
  display: block;
}

.sidebar-left-menu svg.arrow-ico {
  width: 30px;
  height: 30px;
  color: #606770;
}

.sidebar-left-menu details summary:hover,
.sidebar-left-menu details[open] summary:hover,
.sidebar-left-menu ul ul li a:hover {
  background: #f2f2f2 !important;
  border-radius: 4px !important;
}

.c-breadcrumbs__link{
      font-size: 14px!important;
}
.shadow-xs {box-shadow: 0px 0px 5px 0px rgba(227, 227, 227, 1);}

  /* Add any custom styles you need here */
/* / pagefind ui css starts / */
.dark {
  --pagefind-ui-primary: #eeeeee;
  --pagefind-ui-text: #eeeeee;
  --pagefind-ui-background: #152028;
  --pagefind-ui-border: #152028;
  --pagefind-ui-tag: #152028;
}

.pagefind-ui__results-area {
  margin-top: 0px !important;
}
.pagefind-ui__result {
  padding-left: 15px !important;
  padding-right: 15px !important;
  padding-top: 0px !important;
  padding-bottom: 10px !important;
}
.pagefind-ui__result-link {
  font-size: 14px !important;
}
.pagefind-ui__result-excerpt {
  line-height: 19px !important;
}

.pagefind-ui__message {
  font-weight: 400 !important;
  font-size: 14px !important;
  padding-top: 12px !important;
  padding-bottom: 8px !important;
  padding-left: 15px !important;
}

.pagefind-ui__search-input {
  border-radius: 1.5rem !important;
  border: 1px solid #ccc !important;
}

.pagefind-ui__search-clear {
  border-radius: 1.5rem !important;
}

.pagefind-ui__drawer {
  position: absolute;
  /* / width: 100%; / */
  top: 100%;
  /* / left: 0; / */
  width: 650px; /*try70 vh*/
  right: 0%;
  z-index: 10;
  background-color: #fff;
  border: 1px solid #ccc;
  border-radius: 0 0 8px 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  max-height: 300px;
  overflow-y: auto;
}

.pagefind-ui__results {
  list-style: none;
  padding: 0;
  margin: 0;
}

.pagefind-ui__result {
  padding: 8px;
  border-bottom: 1px solid #ccc;
}

.pagefind-ui__result:last-child {
  border-bottom: none;
}
.pagefind-ui__message {
  padding-left: 10px !important;
}

.body-noscroll {
  overflow: hidden;
}

.c-breadcrumbs__link[aria-disabled="true"] {
  pointer-events: none;
  cursor: default;
}


/* / Pagefind ui css ends  / */

.dark main a:hover, .dark a.menu-link:hover  {
  color: #fefbfb!important;
}
  /* :::::: markdown css ::::::> starts */
/* .markdown-content.dark-mode, .markdown-content.dark-mode a, .markdown-content.dark-mode .breadcrumb a, .markdown-content.dark-mode h1, .markdown-content.dark-mode h2, .markdown-content.dark-mode h4, .markdown-content.dark-mode ul li::marker, .markdown-content.dark-mode details[open] summary   {
 color: #d1d5db!important;
} */

.dark .markdown-content, .dark .markdown-content a, .dark .markdown-content p, .dark .markdown-content .breadcrumb a, .dark .markdown-content h1, .dark .markdown-content h2, .dark .markdown-content h3, .dark .markdown-content h4, .dark .markdown-content ul li::marker, .dark .markdown-content details[open] summary, .dark .markdown-content b, .dark .markdown-content strong, .dark .markdown-content blockquote, .dark .markdown-content code   {
  color: #d1d5db!important;
 }
 .dark .markdown-content hr, .dark .markdown-content h1{
  border-color: #4b5563!important;
 }

 .dark .sidebar-left-menu details[open] summary, .dark .inner-menu-select,
 .dark .sidebar-left-menu details summary:hover,
 .dark .sidebar-left-menu details[open] summary:hover,
 .dark .sidebar-left-menu ul ul li a:hover{
  background: #0d1218!important;
 }
.dark .markdown-content table, .dark .markdown-content th, .dark .markdown-content td {
  background-color: #374151!important;
  border-color: #4b5563!important;
  color:  #d1d5db!important;
}
/* light version css */

.markdown-content a {
  color: #028db7;
  text-decoration: none;
}

.markdown-content a:hover {
  color: #000;
  text-decoration: none;
}

.markdown-content .breadcrumb a {
  text-decoration: none;
  color: #000 !important;
}

.markdown-content .breadcrumb a:hover {
  text-decoration: none;
  color: #000 !important;
}

.markdown-content,
.markdown-content p,
.markdown-content ul,
.markdown-content ol {
  font-size: 15px !important;
  line-height: 22px !important;
  margin-bottom: 20px !important;
}

.card-content a:hover {
  text-decoration: none !important;
}

.markdown-content {
  color: #334155 !important;
}

.markdown-content h1 {
  font-size: 36px !important;
  margin-bottom: 15px !important;
  line-height: 46px;
  letter-spacing: 0px;
  font-weight: 700;
  color: #334155 !important;
  margin-top: 15px;
  border-bottom: 1px solid #cbd5e1;
  padding-bottom: 12px;

}

.markdown-content h2 {
  font-size: 32px !important;
  line-height: 40px !important;
  margin-top: 40px !important;
  margin-bottom: 15px !important;
  font-weight: 700;
  color: #334155 !important;
}

.markdown-content h3 {
  font-size: 24px !important;
  line-height: 35px !important;
  margin-top: 28px !important;
  margin-bottom: 15px !important;
  font-weight: 700;
  color: #334155 !important;
}

.markdown-content h4 {
  font-size: 20px !important;
  line-height: 30px !important;
  margin-bottom: 15px !important;
  font-weight: 700;
  color: #334155 !important;
}

.markdown-content ul {
  padding-left: 35px !important;
  list-style: disc;
}

.markdown-content ul li::marker {
  color: #000 !important;
}

.markdown-content li+li {
  margin-top: 4px !important;
}

.markdown-content pre {
  white-space: normal !important;
}

/* table > */
.markdown-content table {
  border-collapse: collapse;
  width: 100%;
  margin: 10px 0px 20px 0px !important;
  background: white !important;
}

.markdown-content th,
.markdown-content td {
  border: 1px solid #e5e7eb;
  padding: 8px 12px;
}

.breadcrumb li {
  margin-left: 7px !important;
}

/* < table */

/* accordion > */
.markdown-content details summary {
  padding: 0px 0px 0px 30px;
  margin-bottom: 5px;
  position: relative;
  cursor: pointer;
}

.markdown-content details[open] summary {
  color: #000;
}

.markdown-content details summary {
  list-style: none;
  font-size: 20px;
}

.markdown-content summary::-webkit-details-marker {
  display: none;
}
#activityLog h1{
  display: none;
}
/* .markdown-content summary::after {
  content: "\1F892";
  font-size: 24px;
  position: absolute;
  right: 15px;
  top: 15px;
}

.markdown-content details[open] summary:after {
  content: "\1F893";
  top: 15px;
} */

.markdown-content summary::after {
  content: " ";
  display: inline-block;
  width: 24px;
  height: 24px;
  background: url('/assets/images/ico-right-arrow.png') no-repeat center center;
  background-size: 24px;
  position: absolute;
  left: 0px;
}

.markdown-content details[open] summary:after {
  background: url('/assets/images/ico-down-arrow.png') no-repeat center center;
  width: 24px;
  height: 24px;
  background-size: 24px;
  position: absolute;
  left: 0px;
}

.dark .markdown-content summary::after {
  content: " ";
  display: inline-block;
  width: 24px;
  height: 24px;
  background: url('/assets/images/ico-right-arrow-white.png') no-repeat center center;
  background-size: 24px;
  position: absolute;
  left: 0px;
}
.dark .markdown-content details[open] summary:after {
  background: url('/assets/images/ico-down-arrow-white.png') no-repeat center center;
  width: 24px;
  height: 24px;
  background-size: 24px;
  position: absolute;
  left: 0px;
}

.sql-content summary::after {
  top: 1px;
}

.sql-content details[open] summary:after {
  top: 15px;
}

/* < accordion */
.nav-menu-section .selected {
  color: #028db7 !important;
}

.slide-over-content a {
  color: #028db7 !important;
  text-decoration: none !important;
}

.slide-over-content a:hover {
  text-decoration: underline !important;
}

.selected {
  color: #028db7 !important;
}

h3.notify {
  color: #991b1b !important;
  font-weight: 500 !important;
  margin-top: 0px !important;
  margin-bottom: 0px !important;
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
}
.markdown-content b, .markdown-content strong {
    font-weight: bold!important;
}

a h3 {
  margin-top: 0px !important;
}

#soc2-plans h3 {
  margin-top: 0px !important;
}

/*::: media query starts :::> */
@media (max-width: 576px) {
  .markdown-content h1 {
    font-size: 32px !important;
    line-height: 40px !important;
  }

  .markdown-content h2 {
    font-size: 24px !important;
    line-height: 30px !important;
  }
}

/* :::::: markdown css ::::::> ends */
.dark .markdown-content-doc, .dark .markdown-content-doc .breadcrumb a, .dark .markdown-content-doc h1, .dark .markdown-content-doc h2, .dark .markdown-content-doc h3, .dark .markdown-content-doc h4, .dark .markdown-content-doc ul li::marker, .dark .markdown-content-doc b, .dark .markdown-content-doc strong, .dark .markdown-content-doc blockquote, .dark .markdown-content-doc code   {
  color: #d1d5db!important;

}
.dark .markdown-content-doc hr, .dark .markdown-content-doc h1{
  border-color: #4b5563!important;
 }
 .dark .markdown-content-doc table, .dark .markdown-content-doc thead, .dark .markdown-content-doc tr, .dark .markdown-content-doc td, .dark .markdown-content-doc th {
  background-color: #374151!important;
  border-color: #4b5563!important;
  color:  #d1d5db!important;
}
.dark .markdown-content-doc a{
  color: #028db7!important;
}
/* -- */
.sidebar-left-menu .sub-folder details summary {
  background: transparent!important;
  padding-left:18px;
}
.sidebar-left-menu .sub-folder details[open] summary {
  background: #f2f2f2 !important;
  color: #028db7 !important;
  border-radius: 4px;
}
.dark .sidebar-left-menu .sub-folder details[open] summary {
  background: #000000d9 !important;
  color: #028db7 !important;
  border-radius: 4px;
}
.pagefind-ui__search-input.svelte-e9gkc3 {
  height: 36px !important;
}
.pagefind-ui__form.svelte-e9gkc3:before {
  top: 12px !important;
}
.dark .pagefind-ui__search-input.svelte-e9gkc3 {
  background-color: #1f2937!important;
  border-color: #2b3646!important;
  color: #d1d5db!important;
}
.dark .pagefind-ui__form.svelte-e9gkc3:before {
  background-color: #d1d5db;
}
.dark .pagefind-ui__form.svelte-e9gkc3::placeholder {
  color: #d1d5db!important;
}

.dark .filter-btn {
  color:#000 !important;
}

.dark .filter-btn:hover {
  color:#fff !important;
}

/* lhc-forms */
#lhc-form-container {
  color: #000;
}
#history-tab .text-blue-600 {
    color:#028db7 !important;
   }
   #history-tab .border-blue-600 {
    border-color:#028db7 !important;
   }  
