var t="object"==typeof globalThis?globalThis:"object"==typeof self?self:"object"==typeof window?window:"object"==typeof global?global:{},e=/^(\d+)\.(\d+)\.(\d+)(-(.+))?$/;var n=function(t){var n=new Set([t]),r=new Set,o=t.match(e);if(!o)return function(){return!1};var i=+o[1],a=+o[2],s=+o[3];if(null!=o[4])return function(e){return e===t};function c(t){return r.add(t),!1}function u(t){return n.add(t),!0}return function(t){if(n.has(t))return!0;if(r.has(t))return!1;var o=t.match(e);if(!o)return c(t);var l=+o[1],p=+o[2],h=+o[3];return null!=o[4]||i!==l?c(t):0===i?a===p&&s<=h?u(t):c(t):a<=p?u(t):c(t)}}("1.0.4"),r="1.0.4".split(".")[0],o=Symbol.for("opentelemetry.js.api."+r),i=t;function a(t,e,n,r){var a;void 0===r&&(r=!1);var s=i[o]=null!==(a=i[o])&&void 0!==a?a:{version:"1.0.4"};if(!r&&s[t]){var c=new Error("@opentelemetry/api: Attempted duplicate registration of API: "+t);return n.error(c.stack||c.message),!1}if("1.0.4"!==s.version){c=new Error("@opentelemetry/api: All API registration versions must match");return n.error(c.stack||c.message),!1}return s[t]=e,n.debug("@opentelemetry/api: Registered a global for "+t+" v1.0.4."),!0}function s(t){var e,r,a=null===(e=i[o])||void 0===e?void 0:e.version;if(a&&n(a))return null===(r=i[o])||void 0===r?void 0:r[t]}function c(t,e){e.debug("@opentelemetry/api: Unregistering a global for "+t+" v1.0.4.");var n=i[o];n&&delete n[t]}var u,l,p=function(){function t(t){this._namespace=t.namespace||"DiagComponentLogger"}return t.prototype.debug=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return h("debug",this._namespace,t)},t.prototype.error=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return h("error",this._namespace,t)},t.prototype.info=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return h("info",this._namespace,t)},t.prototype.warn=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return h("warn",this._namespace,t)},t.prototype.verbose=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return h("verbose",this._namespace,t)},t}();function h(t,e,n){var r=s("diag");if(r)return n.unshift(e),r[t].apply(r,n)}(l=u||(u={}))[l.NONE=0]="NONE",l[l.ERROR=30]="ERROR",l[l.WARN=50]="WARN",l[l.INFO=60]="INFO",l[l.DEBUG=70]="DEBUG",l[l.VERBOSE=80]="VERBOSE",l[l.ALL=9999]="ALL";var f=function(){function t(){function t(t){return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];var r=s("diag");if(r)return r[t].apply(r,e)}}var e=this;e.setLogger=function(t,n){var r,o;if(void 0===n&&(n=u.INFO),t===e){var i=new Error("Cannot use diag as the logger for itself. Please use a DiagLogger implementation like ConsoleDiagLogger or a custom implementation");return e.error(null!==(r=i.stack)&&void 0!==r?r:i.message),!1}var c=s("diag"),l=function(t,e){function n(n,r){var o=e[n];return"function"==typeof o&&t>=r?o.bind(e):function(){}}return t<u.NONE?t=u.NONE:t>u.ALL&&(t=u.ALL),e=e||{},{error:n("error",u.ERROR),warn:n("warn",u.WARN),info:n("info",u.INFO),debug:n("debug",u.DEBUG),verbose:n("verbose",u.VERBOSE)}}(n,t);if(c){var p=null!==(o=(new Error).stack)&&void 0!==o?o:"<failed to generate stacktrace>";c.warn("Current logger will be overwritten from "+p),l.warn("Current logger will overwrite one already registered from "+p)}return a("diag",l,e,!0)},e.disable=function(){c("diag",e)},e.createComponentLogger=function(t){return new p(t)},e.verbose=t("verbose"),e.debug=t("debug"),e.info=t("info"),e.warn=t("warn"),e.error=t("error")}return t.instance=function(){return this._instance||(this._instance=new t),this._instance},t}(),_=function(){function t(t){this._entries=t?new Map(t):new Map}return t.prototype.getEntry=function(t){var e=this._entries.get(t);if(e)return Object.assign({},e)},t.prototype.getAllEntries=function(){return Array.from(this._entries.entries()).map((function(t){return[t[0],t[1]]}))},t.prototype.setEntry=function(e,n){var r=new t(this._entries);return r._entries.set(e,n),r},t.prototype.removeEntry=function(e){var n=new t(this._entries);return n._entries.delete(e),n},t.prototype.removeEntries=function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];for(var r=new t(this._entries),o=0,i=e;o<i.length;o++){var a=i[o];r._entries.delete(a)}return r},t.prototype.clear=function(){return new t},t}(),d=Symbol("BaggageEntryMetadata"),E=f.instance();function g(t){return void 0===t&&(t={}),new _(new Map(Object.entries(t)))}var v={get:function(t,e){if(null!=t)return t[e]},keys:function(t){return null==t?[]:Object.keys(t)}},T={set:function(t,e,n){null!=t&&(t[e]=n)}};function y(t){return Symbol.for(t)}var m,S,O=new function t(e){var n=this;n._currentContext=e?new Map(e):new Map,n.getValue=function(t){return n._currentContext.get(t)},n.setValue=function(e,r){var o=new t(n._currentContext);return o._currentContext.set(e,r),o},n.deleteValue=function(e){var r=new t(n._currentContext);return r._currentContext.delete(e),r}},b=globalThis&&globalThis.__spreadArray||function(t,e){for(var n=0,r=e.length,o=t.length;n<r;n++,o++)t[o]=e[n];return t},A=function(){function t(){}return t.prototype.active=function(){return O},t.prototype.with=function(t,e,n){for(var r=[],o=3;o<arguments.length;o++)r[o-3]=arguments[o];return e.call.apply(e,b([n],r))},t.prototype.bind=function(t,e){return e},t.prototype.enable=function(){return this},t.prototype.disable=function(){return this},t}(),P=globalThis&&globalThis.__spreadArray||function(t,e){for(var n=0,r=e.length,o=t.length;n<r;n++,o++)t[o]=e[n];return t},w=new A,N=function(){function t(){}return t.getInstance=function(){return this._instance||(this._instance=new t),this._instance},t.prototype.setGlobalContextManager=function(t){return a("context",t,f.instance())},t.prototype.active=function(){return this._getContextManager().active()},t.prototype.with=function(t,e,n){for(var r,o=[],i=3;i<arguments.length;i++)o[i-3]=arguments[i];return(r=this._getContextManager()).with.apply(r,P([t,e,n],o))},t.prototype.bind=function(t,e){return this._getContextManager().bind(t,e)},t.prototype._getContextManager=function(){return s("context")||w},t.prototype.disable=function(){this._getContextManager().disable(),c("context",f.instance())},t}();(S=m||(m={}))[S.NONE=0]="NONE",S[S.SAMPLED=1]="SAMPLED";var R={traceId:"00000000000000000000000000000000",spanId:"0000000000000000",traceFlags:m.NONE},L=function(){function t(t){void 0===t&&(t=R),this._spanContext=t}return t.prototype.spanContext=function(){return this._spanContext},t.prototype.setAttribute=function(t,e){return this},t.prototype.setAttributes=function(t){return this},t.prototype.addEvent=function(t,e){return this},t.prototype.setStatus=function(t){return this},t.prototype.updateName=function(t){return this},t.prototype.end=function(t){},t.prototype.isRecording=function(){return!1},t.prototype.recordException=function(t,e){},t}(),I=y("OpenTelemetry Context Key SPAN");function C(t){return t.getValue(I)||void 0}function k(t,e){return t.setValue(I,e)}function D(t){return t.deleteValue(I)}function M(t,e){return k(t,new L(e))}function x(t){var e;return null===(e=C(t))||void 0===e?void 0:e.spanContext()}var j=/^([0-9a-f]{32})$/i,U=/^[0-9a-f]{16}$/i;function Z(t){return j.test(t)&&"00000000000000000000000000000000"!==t}function B(t){return Z(t.traceId)&&(e=t.spanId,U.test(e)&&"0000000000000000"!==e);var e}function G(t){return new L(t)}var z=N.getInstance(),F=function(){function t(){}return t.prototype.startSpan=function(t,e,n){if(Boolean(null==e?void 0:e.root))return new L;var r,o=n&&x(n);return"object"==typeof(r=o)&&"string"==typeof r.spanId&&"string"==typeof r.traceId&&"number"==typeof r.traceFlags&&B(o)?new L(o):new L},t.prototype.startActiveSpan=function(t,e,n,r){var o,i,a;if(!(arguments.length<2)){2===arguments.length?a=e:3===arguments.length?(o=e,a=n):(o=e,i=n,a=r);var s=null!=i?i:z.active(),c=this.startSpan(t,o,s),u=k(s,c);return z.with(u,a,void 0,c)}},t}();var V,H,K,X,Y,W,q=new F,$=function(){function t(t,e,n){this._provider=t,this.name=e,this.version=n}return t.prototype.startSpan=function(t,e,n){return this._getTracer().startSpan(t,e,n)},t.prototype.startActiveSpan=function(t,e,n,r){var o=this._getTracer();return Reflect.apply(o.startActiveSpan,o,arguments)},t.prototype._getTracer=function(){if(this._delegate)return this._delegate;var t=this._provider.getDelegateTracer(this.name,this.version);return t?(this._delegate=t,this._delegate):q},t}(),J=new(function(){function t(){}return t.prototype.getTracer=function(t,e){return new F},t}()),Q=function(){function t(){}return t.prototype.getTracer=function(t,e){var n;return null!==(n=this.getDelegateTracer(t,e))&&void 0!==n?n:new $(this,t,e)},t.prototype.getDelegate=function(){var t;return null!==(t=this._delegate)&&void 0!==t?t:J},t.prototype.setDelegate=function(t){this._delegate=t},t.prototype.getDelegateTracer=function(t,e){var n;return null===(n=this._delegate)||void 0===n?void 0:n.getTracer(t,e)},t}();(H=V||(V={}))[H.NOT_RECORD=0]="NOT_RECORD",H[H.RECORD=1]="RECORD",H[H.RECORD_AND_SAMPLED=2]="RECORD_AND_SAMPLED",(X=K||(K={}))[X.INTERNAL=0]="INTERNAL",X[X.SERVER=1]="SERVER",X[X.CLIENT=2]="CLIENT",X[X.PRODUCER=3]="PRODUCER",X[X.CONSUMER=4]="CONSUMER",(W=Y||(Y={}))[W.UNSET=0]="UNSET",W[W.OK=1]="OK",W[W.ERROR=2]="ERROR";var tt=function(){function t(){this._proxyTracerProvider=new Q,this.wrapSpanContext=G,this.isSpanContextValid=B,this.deleteSpan=D,this.getSpan=C,this.getSpanContext=x,this.setSpan=k,this.setSpanContext=M}return t.getInstance=function(){return this._instance||(this._instance=new t),this._instance},t.prototype.setGlobalTracerProvider=function(t){var e=a("trace",this._proxyTracerProvider,f.instance());return e&&this._proxyTracerProvider.setDelegate(t),e},t.prototype.getTracerProvider=function(){return s("trace")||this._proxyTracerProvider},t.prototype.getTracer=function(t,e){return this.getTracerProvider().getTracer(t,e)},t.prototype.disable=function(){c("trace",f.instance()),this._proxyTracerProvider=new Q},t}(),et=function(){function t(){}return t.prototype.inject=function(t,e){},t.prototype.extract=function(t,e){return t},t.prototype.fields=function(){return[]},t}(),nt=y("OpenTelemetry Baggage Key");function rt(t){return t.getValue(nt)||void 0}function ot(t,e){return t.setValue(nt,e)}function it(t){return t.deleteValue(nt)}var at=new et,st=function(){function t(){this.createBaggage=g,this.getBaggage=rt,this.setBaggage=ot,this.deleteBaggage=it}return t.getInstance=function(){return this._instance||(this._instance=new t),this._instance},t.prototype.setGlobalPropagator=function(t){return a("propagation",t,f.instance())},t.prototype.inject=function(t,e,n){return void 0===n&&(n=T),this._getGlobalPropagator().inject(t,e,n)},t.prototype.extract=function(t,e,n){return void 0===n&&(n=v),this._getGlobalPropagator().extract(t,e,n)},t.prototype.fields=function(){return this._getGlobalPropagator().fields()},t.prototype.disable=function(){c("propagation",f.instance())},t.prototype._getGlobalPropagator=function(){return s("propagation")||at},t}(),ct=N.getInstance(),ut=tt.getInstance(),lt=st.getInstance(),pt=f.instance(),ht=y("OpenTelemetry SDK Context Key SUPPRESS_TRACING");function ft(t){return t.setValue(ht,!0)}function _t(t){return!0===t.getValue(ht)}function dt(t){var e=t.split(";");if(!(e.length<=0)){var n=e.shift();if(n){var r=n.split("=");if(2===r.length){var o,i,a=decodeURIComponent(r[0].trim()),s=decodeURIComponent(r[1].trim());return e.length>0&&("string"!=typeof(i=e.join(";"))&&(E.error("Cannot create baggage metadata from unknown type: "+typeof i),i=""),o={__TYPE__:d,toString:function(){return i}}),{key:a,value:s,metadata:o}}}}}function Et(t){return"string"!=typeof t||0===t.length?{}:t.split(",").map((function(t){return dt(t)})).filter((function(t){return void 0!==t&&t.value.length>0})).reduce((function(t,e){return t[e.key]=e.value,t}),{})}var gt=function(){function t(){}return t.prototype.inject=function(t,e,n){var r=lt.getBaggage(t);if(r&&!_t(t)){var o=function(t){return t.getAllEntries().map((function(t){var e=t[0],n=t[1];return encodeURIComponent(e)+"="+encodeURIComponent(n.value)}))}(r).filter((function(t){return t.length<=4096})).slice(0,180),i=function(t){return t.reduce((function(t,e){var n=t+(""!==t?",":"")+e;return n.length>8192?t:n}),"")}(o);i.length>0&&n.set(e,"baggage",i)}},t.prototype.extract=function(t,e,n){var r=n.get(e,"baggage");if(!r)return t;var o={};return 0===r.length?t:(r.split(",").forEach((function(t){var e=dt(t);if(e){var n={value:e.value};e.metadata&&(n.metadata=e.metadata),o[e.key]=n}})),0===Object.entries(o).length?t:lt.setBaggage(t,lt.createBaggage(o)))},t.prototype.fields=function(){return["baggage"]},t}();function vt(t){return null==t||(Array.isArray(t)?function(t){for(var e,n=0,r=t;n<r.length;n++){var o=r[n];if(null!=o){if(!e){if(Tt(o)){e=typeof o;continue}return!1}if(typeof o!==e)return!1}}return!0}(t):Tt(t))}function Tt(t){switch(typeof t){case"number":case"boolean":case"string":return!0}return!1}var yt,mt,St=function(t){pt.error(function(t){return"string"==typeof t?t:JSON.stringify(function(t){for(var e={},n=t;null!==n;)Object.getOwnPropertyNames(n).forEach((function(t){if(!e[t]){var r=n[t];r&&(e[t]=String(r))}})),n=Object.getPrototypeOf(n);return e}(t))}(t))};function Ot(t){try{St(t)}catch(e){}}(mt=yt||(yt={})).AlwaysOff="always_off",mt.AlwaysOn="always_on",mt.ParentBasedAlwaysOff="parentbased_always_off",mt.ParentBasedAlwaysOn="parentbased_always_on",mt.ParentBasedTraceIdRatio="parentbased_traceidratio",mt.TraceIdRatio="traceidratio";var bt=["OTEL_BSP_EXPORT_TIMEOUT","OTEL_BSP_MAX_EXPORT_BATCH_SIZE","OTEL_BSP_MAX_QUEUE_SIZE","OTEL_BSP_SCHEDULE_DELAY","OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT","OTEL_ATTRIBUTE_COUNT_LIMIT","OTEL_SPAN_ATTRIBUTE_VALUE_LENGTH_LIMIT","OTEL_SPAN_ATTRIBUTE_COUNT_LIMIT","OTEL_SPAN_EVENT_COUNT_LIMIT","OTEL_SPAN_LINK_COUNT_LIMIT"];function At(t){return bt.indexOf(t)>-1}var Pt=["OTEL_NO_PATCH_MODULES","OTEL_PROPAGATORS"];function wt(t){return Pt.indexOf(t)>-1}var Nt={CONTAINER_NAME:"",ECS_CONTAINER_METADATA_URI_V4:"",ECS_CONTAINER_METADATA_URI:"",HOSTNAME:"",KUBERNETES_SERVICE_HOST:"",NAMESPACE:"",OTEL_BSP_EXPORT_TIMEOUT:3e4,OTEL_BSP_MAX_EXPORT_BATCH_SIZE:512,OTEL_BSP_MAX_QUEUE_SIZE:2048,OTEL_BSP_SCHEDULE_DELAY:5e3,OTEL_EXPORTER_JAEGER_AGENT_HOST:"",OTEL_EXPORTER_JAEGER_ENDPOINT:"",OTEL_EXPORTER_JAEGER_PASSWORD:"",OTEL_EXPORTER_JAEGER_USER:"",OTEL_EXPORTER_OTLP_ENDPOINT:"",OTEL_EXPORTER_OTLP_TRACES_ENDPOINT:"",OTEL_EXPORTER_OTLP_METRICS_ENDPOINT:"",OTEL_EXPORTER_OTLP_HEADERS:"",OTEL_EXPORTER_OTLP_TRACES_HEADERS:"",OTEL_EXPORTER_OTLP_METRICS_HEADERS:"",OTEL_EXPORTER_ZIPKIN_ENDPOINT:"http://localhost:9411/api/v2/spans",OTEL_LOG_LEVEL:u.INFO,OTEL_NO_PATCH_MODULES:[],OTEL_PROPAGATORS:["tracecontext","baggage"],OTEL_RESOURCE_ATTRIBUTES:"",OTEL_SERVICE_NAME:"",OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT:Infinity,OTEL_ATTRIBUTE_COUNT_LIMIT:128,OTEL_SPAN_ATTRIBUTE_VALUE_LENGTH_LIMIT:Infinity,OTEL_SPAN_ATTRIBUTE_COUNT_LIMIT:128,OTEL_SPAN_EVENT_COUNT_LIMIT:128,OTEL_SPAN_LINK_COUNT_LIMIT:128,OTEL_TRACES_EXPORTER:"none",OTEL_TRACES_SAMPLER:yt.ParentBasedAlwaysOn,OTEL_TRACES_SAMPLER_ARG:""};function Rt(t,e,n,r,o){if(void 0===r&&(r=-1/0),void 0===o&&(o=1/0),void 0!==n[t]){var i=Number(n[t]);isNaN(i)||(e[t]=i<r?r:i>o?o:i)}}function Lt(t,e,n,r){void 0===r&&(r=",");var o=n[t];"string"==typeof o&&(e[t]=o.split(r).map((function(t){return t.trim()})))}var It={ALL:u.ALL,VERBOSE:u.VERBOSE,DEBUG:u.DEBUG,INFO:u.INFO,WARN:u.WARN,ERROR:u.ERROR,NONE:u.NONE};function Ct(t,e,n){var r=n[t];if("string"==typeof r){var o=It[r.toUpperCase()];null!=o&&(e[t]=o)}}var kt="object"==typeof globalThis?globalThis:"object"==typeof self?self:"object"==typeof window?window:"object"==typeof global?global:{};function Dt(){var t=function(t){var e={};for(var n in Nt){var r=n;if("OTEL_LOG_LEVEL"===r)Ct(r,e,t);else if(At(r))Rt(r,e,t);else if(wt(r))Lt(r,e,t);else{var o=t[r];null!=o&&(e[r]=String(o))}}return e}(kt);return Object.assign({},Nt,t)}function Mt(t){for(var e=t.length,n="",r=0;r<e;r+=2){var o=t.substring(r,r+2),i=parseInt(o,16);n+=String.fromCharCode(i)}return btoa(n)}var xt=function(){this.generateTraceId=Ut(16),this.generateSpanId=Ut(8)},jt=Array(32);function Ut(t){return function(){for(var e=0;e<2*t;e++)jt[e]=Math.floor(16*Math.random())+48,jt[e]>=58&&(jt[e]+=39);return String.fromCharCode.apply(null,jt.slice(0,2*t))}}var Zt,Bt=performance,Gt="exception.type",zt="exception.message",Ft="exception.stacktrace",Vt={CLOUD_PROVIDER:"cloud.provider",CLOUD_ACCOUNT_ID:"cloud.account.id",CLOUD_REGION:"cloud.region",CLOUD_AVAILABILITY_ZONE:"cloud.availability_zone",CLOUD_PLATFORM:"cloud.platform",AWS_ECS_CONTAINER_ARN:"aws.ecs.container.arn",AWS_ECS_CLUSTER_ARN:"aws.ecs.cluster.arn",AWS_ECS_LAUNCHTYPE:"aws.ecs.launchtype",AWS_ECS_TASK_ARN:"aws.ecs.task.arn",AWS_ECS_TASK_FAMILY:"aws.ecs.task.family",AWS_ECS_TASK_REVISION:"aws.ecs.task.revision",AWS_EKS_CLUSTER_ARN:"aws.eks.cluster.arn",AWS_LOG_GROUP_NAMES:"aws.log.group.names",AWS_LOG_GROUP_ARNS:"aws.log.group.arns",AWS_LOG_STREAM_NAMES:"aws.log.stream.names",AWS_LOG_STREAM_ARNS:"aws.log.stream.arns",CONTAINER_NAME:"container.name",CONTAINER_ID:"container.id",CONTAINER_RUNTIME:"container.runtime",CONTAINER_IMAGE_NAME:"container.image.name",CONTAINER_IMAGE_TAG:"container.image.tag",DEPLOYMENT_ENVIRONMENT:"deployment.environment",DEVICE_ID:"device.id",DEVICE_MODEL_IDENTIFIER:"device.model.identifier",DEVICE_MODEL_NAME:"device.model.name",FAAS_NAME:"faas.name",FAAS_ID:"faas.id",FAAS_VERSION:"faas.version",FAAS_INSTANCE:"faas.instance",FAAS_MAX_MEMORY:"faas.max_memory",HOST_ID:"host.id",HOST_NAME:"host.name",HOST_TYPE:"host.type",HOST_ARCH:"host.arch",HOST_IMAGE_NAME:"host.image.name",HOST_IMAGE_ID:"host.image.id",HOST_IMAGE_VERSION:"host.image.version",K8S_CLUSTER_NAME:"k8s.cluster.name",K8S_NODE_NAME:"k8s.node.name",K8S_NODE_UID:"k8s.node.uid",K8S_NAMESPACE_NAME:"k8s.namespace.name",K8S_POD_UID:"k8s.pod.uid",K8S_POD_NAME:"k8s.pod.name",K8S_CONTAINER_NAME:"k8s.container.name",K8S_REPLICASET_UID:"k8s.replicaset.uid",K8S_REPLICASET_NAME:"k8s.replicaset.name",K8S_DEPLOYMENT_UID:"k8s.deployment.uid",K8S_DEPLOYMENT_NAME:"k8s.deployment.name",K8S_STATEFULSET_UID:"k8s.statefulset.uid",K8S_STATEFULSET_NAME:"k8s.statefulset.name",K8S_DAEMONSET_UID:"k8s.daemonset.uid",K8S_DAEMONSET_NAME:"k8s.daemonset.name",K8S_JOB_UID:"k8s.job.uid",K8S_JOB_NAME:"k8s.job.name",K8S_CRONJOB_UID:"k8s.cronjob.uid",K8S_CRONJOB_NAME:"k8s.cronjob.name",OS_TYPE:"os.type",OS_DESCRIPTION:"os.description",OS_NAME:"os.name",OS_VERSION:"os.version",PROCESS_PID:"process.pid",PROCESS_EXECUTABLE_NAME:"process.executable.name",PROCESS_EXECUTABLE_PATH:"process.executable.path",PROCESS_COMMAND:"process.command",PROCESS_COMMAND_LINE:"process.command_line",PROCESS_COMMAND_ARGS:"process.command_args",PROCESS_OWNER:"process.owner",PROCESS_RUNTIME_NAME:"process.runtime.name",PROCESS_RUNTIME_VERSION:"process.runtime.version",PROCESS_RUNTIME_DESCRIPTION:"process.runtime.description",SERVICE_NAME:"service.name",SERVICE_NAMESPACE:"service.namespace",SERVICE_INSTANCE_ID:"service.instance.id",SERVICE_VERSION:"service.version",TELEMETRY_SDK_NAME:"telemetry.sdk.name",TELEMETRY_SDK_LANGUAGE:"telemetry.sdk.language",TELEMETRY_SDK_VERSION:"telemetry.sdk.version",TELEMETRY_AUTO_VERSION:"telemetry.auto.version",WEBENGINE_NAME:"webengine.name",WEBENGINE_VERSION:"webengine.version",WEBENGINE_DESCRIPTION:"webengine.description"},Ht=((Zt={})[Vt.TELEMETRY_SDK_NAME]="opentelemetry",Zt[Vt.PROCESS_RUNTIME_NAME]="browser",Zt[Vt.TELEMETRY_SDK_LANGUAGE]="webjs",Zt[Vt.TELEMETRY_SDK_VERSION]="1.0.1",Zt);var Kt,Xt,Yt=Math.pow(10,9);function Wt(t){var e=t/1e3,n=Math.trunc(e);return[n,Number((e-n).toFixed(9))*Yt]}function qt(){var t=Bt.timeOrigin;if("number"!=typeof t){var e=Bt;t=e.timing&&e.timing.fetchStart}return t}function $t(t){var e=Wt(qt()),n=Wt("number"==typeof t?t:Bt.now()),r=e[0]+n[0],o=e[1]+n[1];return o>Yt&&(o-=Yt,r+=1),[r,o]}function Jt(t){if(te(t))return t;if("number"==typeof t)return t<qt()?$t(t):Wt(t);if(t instanceof Date)return Wt(t.getTime());throw TypeError("Invalid input type")}function Qt(t){return t[0]*Yt+t[1]}function te(t){return Array.isArray(t)&&2===t.length&&"number"==typeof t[0]&&"number"==typeof t[1]}(Xt=Kt||(Kt={}))[Xt.SUCCESS=0]="SUCCESS",Xt[Xt.FAILED=1]="FAILED";var ee=function(){function t(t){var e;void 0===t&&(t={}),this._propagators=null!==(e=t.propagators)&&void 0!==e?e:[],this._fields=Array.from(new Set(this._propagators.map((function(t){return"function"==typeof t.fields?t.fields():[]})).reduce((function(t,e){return t.concat(e)}),[])))}return t.prototype.inject=function(t,e,n){for(var r=0,o=this._propagators;r<o.length;r++){var i=o[r];try{i.inject(t,e,n)}catch(a){pt.warn("Failed to inject with "+i.constructor.name+". Err: "+a.message)}}},t.prototype.extract=function(t,e,n){return this._propagators.reduce((function(t,r){try{return r.extract(t,e,n)}catch(o){pt.warn("Failed to inject with "+r.constructor.name+". Err: "+o.message)}return t}),t)},t.prototype.fields=function(){return this._fields.slice()},t}(),ne=new RegExp("^(?:[a-z][_0-9a-z-*/]{0,255}|[a-z0-9][_0-9a-z-*/]{0,240}@[a-z][_0-9a-z-*/]{0,13})$"),re=/^[ -~]{0,255}[!-~]$/,oe=/,|=/;var ie=function(){function t(t){this._internalState=new Map,t&&this._parse(t)}return t.prototype.set=function(t,e){var n=this._clone();return n._internalState.has(t)&&n._internalState.delete(t),n._internalState.set(t,e),n},t.prototype.unset=function(t){var e=this._clone();return e._internalState.delete(t),e},t.prototype.get=function(t){return this._internalState.get(t)},t.prototype.serialize=function(){var t=this;return this._keys().reduce((function(e,n){return e.push(n+"="+t.get(n)),e}),[]).join(",")},t.prototype._parse=function(t){t.length>512||(this._internalState=t.split(",").reverse().reduce((function(t,e){var n=e.trim(),r=n.indexOf("=");if(-1!==r){var o=n.slice(0,r),i=n.slice(r+1,e.length);(function(t){return ne.test(t)})(o)&&function(t){return re.test(t)&&!oe.test(t)}(i)&&t.set(o,i)}return t}),new Map),this._internalState.size>32&&(this._internalState=new Map(Array.from(this._internalState.entries()).reverse().slice(0,32))))},t.prototype._keys=function(){return Array.from(this._internalState.keys()).reverse()},t.prototype._clone=function(){var e=new t;return e._internalState=new Map(this._internalState),e},t}(),ae=new RegExp("^\\s?((?!ff)[\\da-f]{2})-((?![0]{32})[\\da-f]{32})-((?![0]{16})[\\da-f]{16})-([\\da-f]{2})(-.*)?\\s?$");var se,ce=function(){function t(){}return t.prototype.inject=function(t,e,n){var r=ut.getSpanContext(t);if(r&&!_t(t)&&B(r)){var o="00-"+r.traceId+"-"+r.spanId+"-0"+Number(r.traceFlags||m.NONE).toString(16);n.set(e,"traceparent",o),r.traceState&&n.set(e,"tracestate",r.traceState.serialize())}},t.prototype.extract=function(t,e,n){var r=n.get(e,"traceparent");if(!r)return t;var o=Array.isArray(r)?r[0]:r;if("string"!=typeof o)return t;var i=function(t){var e=ae.exec(t);return e?"00"===e[1]&&e[5]?null:{traceId:e[2],spanId:e[3],traceFlags:parseInt(e[4],16)}:null}(o);if(!i)return t;i.isRemote=!0;var a=n.get(e,"tracestate");if(a){var s=Array.isArray(a)?a.join(","):a;i.traceState=new ie("string"==typeof s?s:void 0)}return ut.setSpanContext(t,i)},t.prototype.fields=function(){return["traceparent","tracestate"]},t}();(se||(se={})).HTTP="http";var ue,le,pe=function(){function t(){}return t.prototype.shouldSample=function(){return{decision:V.NOT_RECORD}},t.prototype.toString=function(){return"AlwaysOffSampler"},t}(),he=function(){function t(){}return t.prototype.shouldSample=function(){return{decision:V.RECORD_AND_SAMPLED}},t.prototype.toString=function(){return"AlwaysOnSampler"},t}(),fe=function(){function t(t){var e,n,r,o;this._root=t.root,this._root||(Ot(new Error("ParentBasedSampler must have a root sampler configured")),this._root=new he),this._remoteParentSampled=null!==(e=t.remoteParentSampled)&&void 0!==e?e:new he,this._remoteParentNotSampled=null!==(n=t.remoteParentNotSampled)&&void 0!==n?n:new pe,this._localParentSampled=null!==(r=t.localParentSampled)&&void 0!==r?r:new he,this._localParentNotSampled=null!==(o=t.localParentNotSampled)&&void 0!==o?o:new pe}return t.prototype.shouldSample=function(t,e,n,r,o,i){var a=ut.getSpanContext(t);return a&&B(a)?a.isRemote?a.traceFlags&m.SAMPLED?this._remoteParentSampled.shouldSample(t,e,n,r,o,i):this._remoteParentNotSampled.shouldSample(t,e,n,r,o,i):a.traceFlags&m.SAMPLED?this._localParentSampled.shouldSample(t,e,n,r,o,i):this._localParentNotSampled.shouldSample(t,e,n,r,o,i):this._root.shouldSample(t,e,n,r,o,i)},t.prototype.toString=function(){return"ParentBased{root="+this._root.toString()+", remoteParentSampled="+this._remoteParentSampled.toString()+", remoteParentNotSampled="+this._remoteParentNotSampled.toString()+", localParentSampled="+this._localParentSampled.toString()+", localParentNotSampled="+this._localParentNotSampled.toString()+"}"},t}(),_e=function(){function t(t){void 0===t&&(t=0),this._ratio=t,this._ratio=this._normalize(t),this._upperBound=Math.floor(4294967295*this._ratio)}return t.prototype.shouldSample=function(t,e){return{decision:Z(e)&&this._accumulate(e)<this._upperBound?V.RECORD_AND_SAMPLED:V.NOT_RECORD}},t.prototype.toString=function(){return"TraceIdRatioBased{"+this._ratio+"}"},t.prototype._normalize=function(t){return"number"!=typeof t||isNaN(t)?0:t>=1?1:t<=0?0:t},t.prototype._accumulate=function(t){for(var e=0,n=0;n<t.length/8;n++){var r=8*n;e=(e^parseInt(t.slice(r,r+8),16))>>>0}return e},t}(),de=Function.prototype.toString,Ee=de.call(Object),ge=(ue=Object.getPrototypeOf,le=Object,function(t){return ue(le(t))}),ve=Object.prototype,Te=ve.hasOwnProperty,ye=Symbol?Symbol.toStringTag:void 0,me=ve.toString;function Se(t){if(!function(t){return null!=t&&"object"==typeof t}(t)||"[object Object]"!==function(t){if(null==t)return void 0===t?"[object Undefined]":"[object Null]";return ye&&ye in Object(t)?function(t){var e=Te.call(t,ye),n=t[ye],r=!1;try{t[ye]=void 0,r=!0}catch(i){}var o=me.call(t);r&&(e?t[ye]=n:delete t[ye]);return o}(t):function(t){return me.call(t)}(t)}(t))return!1;var e=ge(t);if(null===e)return!0;var n=Te.call(e,"constructor")&&e.constructor;return"function"==typeof n&&n instanceof n&&de.call(n)===Ee}function Oe(t){return Pe(t)?t.slice():t}function be(t,e,n,r){var o;if(void 0===n&&(n=0),!(n>20)){if(n++,Re(t)||Re(e)||we(e))o=Oe(e);else if(Pe(t)){if(o=t.slice(),Pe(e))for(var i=0,a=e.length;i<a;i++)o.push(Oe(e[i]));else if(Ne(e))for(i=0,a=(s=Object.keys(e)).length;i<a;i++){o[c=s[i]]=Oe(e[c])}}else if(Ne(t))if(Ne(e)){if(!function(t,e){if(!Se(t)||!Se(e))return!1;return!0}(t,e))return e;o=Object.assign({},t);var s;for(i=0,a=(s=Object.keys(e)).length;i<a;i++){var c,u=e[c=s[i]];if(Re(u))void 0===u?delete o[c]:o[c]=u;else{var l=o[c],p=u;if(Ae(t,c,r)||Ae(e,c,r))delete o[c];else{if(Ne(l)&&Ne(p)){var h=r.get(l)||[],f=r.get(p)||[];h.push({obj:t,key:c}),f.push({obj:e,key:c}),r.set(l,h),r.set(p,f)}o[c]=be(o[c],u,n,r)}}}}else o=e;return o}}function Ae(t,e,n){for(var r=n.get(t[e])||[],o=0,i=r.length;o<i;o++){var a=r[o];if(a.key===e&&a.obj===t)return!0}return!1}function Pe(t){return Array.isArray(t)}function we(t){return"function"==typeof t}function Ne(t){return!Re(t)&&!Pe(t)&&!we(t)&&"object"==typeof t}function Re(t){return"string"==typeof t||"number"==typeof t||"boolean"==typeof t||void 0===t||t instanceof Date||t instanceof RegExp||null===t}var Le=function(){function t(t,e,n,r,o,i,a,s){void 0===a&&(a=[]),void 0===s&&(s=$t()),this.attributes={},this.links=[],this.events=[],this.status={code:Y.UNSET},this.endTime=[0,0],this._ended=!1,this._duration=[-1,-1],this.name=n,this._spanContext=r,this.parentSpanId=i,this.kind=o,this.links=a,this.startTime=Jt(s),this.resource=t.resource,this.instrumentationLibrary=t.instrumentationLibrary,this._spanLimits=t.getSpanLimits(),this._spanProcessor=t.getActiveSpanProcessor(),this._spanProcessor.onStart(this,e),this._attributeValueLengthLimit=this._spanLimits.attributeValueLengthLimit||0}return t.prototype.spanContext=function(){return this._spanContext},t.prototype.setAttribute=function(t,e){return null==e||this._isSpanEnded()?this:0===t.length?(pt.warn("Invalid attribute key: "+t),this):vt(e)?(Object.keys(this.attributes).length>=this._spanLimits.attributeCountLimit&&!Object.prototype.hasOwnProperty.call(this.attributes,t)||(this.attributes[t]=this._truncateToSize(e)),this):(pt.warn("Invalid attribute value set for key: "+t),this)},t.prototype.setAttributes=function(t){for(var e=0,n=Object.entries(t);e<n.length;e++){var r=n[e],o=r[0],i=r[1];this.setAttribute(o,i)}return this},t.prototype.addEvent=function(t,e,n){return this._isSpanEnded()||(this.events.length>=this._spanLimits.eventCountLimit&&(pt.warn("Dropping extra events."),this.events.shift()),(te(r=e)||"number"==typeof r||r instanceof Date)&&(void 0===n&&(n=e),e=void 0),void 0===n&&(n=$t()),this.events.push({name:t,attributes:e,time:Jt(n)})),this;var r},t.prototype.setStatus=function(t){return this._isSpanEnded()||(this.status=t),this},t.prototype.updateName=function(t){return this._isSpanEnded()||(this.name=t),this},t.prototype.end=function(t){void 0===t&&(t=$t()),this._isSpanEnded()?pt.error("You can only call end() on a span once."):(this._ended=!0,this.endTime=Jt(t),this._duration=function(t,e){var n=e[0]-t[0],r=e[1]-t[1];return r<0&&(n-=1,r+=Yt),[n,r]}(this.startTime,this.endTime),this._duration[0]<0&&pt.warn("Inconsistent start and end time, startTime > endTime",this.startTime,this.endTime),this._spanProcessor.onEnd(this))},t.prototype.isRecording=function(){return!1===this._ended},t.prototype.recordException=function(t,e){void 0===e&&(e=$t());var n={};"string"==typeof t?n[zt]=t:t&&(t.code?n[Gt]=t.code.toString():t.name&&(n[Gt]=t.name),t.message&&(n[zt]=t.message),t.stack&&(n[Ft]=t.stack)),n[Gt]||n[zt]?this.addEvent("exception",n,e):pt.warn("Failed to record an exception "+t)},Object.defineProperty(t.prototype,"duration",{get:function(){return this._duration},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"ended",{get:function(){return this._ended},enumerable:!1,configurable:!0}),t.prototype._isSpanEnded=function(){return this._ended&&pt.warn("Can not execute the operation on ended Span {traceId: "+this._spanContext.traceId+", spanId: "+this._spanContext.spanId+"}"),this._ended},t.prototype._truncateToLimitUtil=function(t,e){return t.length<=e?t:t.substr(0,e)},t.prototype._truncateToSize=function(t){var e=this,n=this._attributeValueLengthLimit;return n<=0?(pt.warn("Attribute value limit must be positive, got "+n),t):"string"==typeof t?this._truncateToLimitUtil(t,n):Array.isArray(t)?t.map((function(t){return"string"==typeof t?e._truncateToLimitUtil(t,n):t})):t},t}(),Ie=Dt(),Ce=yt.AlwaysOn,ke={sampler:De(Ie),forceFlushTimeoutMillis:3e4,generalLimits:{attributeValueLengthLimit:Dt().OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT,attributeCountLimit:Dt().OTEL_ATTRIBUTE_COUNT_LIMIT},spanLimits:{attributeValueLengthLimit:Dt().OTEL_SPAN_ATTRIBUTE_VALUE_LENGTH_LIMIT,attributeCountLimit:Dt().OTEL_SPAN_ATTRIBUTE_COUNT_LIMIT,linkCountLimit:Dt().OTEL_SPAN_LINK_COUNT_LIMIT,eventCountLimit:Dt().OTEL_SPAN_EVENT_COUNT_LIMIT}};function De(t){switch(void 0===t&&(t=Dt()),t.OTEL_TRACES_SAMPLER){case yt.AlwaysOn:return new he;case yt.AlwaysOff:return new pe;case yt.ParentBasedAlwaysOn:return new fe({root:new he});case yt.ParentBasedAlwaysOff:return new fe({root:new pe});case yt.TraceIdRatio:return new _e(Me(t));case yt.ParentBasedTraceIdRatio:return new fe({root:new _e(Me(t))});default:return pt.error('OTEL_TRACES_SAMPLER value "'+t.OTEL_TRACES_SAMPLER+" invalid, defaulting to "+Ce+'".'),new he}}function Me(t){if(void 0===t.OTEL_TRACES_SAMPLER_ARG||""===t.OTEL_TRACES_SAMPLER_ARG)return pt.error("OTEL_TRACES_SAMPLER_ARG is blank, defaulting to 1."),1;var e=Number(t.OTEL_TRACES_SAMPLER_ARG);return isNaN(e)?(pt.error("OTEL_TRACES_SAMPLER_ARG="+t.OTEL_TRACES_SAMPLER_ARG+" was given, but it is invalid, defaulting to 1."),1):e<0||e>1?(pt.error("OTEL_TRACES_SAMPLER_ARG="+t.OTEL_TRACES_SAMPLER_ARG+" was given, but it is out of range ([0..1]), defaulting to 1."),1):e}var xe=function(){function t(t,e,n){this._tracerProvider=n;var r,o,i,a=(r=e,o={sampler:De()},(i=Object.assign({},ke,o,r)).generalLimits=Object.assign({},ke.generalLimits,r.generalLimits||{}),i.spanLimits=Object.assign({},ke.spanLimits,r.spanLimits||{}),128===i.spanLimits.attributeCountLimit&&128!==i.generalLimits.attributeCountLimit&&(i.spanLimits.attributeCountLimit=i.generalLimits.attributeCountLimit),1/0===i.spanLimits.attributeValueLengthLimit&&1/0!==i.generalLimits.attributeValueLengthLimit&&(i.spanLimits.attributeValueLengthLimit=i.generalLimits.attributeValueLengthLimit),i);this._sampler=a.sampler,this._generalLimits=a.generalLimits,this._spanLimits=a.spanLimits,this._idGenerator=e.idGenerator||new xt,this.resource=n.resource,this.instrumentationLibrary=t}return t.prototype.startSpan=function(t,e,n){var r,o;if(void 0===e&&(e={}),void 0===n&&(n=ct.active()),_t(n))return pt.debug("Instrumentation suppressed, returning Noop Span"),ut.wrapSpanContext(R);var i,a,s,c=function(t,e){return t.root?void 0:ut.getSpanContext(e)}(e,n),u=this._idGenerator.generateSpanId();c&&ut.isSpanContextValid(c)?(i=c.traceId,a=c.traceState,s=c.spanId):i=this._idGenerator.generateTraceId();var l=null!==(r=e.kind)&&void 0!==r?r:K.INTERNAL,p=null!==(o=e.links)&&void 0!==o?o:[],h=function(t){var e={};if(null==t||"object"!=typeof t)return e;for(var n=0,r=Object.entries(t);n<r.length;n++){var o=r[n],i=o[0],a=o[1];vt(a)&&(Array.isArray(a)?e[i]=a.slice():e[i]=a)}return e}(e.attributes),f=this._sampler.shouldSample(e.root?ut.setSpanContext(n,R):n,i,t,l,h,p),_={traceId:i,spanId:u,traceFlags:f.decision===V.RECORD_AND_SAMPLED?m.SAMPLED:m.NONE,traceState:a};if(f.decision===V.NOT_RECORD)return pt.debug("Recording is off, propagating context in a non-recording span"),ut.wrapSpanContext(_);var d=new Le(this,n,t,_,l,s,p,e.startTime);return d.setAttributes(Object.assign(h,f.attributes)),d},t.prototype.startActiveSpan=function(t,e,n,r){var o,i,a;if(!(arguments.length<2)){2===arguments.length?a=e:3===arguments.length?(o=e,a=n):(o=e,i=n,a=r);var s=null!=i?i:ct.active(),c=this.startSpan(t,o,s),u=ut.setSpan(s,c);return ct.with(u,a,void 0,c)}},t.prototype.getGeneralLimits=function(){return this._generalLimits},t.prototype.getSpanLimits=function(){return this._spanLimits},t.prototype.getActiveSpanProcessor=function(){return this._tracerProvider.getActiveSpanProcessor()},t}();globalThis&&globalThis.__awaiter,globalThis&&globalThis.__generator;var je,Ue,Ze,Be=function(){function t(t){this.attributes=t}return t.empty=function(){return t.EMPTY},t.default=function(){var e;return new t(((e={})[Vt.SERVICE_NAME]="unknown_service",e[Vt.TELEMETRY_SDK_LANGUAGE]=Ht[Vt.TELEMETRY_SDK_LANGUAGE],e[Vt.TELEMETRY_SDK_NAME]=Ht[Vt.TELEMETRY_SDK_NAME],e[Vt.TELEMETRY_SDK_VERSION]=Ht[Vt.TELEMETRY_SDK_VERSION],e))},t.prototype.merge=function(e){return e&&Object.keys(e.attributes).length?new t(Object.assign({},this.attributes,e.attributes)):this},t.EMPTY=new t({}),t}(),Ge=function(){function t(t){this._spanProcessors=t}return t.prototype.forceFlush=function(){for(var t=[],e=0,n=this._spanProcessors;e<n.length;e++){var r=n[e];t.push(r.forceFlush())}return new Promise((function(e){Promise.all(t).then((function(){e()})).catch((function(t){Ot(t||new Error("MultiSpanProcessor: forceFlush failed")),e()}))}))},t.prototype.onStart=function(t,e){for(var n=0,r=this._spanProcessors;n<r.length;n++){r[n].onStart(t,e)}},t.prototype.onEnd=function(t){for(var e=0,n=this._spanProcessors;e<n.length;e++){n[e].onEnd(t)}},t.prototype.shutdown=function(){for(var t=[],e=0,n=this._spanProcessors;e<n.length;e++){var r=n[e];t.push(r.shutdown())}return new Promise((function(e,n){Promise.all(t).then((function(){e()}),n)}))},t}(),ze=function(){function t(){}return t.prototype.onStart=function(t,e){},t.prototype.onEnd=function(t){},t.prototype.shutdown=function(){return Promise.resolve()},t.prototype.forceFlush=function(){return Promise.resolve()},t}(),Fe=function(){function t(t,e){this._exporter=t,this._finishedSpans=[],this._isShutdown=!1,this._shuttingDownPromise=Promise.resolve();var n=Dt();this._maxExportBatchSize="number"==typeof(null==e?void 0:e.maxExportBatchSize)?e.maxExportBatchSize:n.OTEL_BSP_MAX_EXPORT_BATCH_SIZE,this._maxQueueSize="number"==typeof(null==e?void 0:e.maxQueueSize)?e.maxQueueSize:n.OTEL_BSP_MAX_QUEUE_SIZE,this._scheduledDelayMillis="number"==typeof(null==e?void 0:e.scheduledDelayMillis)?e.scheduledDelayMillis:n.OTEL_BSP_SCHEDULE_DELAY,this._exportTimeoutMillis="number"==typeof(null==e?void 0:e.exportTimeoutMillis)?e.exportTimeoutMillis:n.OTEL_BSP_EXPORT_TIMEOUT}return t.prototype.forceFlush=function(){return this._isShutdown?this._shuttingDownPromise:this._flushAll()},t.prototype.onStart=function(t){},t.prototype.onEnd=function(t){this._isShutdown||0!=(t.spanContext().traceFlags&m.SAMPLED)&&this._addToBuffer(t)},t.prototype.shutdown=function(){var t=this;return this._isShutdown||(this._isShutdown=!0,this._shuttingDownPromise=new Promise((function(e,n){Promise.resolve().then((function(){return t.onShutdown()})).then((function(){return t._flushAll()})).then((function(){return t._exporter.shutdown()})).then(e).catch((function(t){n(t)}))}))),this._shuttingDownPromise},t.prototype._addToBuffer=function(t){this._finishedSpans.length>=this._maxQueueSize||(this._finishedSpans.push(t),this._maybeStartTimer())},t.prototype._flushAll=function(){var t=this;return new Promise((function(e,n){for(var r=[],o=0,i=Math.ceil(t._finishedSpans.length/t._maxExportBatchSize);o<i;o++)r.push(t._flushOneBatch());Promise.all(r).then((function(){e()})).catch(n)}))},t.prototype._flushOneBatch=function(){var t=this;return this._clearTimer(),0===this._finishedSpans.length?Promise.resolve():new Promise((function(e,n){var r=setTimeout((function(){n(new Error("Timeout"))}),t._exportTimeoutMillis);ct.with(ft(ct.active()),(function(){t._exporter.export(t._finishedSpans.splice(0,t._maxExportBatchSize),(function(t){var o;clearTimeout(r),t.code===Kt.SUCCESS?e():n(null!==(o=t.error)&&void 0!==o?o:new Error("BatchSpanProcessor: span export failed"))}))}))}))},t.prototype._maybeStartTimer=function(){var t=this;void 0===this._timer&&(this._timer=setTimeout((function(){t._flushOneBatch().then((function(){t._finishedSpans.length>0&&(t._clearTimer(),t._maybeStartTimer())})).catch((function(t){Ot(t)}))}),this._scheduledDelayMillis),this._timer)},t.prototype._clearTimer=function(){void 0!==this._timer&&(clearTimeout(this._timer),this._timer=void 0)},t}(),Ve=globalThis&&globalThis.__extends||(je=function(t,e){return(je=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}je(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),He=function(t){function e(e,n){var r=t.call(this,e,n)||this;return r.onInit(n),r}return Ve(e,t),e.prototype.onInit=function(t){var e=this;!0!==(null==t?void 0:t.disableAutoFlushOnDocumentHide)&&null!=document&&(this._visibilityChangeListener=function(){"hidden"===document.visibilityState&&e.forceFlush()},this._pageHideListener=function(){e.forceFlush()},document.addEventListener("visibilitychange",this._visibilityChangeListener),document.addEventListener("pagehide",this._pageHideListener))},e.prototype.onShutdown=function(){this._visibilityChangeListener&&document.removeEventListener("visibilitychange",this._visibilityChangeListener),this._pageHideListener&&document.removeEventListener("pagehide",this._pageHideListener)},e}(Fe);(Ze=Ue||(Ue={}))[Ze.resolved=0]="resolved",Ze[Ze.timeout=1]="timeout",Ze[Ze.error=2]="error",Ze[Ze.unresolved=3]="unresolved";var Ke,Xe,Ye=function(){function t(t){var e;void 0===t&&(t={}),this._registeredSpanProcessors=[],this._tracers=new Map;var n=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];for(var n=t.shift(),r=new WeakMap;t.length>0;)n=be(n,t.shift(),0,r);return n}({},ke,t);this.resource=null!==(e=n.resource)&&void 0!==e?e:Be.empty(),this.resource=Be.default().merge(this.resource),this._config=Object.assign({},n,{resource:this.resource});var r=this._buildExporterFromEnv();if(void 0!==r){var o=new He(r);this.activeSpanProcessor=o}else this.activeSpanProcessor=new ze}return t.prototype.getTracer=function(t,e){var n=t+"@"+(e||"");return this._tracers.has(n)||this._tracers.set(n,new xe({name:t,version:e},this._config,this)),this._tracers.get(n)},t.prototype.addSpanProcessor=function(t){0===this._registeredSpanProcessors.length&&this.activeSpanProcessor.shutdown().catch((function(t){return pt.error("Error while trying to shutdown current span processor",t)})),this._registeredSpanProcessors.push(t),this.activeSpanProcessor=new Ge(this._registeredSpanProcessors)},t.prototype.getActiveSpanProcessor=function(){return this.activeSpanProcessor},t.prototype.register=function(t){void 0===t&&(t={}),ut.setGlobalTracerProvider(this),void 0===t.propagator&&(t.propagator=this._buildPropagatorFromEnv()),t.contextManager&&ct.setGlobalContextManager(t.contextManager),t.propagator&&lt.setGlobalPropagator(t.propagator)},t.prototype.forceFlush=function(){var t=this._config.forceFlushTimeoutMillis,e=this._registeredSpanProcessors.map((function(e){return new Promise((function(n){var r,o=setTimeout((function(){n(new Error("Span processor did not completed within timeout period of "+t+" ms")),r=Ue.timeout}),t);e.forceFlush().then((function(){clearTimeout(o),r!==Ue.timeout&&(r=Ue.resolved,n(r))})).catch((function(t){clearTimeout(o),r=Ue.error,n(t)}))}))}));return new Promise((function(t,n){Promise.all(e).then((function(e){var r=e.filter((function(t){return t!==Ue.resolved}));r.length>0?n(r):t()})).catch((function(t){return n([t])}))}))},t.prototype.shutdown=function(){return this.activeSpanProcessor.shutdown()},t.prototype._getPropagator=function(e){var n;return null===(n=t._registeredPropagators.get(e))||void 0===n?void 0:n()},t.prototype._getSpanExporter=function(e){var n;return null===(n=t._registeredExporters.get(e))||void 0===n?void 0:n()},t.prototype._buildPropagatorFromEnv=function(){var t=this,e=Array.from(new Set(Dt().OTEL_PROPAGATORS)),n=e.map((function(e){var n=t._getPropagator(e);return n||pt.warn('Propagator "'+e+'" requested through environment variable is unavailable.'),n})).reduce((function(t,e){return e&&t.push(e),t}),[]);return 0===n.length?void 0:1===e.length?n[0]:new ee({propagators:n})},t.prototype._buildExporterFromEnv=function(){var t=Dt().OTEL_TRACES_EXPORTER;if("none"!==t){var e=this._getSpanExporter(t);return e||pt.error('Exporter "'+t+'" requested through environment variable is unavailable.'),e}},t._registeredPropagators=new Map([["tracecontext",function(){return new ce}],["baggage",function(){return new gt}]]),t._registeredExporters=new Map,t}(),We=function(){function t(t){this._exporter=t,this._isShutdown=!1,this._shuttingDownPromise=Promise.resolve()}return t.prototype.forceFlush=function(){return Promise.resolve()},t.prototype.onStart=function(t){},t.prototype.onEnd=function(t){var e=this;this._isShutdown||0!=(t.spanContext().traceFlags&m.SAMPLED)&&ct.with(ft(ct.active()),(function(){e._exporter.export([t],(function(t){var e;t.code!==Kt.SUCCESS&&Ot(null!==(e=t.error)&&void 0!==e?e:new Error("SimpleSpanProcessor: span export failed (status "+t+")"))}))}))},t.prototype.shutdown=function(){var t=this;return this._isShutdown||(this._isShutdown=!0,this._shuttingDownPromise=new Promise((function(e,n){Promise.resolve().then((function(){return t._exporter.shutdown()})).then(e).catch((function(t){n(t)}))}))),this._shuttingDownPromise},t}(),qe=globalThis&&globalThis.__spreadArray||function(t,e){for(var n=0,r=e.length,o=t.length;n<r;n++,o++)t[o]=e[n];return t},$e=function(){function t(){this._enabled=!1,this._currentContext=O}return t.prototype._bindFunction=function(t,e){void 0===t&&(t=O);var n=this,r=function(){for(var r=this,o=[],i=0;i<arguments.length;i++)o[i]=arguments[i];return n.with(t,(function(){return e.apply(r,o)}))};return Object.defineProperty(r,"length",{enumerable:!1,configurable:!0,writable:!1,value:e.length}),r},t.prototype.active=function(){return this._currentContext},t.prototype.bind=function(t,e){return void 0===t&&(t=this.active()),"function"==typeof e?this._bindFunction(t,e):e},t.prototype.disable=function(){return this._currentContext=O,this._enabled=!1,this},t.prototype.enable=function(){return this._enabled||(this._enabled=!0,this._currentContext=O),this},t.prototype.with=function(t,e,n){for(var r=[],o=3;o<arguments.length;o++)r[o-3]=arguments[o];var i=this._currentContext;this._currentContext=t||O;try{return e.call.apply(e,qe([n],r))}finally{this._currentContext=i}},t}(),Je=globalThis&&globalThis.__extends||function(){var t=function(e,n){return(t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(e,n)};return function(e,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),Qe=function(t){function e(e){void 0===e&&(e={});var n=t.call(this,e)||this;if(e.contextManager)throw"contextManager should be defined in register method not in constructor";if(e.propagator)throw"propagator should be defined in register method not in constructor";return n}return Je(e,t),e.prototype.register=function(e){void 0===e&&(e={}),void 0===e.contextManager&&(e.contextManager=new $e),e.contextManager&&e.contextManager.enable(),t.prototype.register.call(this,e)},e}(Ye);(Xe=Ke||(Ke={})).CONNECT_END="connectEnd",Xe.CONNECT_START="connectStart",Xe.DECODED_BODY_SIZE="decodedBodySize",Xe.DOM_COMPLETE="domComplete",Xe.DOM_CONTENT_LOADED_EVENT_END="domContentLoadedEventEnd",Xe.DOM_CONTENT_LOADED_EVENT_START="domContentLoadedEventStart",Xe.DOM_INTERACTIVE="domInteractive",Xe.DOMAIN_LOOKUP_END="domainLookupEnd",Xe.DOMAIN_LOOKUP_START="domainLookupStart",Xe.ENCODED_BODY_SIZE="encodedBodySize",Xe.FETCH_START="fetchStart",Xe.LOAD_EVENT_END="loadEventEnd",Xe.LOAD_EVENT_START="loadEventStart",Xe.NAVIGATION_START="navigationStart",Xe.REDIRECT_END="redirectEnd",Xe.REDIRECT_START="redirectStart",Xe.REQUEST_START="requestStart",Xe.RESPONSE_END="responseEnd",Xe.RESPONSE_START="responseStart",Xe.SECURE_CONNECTION_START="secureConnectionStart",Xe.UNLOAD_EVENT_END="unloadEventEnd",Xe.UNLOAD_EVENT_START="unloadEventStart";var tn=function(){function t(){this._enabled=!1,this._zoneCounter=0}return t.prototype._activeContextFromZone=function(t){return t&&t.get("OT_ZONE_CONTEXT")||O},t.prototype._bindFunction=function(t,e){var n=this,r=function(){for(var r=this,o=[],i=0;i<arguments.length;i++)o[i]=arguments[i];return n.with(t,(function(){return e.apply(r,o)}))};return Object.defineProperty(r,"length",{enumerable:!1,configurable:!0,writable:!1,value:e.length}),r},t.prototype._bindListener=function(t,e){var n=e;return void 0!==n.__ot_listeners||(n.__ot_listeners={},"function"==typeof n.addEventListener&&(n.addEventListener=this._patchAddEventListener(n,n.addEventListener,t)),"function"==typeof n.removeEventListener&&(n.removeEventListener=this._patchRemoveEventListener(n,n.removeEventListener))),e},t.prototype._createZoneName=function(){this._zoneCounter++;var t=Math.random();return this._zoneCounter+"-"+t},t.prototype._createZone=function(t,e){var n;return Zone.current.fork({name:t,properties:(n={},n.OT_ZONE_CONTEXT=e,n)})},t.prototype._getActiveZone=function(){return Zone.current},t.prototype._patchAddEventListener=function(t,e,n){var r=this;return function(o,i,a){void 0===t.__ot_listeners&&(t.__ot_listeners={});var s=t.__ot_listeners[o];void 0===s&&(s=new WeakMap,t.__ot_listeners[o]=s);var c=r.bind(n,i);return s.set(i,c),e.call(this,o,c,a)}},t.prototype._patchRemoveEventListener=function(t,e){return function(n,r){if(void 0===t.__ot_listeners||void 0===t.__ot_listeners[n])return e.call(this,n,r);var o=t.__ot_listeners[n],i=o.get(r);return o.delete(r),e.call(this,n,i||r)}},t.prototype.active=function(){if(!this._enabled)return O;var t=this._getActiveZone(),e=this._activeContextFromZone(t);return e||O},t.prototype.bind=function(t,e){return void 0===t&&(t=this.active()),"function"==typeof e?this._bindFunction(t,e):(void 0===(n=e)&&(n={}),"function"==typeof n.addEventListener&&"function"==typeof n.removeEventListener&&this._bindListener(t,e),e);var n},t.prototype.disable=function(){return this._enabled=!1,this},t.prototype.enable=function(){return this._enabled=!0,this},t.prototype.with=function(t,e,n){for(var r=[],o=3;o<arguments.length;o++)r[o-3]=arguments[o];var i=this._createZoneName(),a=this._createZone(i,t);return a.run(e,n,r)},t}(),en="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};
/**
 * @license Angular v14.2.0-next.0
 * (c) 2010-2022 Google LLC. https://angular.io/
 * License: MIT
 */
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
!function(t){const e=t.performance;function n(t){e&&e.mark&&e.mark(t)}function r(t,n){e&&e.measure&&e.measure(t,n)}n("Zone");const o=t.__Zone_symbol_prefix||"__zone_symbol__";function i(t){return o+t}const a=!0===t[i("forceDuplicateZoneCheck")];if(t.Zone){if(a||"function"!=typeof t.Zone.__symbol__)throw new Error("Zone already loaded.");return t.Zone}class s{constructor(t,e){this._parent=t,this._name=e?e.name||"unnamed":"<root>",this._properties=e&&e.properties||{},this._zoneDelegate=new u(this,this._parent&&this._parent._zoneDelegate,e)}static assertZonePatched(){if(t.Promise!==L.ZoneAwarePromise)throw new Error("Zone.js has detected that ZoneAwarePromise `(window|global).Promise` has been overwritten.\nMost likely cause is that a Promise polyfill has been loaded after Zone.js (Polyfilling Promise api is not necessary when zone.js is loaded. If you must load one, do so before loading zone.js.)")}static get root(){let t=s.current;for(;t.parent;)t=t.parent;return t}static get current(){return C.zone}static get currentTask(){return k}static __load_patch(e,o,i=!1){if(L.hasOwnProperty(e)){if(!i&&a)throw Error("Already loaded patch: "+e)}else if(!t["__Zone_disable_"+e]){const i="Zone:"+e;n(i),L[e]=o(t,s,I),r(i,i)}}get parent(){return this._parent}get name(){return this._name}get(t){const e=this.getZoneWith(t);if(e)return e._properties[t]}getZoneWith(t){let e=this;for(;e;){if(e._properties.hasOwnProperty(t))return e;e=e._parent}return null}fork(t){if(!t)throw new Error("ZoneSpec required!");return this._zoneDelegate.fork(this,t)}wrap(t,e){if("function"!=typeof t)throw new Error("Expecting function got: "+t);const n=this._zoneDelegate.intercept(this,t,e),r=this;return function(){return r.runGuarded(n,this,arguments,e)}}run(t,e,n,r){C={parent:C,zone:this};try{return this._zoneDelegate.invoke(this,t,e,n,r)}finally{C=C.parent}}runGuarded(t,e=null,n,r){C={parent:C,zone:this};try{try{return this._zoneDelegate.invoke(this,t,e,n,r)}catch(o){if(this._zoneDelegate.handleError(this,o))throw o}}finally{C=C.parent}}runTask(t,e,n){if(t.zone!=this)throw new Error("A task can only be run in the zone of creation! (Creation: "+(t.zone||y).name+"; Execution: "+this.name+")");if(t.state===m&&(t.type===R||t.type===N))return;const r=t.state!=b;r&&t._transitionTo(b,O),t.runCount++;const o=k;k=t,C={parent:C,zone:this};try{t.type==N&&t.data&&!t.data.isPeriodic&&(t.cancelFn=void 0);try{return this._zoneDelegate.invokeTask(this,t,e,n)}catch(i){if(this._zoneDelegate.handleError(this,i))throw i}}finally{t.state!==m&&t.state!==P&&(t.type==R||t.data&&t.data.isPeriodic?r&&t._transitionTo(O,b):(t.runCount=0,this._updateTaskCount(t,-1),r&&t._transitionTo(m,b,m))),C=C.parent,k=o}}scheduleTask(t){if(t.zone&&t.zone!==this){let e=this;for(;e;){if(e===t.zone)throw Error(`can not reschedule task to ${this.name} which is descendants of the original zone ${t.zone.name}`);e=e.parent}}t._transitionTo(S,m);const e=[];t._zoneDelegates=e,t._zone=this;try{t=this._zoneDelegate.scheduleTask(this,t)}catch(n){throw t._transitionTo(P,S,m),this._zoneDelegate.handleError(this,n),n}return t._zoneDelegates===e&&this._updateTaskCount(t,1),t.state==S&&t._transitionTo(O,S),t}scheduleMicroTask(t,e,n,r){return this.scheduleTask(new l(w,t,e,n,r,void 0))}scheduleMacroTask(t,e,n,r,o){return this.scheduleTask(new l(N,t,e,n,r,o))}scheduleEventTask(t,e,n,r,o){return this.scheduleTask(new l(R,t,e,n,r,o))}cancelTask(t){if(t.zone!=this)throw new Error("A task can only be cancelled in the zone of creation! (Creation: "+(t.zone||y).name+"; Execution: "+this.name+")");t._transitionTo(A,O,b);try{this._zoneDelegate.cancelTask(this,t)}catch(e){throw t._transitionTo(P,A),this._zoneDelegate.handleError(this,e),e}return this._updateTaskCount(t,-1),t._transitionTo(m,A),t.runCount=0,t}_updateTaskCount(t,e){const n=t._zoneDelegates;-1==e&&(t._zoneDelegates=null);for(let r=0;r<n.length;r++)n[r]._updateTaskCount(t.type,e)}}s.__symbol__=i;const c={name:"",onHasTask:(t,e,n,r)=>t.hasTask(n,r),onScheduleTask:(t,e,n,r)=>t.scheduleTask(n,r),onInvokeTask:(t,e,n,r,o,i)=>t.invokeTask(n,r,o,i),onCancelTask:(t,e,n,r)=>t.cancelTask(n,r)};class u{constructor(t,e,n){this._taskCounts={microTask:0,macroTask:0,eventTask:0},this.zone=t,this._parentDelegate=e,this._forkZS=n&&(n&&n.onFork?n:e._forkZS),this._forkDlgt=n&&(n.onFork?e:e._forkDlgt),this._forkCurrZone=n&&(n.onFork?this.zone:e._forkCurrZone),this._interceptZS=n&&(n.onIntercept?n:e._interceptZS),this._interceptDlgt=n&&(n.onIntercept?e:e._interceptDlgt),this._interceptCurrZone=n&&(n.onIntercept?this.zone:e._interceptCurrZone),this._invokeZS=n&&(n.onInvoke?n:e._invokeZS),this._invokeDlgt=n&&(n.onInvoke?e:e._invokeDlgt),this._invokeCurrZone=n&&(n.onInvoke?this.zone:e._invokeCurrZone),this._handleErrorZS=n&&(n.onHandleError?n:e._handleErrorZS),this._handleErrorDlgt=n&&(n.onHandleError?e:e._handleErrorDlgt),this._handleErrorCurrZone=n&&(n.onHandleError?this.zone:e._handleErrorCurrZone),this._scheduleTaskZS=n&&(n.onScheduleTask?n:e._scheduleTaskZS),this._scheduleTaskDlgt=n&&(n.onScheduleTask?e:e._scheduleTaskDlgt),this._scheduleTaskCurrZone=n&&(n.onScheduleTask?this.zone:e._scheduleTaskCurrZone),this._invokeTaskZS=n&&(n.onInvokeTask?n:e._invokeTaskZS),this._invokeTaskDlgt=n&&(n.onInvokeTask?e:e._invokeTaskDlgt),this._invokeTaskCurrZone=n&&(n.onInvokeTask?this.zone:e._invokeTaskCurrZone),this._cancelTaskZS=n&&(n.onCancelTask?n:e._cancelTaskZS),this._cancelTaskDlgt=n&&(n.onCancelTask?e:e._cancelTaskDlgt),this._cancelTaskCurrZone=n&&(n.onCancelTask?this.zone:e._cancelTaskCurrZone),this._hasTaskZS=null,this._hasTaskDlgt=null,this._hasTaskDlgtOwner=null,this._hasTaskCurrZone=null;const r=n&&n.onHasTask,o=e&&e._hasTaskZS;(r||o)&&(this._hasTaskZS=r?n:c,this._hasTaskDlgt=e,this._hasTaskDlgtOwner=this,this._hasTaskCurrZone=t,n.onScheduleTask||(this._scheduleTaskZS=c,this._scheduleTaskDlgt=e,this._scheduleTaskCurrZone=this.zone),n.onInvokeTask||(this._invokeTaskZS=c,this._invokeTaskDlgt=e,this._invokeTaskCurrZone=this.zone),n.onCancelTask||(this._cancelTaskZS=c,this._cancelTaskDlgt=e,this._cancelTaskCurrZone=this.zone))}fork(t,e){return this._forkZS?this._forkZS.onFork(this._forkDlgt,this.zone,t,e):new s(t,e)}intercept(t,e,n){return this._interceptZS?this._interceptZS.onIntercept(this._interceptDlgt,this._interceptCurrZone,t,e,n):e}invoke(t,e,n,r,o){return this._invokeZS?this._invokeZS.onInvoke(this._invokeDlgt,this._invokeCurrZone,t,e,n,r,o):e.apply(n,r)}handleError(t,e){return!this._handleErrorZS||this._handleErrorZS.onHandleError(this._handleErrorDlgt,this._handleErrorCurrZone,t,e)}scheduleTask(t,e){let n=e;if(this._scheduleTaskZS)this._hasTaskZS&&n._zoneDelegates.push(this._hasTaskDlgtOwner),n=this._scheduleTaskZS.onScheduleTask(this._scheduleTaskDlgt,this._scheduleTaskCurrZone,t,e),n||(n=e);else if(e.scheduleFn)e.scheduleFn(e);else{if(e.type!=w)throw new Error("Task is missing scheduleFn.");v(e)}return n}invokeTask(t,e,n,r){return this._invokeTaskZS?this._invokeTaskZS.onInvokeTask(this._invokeTaskDlgt,this._invokeTaskCurrZone,t,e,n,r):e.callback.apply(n,r)}cancelTask(t,e){let n;if(this._cancelTaskZS)n=this._cancelTaskZS.onCancelTask(this._cancelTaskDlgt,this._cancelTaskCurrZone,t,e);else{if(!e.cancelFn)throw Error("Task is not cancelable");n=e.cancelFn(e)}return n}hasTask(t,e){try{this._hasTaskZS&&this._hasTaskZS.onHasTask(this._hasTaskDlgt,this._hasTaskCurrZone,t,e)}catch(n){this.handleError(t,n)}}_updateTaskCount(t,e){const n=this._taskCounts,r=n[t],o=n[t]=r+e;if(o<0)throw new Error("More tasks executed then were scheduled.");if(0==r||0==o){const e={microTask:n.microTask>0,macroTask:n.macroTask>0,eventTask:n.eventTask>0,change:t};this.hasTask(this.zone,e)}}}class l{constructor(e,n,r,o,i,a){if(this._zone=null,this.runCount=0,this._zoneDelegates=null,this._state="notScheduled",this.type=e,this.source=n,this.data=o,this.scheduleFn=i,this.cancelFn=a,!r)throw new Error("callback is not defined");this.callback=r;const s=this;e===R&&o&&o.useG?this.invoke=l.invokeTask:this.invoke=function(){return l.invokeTask.call(t,s,this,arguments)}}static invokeTask(t,e,n){t||(t=this),D++;try{return t.runCount++,t.zone.runTask(t,e,n)}finally{1==D&&T(),D--}}get zone(){return this._zone}get state(){return this._state}cancelScheduleRequest(){this._transitionTo(m,S)}_transitionTo(t,e,n){if(this._state!==e&&this._state!==n)throw new Error(`${this.type} '${this.source}': can not transition to '${t}', expecting state '${e}'${n?" or '"+n+"'":""}, was '${this._state}'.`);this._state=t,t==m&&(this._zoneDelegates=null)}toString(){return this.data&&void 0!==this.data.handleId?this.data.handleId.toString():Object.prototype.toString.call(this)}toJSON(){return{type:this.type,state:this.state,source:this.source,zone:this.zone.name,runCount:this.runCount}}}const p=i("setTimeout"),h=i("Promise"),f=i("then");let _,d=[],E=!1;function g(e){if(_||t[h]&&(_=t[h].resolve(0)),_){let t=_[f];t||(t=_.then),t.call(_,e)}else t[p](e,0)}function v(t){0===D&&0===d.length&&g(T),t&&d.push(t)}function T(){if(!E){for(E=!0;d.length;){const e=d;d=[];for(let n=0;n<e.length;n++){const r=e[n];try{r.zone.runTask(r,null,null)}catch(t){I.onUnhandledError(t)}}}I.microtaskDrainDone(),E=!1}}const y={name:"NO ZONE"},m="notScheduled",S="scheduling",O="scheduled",b="running",A="canceling",P="unknown",w="microTask",N="macroTask",R="eventTask",L={},I={symbol:i,currentZoneFrame:()=>C,onUnhandledError:M,microtaskDrainDone:M,scheduleMicroTask:v,showUncaughtError:()=>!s[i("ignoreConsoleErrorUncaughtError")],patchEventTarget:()=>[],patchOnProperties:M,patchMethod:()=>M,bindArguments:()=>[],patchThen:()=>M,patchMacroTask:()=>M,patchEventPrototype:()=>M,isIEOrEdge:()=>!1,getGlobalObjects:()=>{},ObjectDefineProperty:()=>M,ObjectGetOwnPropertyDescriptor:()=>{},ObjectCreate:()=>{},ArraySlice:()=>[],patchClass:()=>M,wrapWithCurrentZone:()=>M,filterProperties:()=>[],attachOriginToPatched:()=>M,_redefineProperty:()=>M,patchCallbacks:()=>M,nativeScheduleMicroTask:g};let C={parent:null,zone:new s(null,null)},k=null,D=0;function M(){}r("Zone","Zone"),t.Zone=s}("undefined"!=typeof window&&window||"undefined"!=typeof self&&self||en);
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
const nn=Object.getOwnPropertyDescriptor,rn=Object.defineProperty,on=Object.getPrototypeOf,an=Object.create,sn=Array.prototype.slice,cn=Zone.__symbol__("addEventListener"),un=Zone.__symbol__("removeEventListener"),ln=Zone.__symbol__("");function pn(t,e){return Zone.current.wrap(t,e)}function hn(t,e,n,r,o){return Zone.current.scheduleMacroTask(t,e,n,r,o)}const fn=Zone.__symbol__,_n="undefined"!=typeof window,dn=_n?window:void 0,En=_n&&dn||"object"==typeof self&&self||en;function gn(t,e){for(let n=t.length-1;n>=0;n--)"function"==typeof t[n]&&(t[n]=pn(t[n],e+"_"+n));return t}function vn(t){return!t||!1!==t.writable&&!("function"==typeof t.get&&void 0===t.set)}const Tn="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope,yn=!("nw"in En)&&void 0!==En.process&&"[object process]"==={}.toString.call(En.process),mn=!yn&&!Tn&&!(!_n||!dn.HTMLElement),Sn=void 0!==En.process&&"[object process]"==={}.toString.call(En.process)&&!Tn&&!(!_n||!dn.HTMLElement),On={},bn=function(t){if(!(t=t||En.event))return;let e=On[t.type];e||(e=On[t.type]=fn("ON_PROPERTY"+t.type));const n=this||t.target||En,r=n[e];let o;if(mn&&n===dn&&"error"===t.type){const e=t;o=r&&r.call(this,e.message,e.filename,e.lineno,e.colno,e.error),!0===o&&t.preventDefault()}else o=r&&r.apply(this,arguments),null==o||o||t.preventDefault();return o};function An(t,e,n){let r=nn(t,e);if(!r&&n){nn(n,e)&&(r={enumerable:!0,configurable:!0})}if(!r||!r.configurable)return;const o=fn("on"+e+"patched");if(t.hasOwnProperty(o)&&t[o])return;delete r.writable,delete r.value;const i=r.get,a=r.set,s=e.slice(2);let c=On[s];c||(c=On[s]=fn("ON_PROPERTY"+s)),r.set=function(e){let n=this;if(n||t!==En||(n=En),!n)return;"function"==typeof n[c]&&n.removeEventListener(s,bn),a&&a.call(n,null),n[c]=e,"function"==typeof e&&n.addEventListener(s,bn,!1)},r.get=function(){let n=this;if(n||t!==En||(n=En),!n)return null;const o=n[c];if(o)return o;if(i){let t=i.call(this);if(t)return r.set.call(this,t),"function"==typeof n.removeAttribute&&n.removeAttribute(e),t}return null},rn(t,e,r),t[o]=!0}function Pn(t,e,n){if(e)for(let r=0;r<e.length;r++)An(t,"on"+e[r],n);else{const e=[];for(const n in t)"on"==n.slice(0,2)&&e.push(n);for(let r=0;r<e.length;r++)An(t,e[r],n)}}const wn=fn("originalInstance");function Nn(t){const e=En[t];if(!e)return;En[fn(t)]=e,En[t]=function(){const n=gn(arguments,t);switch(n.length){case 0:this[wn]=new e;break;case 1:this[wn]=new e(n[0]);break;case 2:this[wn]=new e(n[0],n[1]);break;case 3:this[wn]=new e(n[0],n[1],n[2]);break;case 4:this[wn]=new e(n[0],n[1],n[2],n[3]);break;default:throw new Error("Arg list too long.")}},In(En[t],e);const n=new e((function(){}));let r;for(r in n)"XMLHttpRequest"===t&&"responseBlob"===r||function(e){"function"==typeof n[e]?En[t].prototype[e]=function(){return this[wn][e].apply(this[wn],arguments)}:rn(En[t].prototype,e,{set:function(n){"function"==typeof n?(this[wn][e]=pn(n,t+"."+e),In(this[wn][e],n)):this[wn][e]=n},get:function(){return this[wn][e]}})}(r);for(r in e)"prototype"!==r&&e.hasOwnProperty(r)&&(En[t][r]=e[r])}function Rn(t,e,n){let r=t;for(;r&&!r.hasOwnProperty(e);)r=on(r);!r&&t[e]&&(r=t);const o=fn(e);let i=null;if(r&&(!(i=r[o])||!r.hasOwnProperty(o))){i=r[o]=r[e];if(vn(r&&nn(r,e))){const t=n(i,o,e);r[e]=function(){return t(this,arguments)},In(r[e],i)}}return i}function Ln(t,e,n){let r=null;function o(t){const e=t.data;return e.args[e.cbIdx]=function(){t.invoke.apply(this,arguments)},r.apply(e.target,e.args),t}r=Rn(t,e,(t=>function(e,r){const i=n(e,r);return i.cbIdx>=0&&"function"==typeof r[i.cbIdx]?hn(i.name,r[i.cbIdx],i,o):t.apply(e,r)}))}function In(t,e){t[fn("OriginalDelegate")]=e}let Cn=!1,kn=!1;function Dn(){if(Cn)return kn;Cn=!0;try{const t=dn.navigator.userAgent;-1===t.indexOf("MSIE ")&&-1===t.indexOf("Trident/")&&-1===t.indexOf("Edge/")||(kn=!0)}catch(t){}return kn}
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */Zone.__load_patch("ZoneAwarePromise",((t,e,n)=>{const r=Object.getOwnPropertyDescriptor,o=Object.defineProperty;const i=n.symbol,a=[],s=!0===t[i("DISABLE_WRAPPING_UNCAUGHT_PROMISE_REJECTION")],c=i("Promise"),u=i("then");n.onUnhandledError=t=>{if(n.showUncaughtError()){const e=t&&t.rejection;e?console.error("Unhandled Promise rejection:",e instanceof Error?e.message:e,"; Zone:",t.zone.name,"; Task:",t.task&&t.task.source,"; Value:",e,e instanceof Error?e.stack:void 0):console.error(t)}},n.microtaskDrainDone=()=>{for(;a.length;){const e=a.shift();try{e.zone.runGuarded((()=>{if(e.throwOriginal)throw e.rejection;throw e}))}catch(t){p(t)}}};const l=i("unhandledPromiseRejectionHandler");function p(t){n.onUnhandledError(t);try{const n=e[l];"function"==typeof n&&n.call(this,t)}catch(r){}}function h(t){return t&&t.then}function f(t){return t}function _(t){return C.reject(t)}const d=i("state"),E=i("value"),g=i("finally"),v=i("parentPromiseValue"),T=i("parentPromiseState"),y=null,m=!0,S=!1;function O(t,e){return n=>{try{P(t,e,n)}catch(r){P(t,!1,r)}}}const b=function(){let t=!1;return function(e){return function(){t||(t=!0,e.apply(null,arguments))}}},A=i("currentTaskTrace");function P(t,r,i){const c=b();if(t===i)throw new TypeError("Promise resolved with itself");if(t[d]===y){let l=null;try{"object"!=typeof i&&"function"!=typeof i||(l=i&&i.then)}catch(u){return c((()=>{P(t,!1,u)}))(),t}if(r!==S&&i instanceof C&&i.hasOwnProperty(d)&&i.hasOwnProperty(E)&&i[d]!==y)N(i),P(t,i[d],i[E]);else if(r!==S&&"function"==typeof l)try{l.call(i,c(O(t,r)),c(O(t,!1)))}catch(u){c((()=>{P(t,!1,u)}))()}else{t[d]=r;const c=t[E];if(t[E]=i,t[g]===g&&r===m&&(t[d]=t[T],t[E]=t[v]),r===S&&i instanceof Error){const t=e.currentTask&&e.currentTask.data&&e.currentTask.data.__creationTrace__;t&&o(i,A,{configurable:!0,enumerable:!1,writable:!0,value:t})}for(let e=0;e<c.length;)R(t,c[e++],c[e++],c[e++],c[e++]);if(0==c.length&&r==S){t[d]=0;let r=i;try{throw new Error("Uncaught (in promise): "+function(t){if(t&&t.toString===Object.prototype.toString){return(t.constructor&&t.constructor.name||"")+": "+JSON.stringify(t)}return t?t.toString():Object.prototype.toString.call(t)}(i)+(i&&i.stack?"\n"+i.stack:""))}catch(u){r=u}s&&(r.throwOriginal=!0),r.rejection=i,r.promise=t,r.zone=e.current,r.task=e.currentTask,a.push(r),n.scheduleMicroTask()}}}return t}const w=i("rejectionHandledHandler");function N(t){if(0===t[d]){try{const n=e[w];n&&"function"==typeof n&&n.call(this,{rejection:t[E],promise:t})}catch(n){}t[d]=S;for(let e=0;e<a.length;e++)t===a[e].promise&&a.splice(e,1)}}function R(t,e,n,r,o){N(t);const i=t[d],a=i?"function"==typeof r?r:f:"function"==typeof o?o:_;e.scheduleMicroTask("Promise.then",(()=>{try{const r=t[E],o=!!n&&g===n[g];o&&(n[v]=r,n[T]=i);const s=e.run(a,void 0,o&&a!==_&&a!==f?[]:[r]);P(n,!0,s)}catch(r){P(n,!1,r)}}),n)}const L=function(){},I=t.AggregateError;class C{static toString(){return"function ZoneAwarePromise() { [native code] }"}static resolve(t){return P(new this(null),m,t)}static reject(t){return P(new this(null),S,t)}static any(t){if(!t||"function"!=typeof t[Symbol.iterator])return Promise.reject(new I([],"All promises were rejected"));const e=[];let n=0;try{for(let r of t)n++,e.push(C.resolve(r))}catch(i){return Promise.reject(new I([],"All promises were rejected"))}if(0===n)return Promise.reject(new I([],"All promises were rejected"));let r=!1;const o=[];return new C(((t,i)=>{for(let a=0;a<e.length;a++)e[a].then((e=>{r||(r=!0,t(e))}),(t=>{o.push(t),n--,0===n&&(r=!0,i(new I(o,"All promises were rejected")))}))}))}static race(t){let e,n,r=new this(((t,r)=>{e=t,n=r}));function o(t){e(t)}function i(t){n(t)}for(let a of t)h(a)||(a=this.resolve(a)),a.then(o,i);return r}static all(t){return C.allWithCallback(t)}static allSettled(t){return(this&&this.prototype instanceof C?this:C).allWithCallback(t,{thenCallback:t=>({status:"fulfilled",value:t}),errorCallback:t=>({status:"rejected",reason:t})})}static allWithCallback(t,e){let n,r,o=new this(((t,e)=>{n=t,r=e})),i=2,a=0;const s=[];for(let u of t){h(u)||(u=this.resolve(u));const t=a;try{u.then((r=>{s[t]=e?e.thenCallback(r):r,i--,0===i&&n(s)}),(o=>{e?(s[t]=e.errorCallback(o),i--,0===i&&n(s)):r(o)}))}catch(c){r(c)}i++,a++}return i-=2,0===i&&n(s),o}constructor(t){const e=this;if(!(e instanceof C))throw new Error("Must be an instanceof Promise.");e[d]=y,e[E]=[];try{const n=b();t&&t(n(O(e,m)),n(O(e,S)))}catch(n){P(e,!1,n)}}get[Symbol.toStringTag](){return"Promise"}get[Symbol.species](){return C}then(t,n){var r;let o=null===(r=this.constructor)||void 0===r?void 0:r[Symbol.species];o&&"function"==typeof o||(o=this.constructor||C);const i=new o(L),a=e.current;return this[d]==y?this[E].push(a,i,t,n):R(this,a,i,t,n),i}catch(t){return this.then(null,t)}finally(t){var n;let r=null===(n=this.constructor)||void 0===n?void 0:n[Symbol.species];r&&"function"==typeof r||(r=C);const o=new r(L);o[g]=g;const i=e.current;return this[d]==y?this[E].push(i,o,t,t):R(this,i,o,t,t),o}}C.resolve=C.resolve,C.reject=C.reject,C.race=C.race,C.all=C.all;const k=t[c]=t.Promise;t.Promise=C;const D=i("thenPatched");function M(t){const e=t.prototype,n=r(e,"then");if(n&&(!1===n.writable||!n.configurable))return;const o=e.then;e[u]=o,t.prototype.then=function(t,e){return new C(((t,e)=>{o.call(this,t,e)})).then(t,e)},t[D]=!0}return n.patchThen=M,k&&(M(k),Rn(t,"fetch",(t=>{return e=t,function(t,n){let r=e.apply(t,n);if(r instanceof C)return r;let o=r.constructor;return o[D]||M(o),r};var e}))),Promise[e.__symbol__("uncaughtPromiseErrors")]=a,C})),
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
Zone.__load_patch("toString",(t=>{const e=Function.prototype.toString,n=fn("OriginalDelegate"),r=fn("Promise"),o=fn("Error"),i=function(){if("function"==typeof this){const i=this[n];if(i)return"function"==typeof i?e.call(i):Object.prototype.toString.call(i);if(this===Promise){const n=t[r];if(n)return e.call(n)}if(this===Error){const n=t[o];if(n)return e.call(n)}}return e.call(this)};i[n]=e,Function.prototype.toString=i;const a=Object.prototype.toString;Object.prototype.toString=function(){return"function"==typeof Promise&&this instanceof Promise?"[object Promise]":a.call(this)}}));
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
let Mn=!1;if("undefined"!=typeof window)try{const t=Object.defineProperty({},"passive",{get:function(){Mn=!0}});window.addEventListener("test",t,t),window.removeEventListener("test",t,t)}catch(br){Mn=!1}const xn={useG:!0},jn={},Un={},Zn=new RegExp("^"+ln+"(\\w+)(true|false)$"),Bn=fn("propagationStopped");function Gn(t,e){const n=(e?e(t):t)+"false",r=(e?e(t):t)+"true",o=ln+n,i=ln+r;jn[t]={},jn[t].false=o,jn[t].true=i}function zn(t,e,n,r){const o=r&&r.add||"addEventListener",i=r&&r.rm||"removeEventListener",a=r&&r.listeners||"eventListeners",s=r&&r.rmAll||"removeAllListeners",c=fn(o),u="."+o+":",l=function(t,e,n){if(t.isRemoved)return;const r=t.callback;let o;"object"==typeof r&&r.handleEvent&&(t.callback=t=>r.handleEvent(t),t.originalDelegate=r);try{t.invoke(t,e,[n])}catch(br){o=br}const a=t.options;if(a&&"object"==typeof a&&a.once){const r=t.originalDelegate?t.originalDelegate:t.callback;e[i].call(e,n.type,r,a)}return o};function p(n,r,o){if(!(r=r||t.event))return;const i=n||r.target||t,a=i[jn[r.type][o?"true":"false"]];if(a){const t=[];if(1===a.length){const e=l(a[0],i,r);e&&t.push(e)}else{const e=a.slice();for(let n=0;n<e.length&&(!r||!0!==r[Bn]);n++){const o=l(e[n],i,r);o&&t.push(o)}}if(1===t.length)throw t[0];for(let n=0;n<t.length;n++){const r=t[n];e.nativeScheduleMicroTask((()=>{throw r}))}}}const h=function(t){return p(this,t,!1)},f=function(t){return p(this,t,!0)};function _(e,n){if(!e)return!1;let r=!0;n&&void 0!==n.useG&&(r=n.useG);const l=n&&n.vh;let p=!0;n&&void 0!==n.chkDup&&(p=n.chkDup);let _=!1;n&&void 0!==n.rt&&(_=n.rt);let d=e;for(;d&&!d.hasOwnProperty(o);)d=on(d);if(!d&&e[o]&&(d=e),!d)return!1;if(d[c])return!1;const E=n&&n.eventNameToString,g={},v=d[c]=d[o],T=d[fn(i)]=d[i],y=d[fn(a)]=d[a],m=d[fn(s)]=d[s];let S;function O(t,e){return!Mn&&"object"==typeof t&&t?!!t.capture:Mn&&e?"boolean"==typeof t?{capture:t,passive:!0}:t?"object"==typeof t&&!1!==t.passive?Object.assign(Object.assign({},t),{passive:!0}):t:{passive:!0}:t}n&&n.prepend&&(S=d[fn(n.prepend)]=d[n.prepend]);const b=function(t){return S.call(g.target,g.eventName,t.invoke,g.options)},A=r?function(t){if(!g.isExisting)return v.call(g.target,g.eventName,g.capture?f:h,g.options)}:function(t){return v.call(g.target,g.eventName,t.invoke,g.options)},P=r?function(t){if(!t.isRemoved){const e=jn[t.eventName];let n;e&&(n=e[t.capture?"true":"false"]);const r=n&&t.target[n];if(r)for(let o=0;o<r.length;o++){if(r[o]===t){r.splice(o,1),t.isRemoved=!0,0===r.length&&(t.allRemoved=!0,t.target[n]=null);break}}}if(t.allRemoved)return T.call(t.target,t.eventName,t.capture?f:h,t.options)}:function(t){return T.call(t.target,t.eventName,t.invoke,t.options)},w=n&&n.diff?n.diff:function(t,e){const n=typeof e;return"function"===n&&t.callback===e||"object"===n&&t.originalDelegate===e},N=Zone[fn("UNPATCHED_EVENTS")],R=t[fn("PASSIVE_EVENTS")],L=function(e,o,i,a,s=!1,c=!1){return function(){const u=this||t;let h=arguments[0];n&&n.transferEventName&&(h=n.transferEventName(h));let f=arguments[1];if(!f)return e.apply(this,arguments);if(yn&&"uncaughtException"===h)return e.apply(this,arguments);let _=!1;if("function"!=typeof f){if(!f.handleEvent)return e.apply(this,arguments);_=!0}if(l&&!l(e,f,u,arguments))return;const d=Mn&&!!R&&-1!==R.indexOf(h),v=O(arguments[2],d);if(N)for(let t=0;t<N.length;t++)if(h===N[t])return d?e.call(u,h,f,v):e.apply(this,arguments);const T=!!v&&("boolean"==typeof v||v.capture),y=!(!v||"object"!=typeof v)&&v.once,m=Zone.current;let S=jn[h];S||(Gn(h,E),S=jn[h]);const b=S[T?"true":"false"];let A,P=u[b],L=!1;if(P){if(L=!0,p)for(let t=0;t<P.length;t++)if(w(P[t],f))return}else P=u[b]=[];const I=u.constructor.name,C=Un[I];C&&(A=C[h]),A||(A=I+o+(E?E(h):h)),g.options=v,y&&(g.options.once=!1),g.target=u,g.capture=T,g.eventName=h,g.isExisting=L;const k=r?xn:void 0;k&&(k.taskData=g);const D=m.scheduleEventTask(A,f,k,i,a);return g.target=null,k&&(k.taskData=null),y&&(v.once=!0),(Mn||"boolean"!=typeof D.options)&&(D.options=v),D.target=u,D.capture=T,D.eventName=h,_&&(D.originalDelegate=f),c?P.unshift(D):P.push(D),s?u:void 0}};return d[o]=L(v,u,A,P,_),S&&(d.prependListener=L(S,".prependListener:",b,P,_,!0)),d[i]=function(){const e=this||t;let r=arguments[0];n&&n.transferEventName&&(r=n.transferEventName(r));const o=arguments[2],i=!!o&&("boolean"==typeof o||o.capture),a=arguments[1];if(!a)return T.apply(this,arguments);if(l&&!l(T,a,e,arguments))return;const s=jn[r];let c;s&&(c=s[i?"true":"false"]);const u=c&&e[c];if(u)for(let t=0;t<u.length;t++){const n=u[t];if(w(n,a)){if(u.splice(t,1),n.isRemoved=!0,0===u.length&&(n.allRemoved=!0,e[c]=null,"string"==typeof r)){e[ln+"ON_PROPERTY"+r]=null}return n.zone.cancelTask(n),_?e:void 0}}return T.apply(this,arguments)},d[a]=function(){const e=this||t;let r=arguments[0];n&&n.transferEventName&&(r=n.transferEventName(r));const o=[],i=Fn(e,E?E(r):r);for(let t=0;t<i.length;t++){const e=i[t];let n=e.originalDelegate?e.originalDelegate:e.callback;o.push(n)}return o},d[s]=function(){const e=this||t;let r=arguments[0];if(r){n&&n.transferEventName&&(r=n.transferEventName(r));const t=jn[r];if(t){const n=t.false,o=t.true,a=e[n],s=e[o];if(a){const t=a.slice();for(let e=0;e<t.length;e++){const n=t[e];let o=n.originalDelegate?n.originalDelegate:n.callback;this[i].call(this,r,o,n.options)}}if(s){const t=s.slice();for(let e=0;e<t.length;e++){const n=t[e];let o=n.originalDelegate?n.originalDelegate:n.callback;this[i].call(this,r,o,n.options)}}}}else{const t=Object.keys(e);for(let e=0;e<t.length;e++){const n=t[e],r=Zn.exec(n);let o=r&&r[1];o&&"removeListener"!==o&&this[s].call(this,o)}this[s].call(this,"removeListener")}if(_)return this},In(d[o],v),In(d[i],T),m&&In(d[s],m),y&&In(d[a],y),!0}let d=[];for(let E=0;E<n.length;E++)d[E]=_(n[E],r);return d}function Fn(t,e){if(!e){const n=[];for(let r in t){const o=Zn.exec(r);let i=o&&o[1];if(i&&(!e||i===e)){const e=t[r];if(e)for(let t=0;t<e.length;t++)n.push(e[t])}}return n}let n=jn[e];n||(Gn(e),n=jn[e]);const r=t[n.false],o=t[n.true];return r?o?r.concat(o):r.slice():o?o.slice():[]}function Vn(t,e){const n=t.Event;n&&n.prototype&&e.patchMethod(n.prototype,"stopImmediatePropagation",(t=>function(e,n){e[Bn]=!0,t&&t.apply(e,n)}))}
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */function Hn(t,e,n,r,o){const i=Zone.__symbol__(r);if(e[i])return;const a=e[i]=e[r];e[r]=function(i,s,c){return s&&s.prototype&&o.forEach((function(e){const o=`${n}.${r}::`+e,i=s.prototype;try{if(i.hasOwnProperty(e)){const n=t.ObjectGetOwnPropertyDescriptor(i,e);n&&n.value?(n.value=t.wrapWithCurrentZone(n.value,o),t._redefineProperty(s.prototype,e,n)):i[e]&&(i[e]=t.wrapWithCurrentZone(i[e],o))}else i[e]&&(i[e]=t.wrapWithCurrentZone(i[e],o))}catch(a){}})),a.call(e,i,s,c)},t.attachOriginToPatched(e[r],a)}
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */function Kn(t,e,n){if(!n||0===n.length)return e;const r=n.filter((e=>e.target===t));if(!r||0===r.length)return e;const o=r[0].ignoreProperties;return e.filter((t=>-1===o.indexOf(t)))}function Xn(t,e,n,r){if(!t)return;Pn(t,Kn(t,e,n),r)}function Yn(t){return Object.getOwnPropertyNames(t).filter((t=>t.startsWith("on")&&t.length>2)).map((t=>t.substring(2)))}function Wn(t,e){if(yn&&!Sn)return;if(Zone[t.symbol("patchEvents")])return;const n=e.__Zone_ignore_on_properties;let r=[];if(mn){const t=window;r=r.concat(["Document","SVGElement","Element","HTMLElement","HTMLBodyElement","HTMLMediaElement","HTMLFrameSetElement","HTMLFrameElement","HTMLIFrameElement","HTMLMarqueeElement","Worker"]);const e=function(){try{const t=dn.navigator.userAgent;if(-1!==t.indexOf("MSIE ")||-1!==t.indexOf("Trident/"))return!0}catch(t){}return!1}()?[{target:t,ignoreProperties:["error"]}]:[];Xn(t,Yn(t),n?n.concat(e):n,on(t))}r=r.concat(["XMLHttpRequest","XMLHttpRequestEventTarget","IDBIndex","IDBRequest","IDBOpenDBRequest","IDBDatabase","IDBTransaction","IDBCursor","WebSocket"]);for(let o=0;o<r.length;o++){const t=e[r[o]];t&&t.prototype&&Xn(t.prototype,Yn(t.prototype),n)}}
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */Zone.__load_patch("util",((t,e,n)=>{const r=Yn(t);n.patchOnProperties=Pn,n.patchMethod=Rn,n.bindArguments=gn,n.patchMacroTask=Ln;const o=e.__symbol__("BLACK_LISTED_EVENTS"),i=e.__symbol__("UNPATCHED_EVENTS");t[i]&&(t[o]=t[i]),t[o]&&(e[o]=e[i]=t[o]),n.patchEventPrototype=Vn,n.patchEventTarget=zn,n.isIEOrEdge=Dn,n.ObjectDefineProperty=rn,n.ObjectGetOwnPropertyDescriptor=nn,n.ObjectCreate=an,n.ArraySlice=sn,n.patchClass=Nn,n.wrapWithCurrentZone=pn,n.filterProperties=Kn,n.attachOriginToPatched=In,n._redefineProperty=Object.defineProperty,n.patchCallbacks=Hn,n.getGlobalObjects=()=>({globalSources:Un,zoneSymbolEventNames:jn,eventNames:r,isBrowser:mn,isMix:Sn,isNode:yn,TRUE_STR:"true",FALSE_STR:"false",ZONE_SYMBOL_PREFIX:ln,ADD_EVENT_LISTENER_STR:"addEventListener",REMOVE_EVENT_LISTENER_STR:"removeEventListener"})}));
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
const qn=fn("zoneTask");function $n(t,e,n,r){let o=null,i=null;n+=r;const a={};function s(e){const n=e.data;return n.args[0]=function(){return e.invoke.apply(this,arguments)},n.handleId=o.apply(t,n.args),e}function c(e){return i.call(t,e.data.handleId)}o=Rn(t,e+=r,(n=>function(o,i){if("function"==typeof i[0]){const t={isPeriodic:"Interval"===r,delay:"Timeout"===r||"Interval"===r?i[1]||0:void 0,args:i},n=i[0];i[0]=function(){try{return n.apply(this,arguments)}finally{t.isPeriodic||("number"==typeof t.handleId?delete a[t.handleId]:t.handleId&&(t.handleId[qn]=null))}};const o=hn(e,i[0],t,s,c);if(!o)return o;const u=o.data.handleId;return"number"==typeof u?a[u]=o:u&&(u[qn]=o),u&&u.ref&&u.unref&&"function"==typeof u.ref&&"function"==typeof u.unref&&(o.ref=u.ref.bind(u),o.unref=u.unref.bind(u)),"number"==typeof u||u?u:o}return n.apply(t,i)})),i=Rn(t,n,(e=>function(n,r){const o=r[0];let i;"number"==typeof o?i=a[o]:(i=o&&o[qn],i||(i=o)),i&&"string"==typeof i.type?"notScheduled"!==i.state&&(i.cancelFn&&i.data.isPeriodic||0===i.runCount)&&("number"==typeof o?delete a[o]:o&&(o[qn]=null),i.zone.cancelTask(i)):e.apply(t,r)}))}
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
Zone.__load_patch("legacy",(t=>{const e=t[Zone.__symbol__("legacyPatch")];e&&e()})),Zone.__load_patch("queueMicrotask",((t,e,n)=>{n.patchMethod(t,"queueMicrotask",(t=>function(t,n){e.current.scheduleMicroTask("queueMicrotask",n[0])}))})),Zone.__load_patch("timers",(t=>{const e="set",n="clear";$n(t,e,n,"Timeout"),$n(t,e,n,"Interval"),$n(t,e,n,"Immediate")})),Zone.__load_patch("requestAnimationFrame",(t=>{$n(t,"request","cancel","AnimationFrame"),$n(t,"mozRequest","mozCancel","AnimationFrame"),$n(t,"webkitRequest","webkitCancel","AnimationFrame")})),Zone.__load_patch("blocking",((t,e)=>{const n=["alert","prompt","confirm"];for(let r=0;r<n.length;r++){Rn(t,n[r],((n,r,o)=>function(r,i){return e.current.run(n,t,i,o)}))}})),Zone.__load_patch("EventTarget",((t,e,n)=>{!function(t,e){e.patchEventPrototype(t,e)}(t,n),
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
function(t,e){if(Zone[e.symbol("patchEventTarget")])return;const{eventNames:n,zoneSymbolEventNames:r,TRUE_STR:o,FALSE_STR:i,ZONE_SYMBOL_PREFIX:a}=e.getGlobalObjects();for(let c=0;c<n.length;c++){const t=n[c],e=a+(t+i),s=a+(t+o);r[t]={},r[t][i]=e,r[t][o]=s}const s=t.EventTarget;s&&s.prototype&&e.patchEventTarget(t,e,[s&&s.prototype])}(t,n);const r=t.XMLHttpRequestEventTarget;r&&r.prototype&&n.patchEventTarget(t,n,[r.prototype])})),Zone.__load_patch("MutationObserver",((t,e,n)=>{Nn("MutationObserver"),Nn("WebKitMutationObserver")})),Zone.__load_patch("IntersectionObserver",((t,e,n)=>{Nn("IntersectionObserver")})),Zone.__load_patch("FileReader",((t,e,n)=>{Nn("FileReader")})),Zone.__load_patch("on_property",((t,e,n)=>{Wn(n,t)})),Zone.__load_patch("customElements",((t,e,n)=>{!function(t,e){const{isBrowser:n,isMix:r}=e.getGlobalObjects();if(!n&&!r||!t.customElements||!("customElements"in t))return;e.patchCallbacks(e,t.customElements,"customElements","define",["connectedCallback","disconnectedCallback","adoptedCallback","attributeChangedCallback"])}(t,n)})),Zone.__load_patch("XHR",((t,e)=>{!function(t){const c=t.XMLHttpRequest;if(!c)return;const u=c.prototype;let l=u[cn],p=u[un];if(!l){const e=t.XMLHttpRequestEventTarget;if(e){const t=e.prototype;l=t[cn],p=t[un]}}const h="readystatechange",f="scheduled";function _(t){const r=t.data,a=r.target;a[i]=!1,a[s]=!1;const c=a[o];l||(l=a[cn],p=a[un]),c&&p.call(a,h,c);const u=a[o]=()=>{if(a.readyState===a.DONE)if(!r.aborted&&a[i]&&t.state===f){const n=a[e.__symbol__("loadfalse")];if(0!==a.status&&n&&n.length>0){const o=t.invoke;t.invoke=function(){const n=a[e.__symbol__("loadfalse")];for(let e=0;e<n.length;e++)n[e]===t&&n.splice(e,1);r.aborted||t.state!==f||o.call(t)},n.push(t)}else t.invoke()}else r.aborted||!1!==a[i]||(a[s]=!0)};l.call(a,h,u);return a[n]||(a[n]=t),y.apply(a,r.args),a[i]=!0,t}function d(){}function E(t){const e=t.data;return e.aborted=!0,m.apply(e.target,e.args)}const g=Rn(u,"open",(()=>function(t,e){return t[r]=0==e[2],t[a]=e[1],g.apply(t,e)})),v=fn("fetchTaskAborting"),T=fn("fetchTaskScheduling"),y=Rn(u,"send",(()=>function(t,n){if(!0===e.current[T])return y.apply(t,n);if(t[r])return y.apply(t,n);{const e={target:t,url:t[a],isPeriodic:!1,args:n,aborted:!1},r=hn("XMLHttpRequest.send",d,e,_,E);t&&!0===t[s]&&!e.aborted&&r.state===f&&r.invoke()}})),m=Rn(u,"abort",(()=>function(t,r){const o=t[n];if(o&&"string"==typeof o.type){if(null==o.cancelFn||o.data&&o.data.aborted)return;o.zone.cancelTask(o)}else if(!0===e.current[v])return m.apply(t,r)}))}(t);const n=fn("xhrTask"),r=fn("xhrSync"),o=fn("xhrListener"),i=fn("xhrScheduled"),a=fn("xhrURL"),s=fn("xhrErrorBeforeScheduled")})),Zone.__load_patch("geolocation",(t=>{t.navigator&&t.navigator.geolocation&&function(t,e){const n=t.constructor.name;for(let r=0;r<e.length;r++){const o=e[r],i=t[o];if(i){if(!vn(nn(t,o)))continue;t[o]=(t=>{const e=function(){return t.apply(this,gn(arguments,n+"."+o))};return In(e,t),e})(i)}}}(t.navigator.geolocation,["getCurrentPosition","watchPosition"])})),Zone.__load_patch("PromiseRejectionEvent",((t,e)=>{function n(e){return function(n){Fn(t,e).forEach((r=>{const o=t.PromiseRejectionEvent;if(o){const t=new o(e,{promise:n.promise,reason:n.rejection});r.invoke(t)}}))}}t.PromiseRejectionEvent&&(e[fn("unhandledPromiseRejectionHandler")]=n("unhandledrejection"),e[fn("rejectionHandledHandler")]=n("rejectionhandled"))}));var Jn=function(){function t(t){void 0===t&&(t={}),this._isShutdown=!1,this._shuttingDownPromise=Promise.resolve(),this._sendingPromises=[],this.url=this.getDefaultUrl(t),"string"==typeof t.hostname&&(this.hostname=t.hostname),this.attributes=t.attributes,this.shutdown=this.shutdown.bind(this),this._concurrencyLimit="number"==typeof t.concurrencyLimit?t.concurrencyLimit:1/0,this.onInit(t)}return t.prototype.export=function(t,e){this._isShutdown?e({code:Kt.FAILED,error:new Error("Exporter has been shutdown")}):this._sendingPromises.length>=this._concurrencyLimit?e({code:Kt.FAILED,error:new Error("Concurrent export limit reached")}):this._export(t).then((function(){e({code:Kt.SUCCESS})})).catch((function(t){e({code:Kt.FAILED,error:t})}))},t.prototype._export=function(t){var e=this;return new Promise((function(n,r){try{pt.debug("items to be sent",t),e.send(t,n,r)}catch(o){r(o)}}))},t.prototype.shutdown=function(){var t=this;return this._isShutdown?(pt.debug("shutdown already started"),this._shuttingDownPromise):(this._isShutdown=!0,pt.debug("shutdown started"),this._shuttingDownPromise=new Promise((function(e,n){Promise.resolve().then((function(){return t.onShutdown()})).then((function(){return Promise.all(t._sendingPromises)})).then((function(){e()})).catch((function(t){n(t)}))})),this._shuttingDownPromise)},t}();var Qn,tr,er,nr,rr,or,ir,ar,sr=globalThis&&globalThis.__extends||function(){var t=function(e,n){return(t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(e,n)};return function(e,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();er=tr||(tr={}),nr=er.metrics||(er.metrics={}),rr=nr.v1||(nr.v1={}),(or=rr.AggregationTemporality||(rr.AggregationTemporality={}))[or.AGGREGATION_TEMPORALITY_UNSPECIFIED=0]="AGGREGATION_TEMPORALITY_UNSPECIFIED",or[or.AGGREGATION_TEMPORALITY_DELTA=1]="AGGREGATION_TEMPORALITY_DELTA",or[or.AGGREGATION_TEMPORALITY_CUMULATIVE=2]="AGGREGATION_TEMPORALITY_CUMULATIVE",function(t){var e,n,r;e=t.ConstantSampler||(t.ConstantSampler={}),(n=e.ConstantDecision||(e.ConstantDecision={}))[n.ALWAYS_OFF=0]="ALWAYS_OFF",n[n.ALWAYS_ON=1]="ALWAYS_ON",n[n.ALWAYS_PARENT=2]="ALWAYS_PARENT",function(t){t[t.SPAN_KIND_UNSPECIFIED=0]="SPAN_KIND_UNSPECIFIED",t[t.SPAN_KIND_INTERNAL=1]="SPAN_KIND_INTERNAL",t[t.SPAN_KIND_SERVER=2]="SPAN_KIND_SERVER",t[t.SPAN_KIND_CLIENT=3]="SPAN_KIND_CLIENT",t[t.SPAN_KIND_PRODUCER=4]="SPAN_KIND_PRODUCER",t[t.SPAN_KIND_CONSUMER=5]="SPAN_KIND_CONSUMER"}((r=t.Span||(t.Span={})).SpanKind||(r.SpanKind={}))}((ir=er.trace||(er.trace={})).v1||(ir.v1={})),function(t){var e;(e=t.ValueType||(t.ValueType={}))[e.STRING=0]="STRING",e[e.INT=1]="INT",e[e.DOUBLE=2]="DOUBLE",e[e.BOOL=3]="BOOL"}((ar=er.common||(er.common={})).v1||(ar.v1={}));var cr=function(t){function e(e,n,r){var o=t.call(this,e)||this;return o.name="OTLPExporterError",o.data=r,o.code=n,o}return sr(e,t),e}(Error),ur=((Qn={})[K.INTERNAL]=tr.trace.v1.Span.SpanKind.SPAN_KIND_INTERNAL,Qn[K.SERVER]=tr.trace.v1.Span.SpanKind.SPAN_KIND_SERVER,Qn[K.CLIENT]=tr.trace.v1.Span.SpanKind.SPAN_KIND_CLIENT,Qn[K.PRODUCER]=tr.trace.v1.Span.SpanKind.SPAN_KIND_PRODUCER,Qn[K.CONSUMER]=tr.trace.v1.Span.SpanKind.SPAN_KIND_CONSUMER,Qn),lr=globalThis&&globalThis.__assign||function(){return lr=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},lr.apply(this,arguments)};var pr=globalThis&&globalThis.__extends||function(){var t=function(e,n){return(t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(e,n)};return function(e,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),hr=function(t){function e(e){void 0===e&&(e={});var n=t.call(this,e)||this;return n._useXHR=!1,n._useXHR=!!e.headers||"function"!=typeof navigator.sendBeacon,n._useXHR?n._headers=Object.assign({},function(t){void 0===t&&(t={});var e={};return Object.entries(t).forEach((function(t){var n=t[0],r=t[1];void 0!==r?e[n]=String(r):pt.warn('Header "'+n+'" has wrong value and will be ignored')})),e}(e.headers),Et(Dt().OTEL_EXPORTER_OTLP_HEADERS)):n._headers={},n}return pr(e,t),e.prototype.onInit=function(){window.addEventListener("unload",this.shutdown)},e.prototype.onShutdown=function(){window.removeEventListener("unload",this.shutdown)},e.prototype.send=function(t,e,n){var r=this;if(this._isShutdown)pt.debug("Shutdown already started. Cannot send objects");else{var o=this.convert(t),i=JSON.stringify(o),a=new Promise((function(t,e){r._useXHR?function(t,e,n,r,o){var i=new XMLHttpRequest;i.open("POST",e),Object.entries(lr(lr({},{Accept:"application/json","Content-Type":"application/json"}),n)).forEach((function(t){var e=t[0],n=t[1];i.setRequestHeader(e,n)})),i.send(t),i.onreadystatechange=function(){if(i.readyState===XMLHttpRequest.DONE)if(i.status>=200&&i.status<=299)pt.debug("xhr success",t),r();else{var e=new cr("Failed to export with XHR (status: "+i.status+")",i.status);o(e)}}}(i,r.url,r._headers,t,e):function(t,e,n,r,o){navigator.sendBeacon(e,new Blob([t],n))?(pt.debug("sendBeacon - can send",t),r()):o(new cr("sendBeacon - cannot send "+t))}(i,r.url,{type:"application/json"},t,e)})).then(e,n);this._sendingPromises.push(a);var s=function(){var t=r._sendingPromises.indexOf(a);r._sendingPromises.splice(t,1)};a.then(s,s)}},e}(Jn);function fr(t){return Object.keys(t).map((function(e){return function(t,e){var n=_r(e);return{key:t,value:n}}(e,t[e])}))}function _r(t){var e={};return"string"==typeof t?e.stringValue=t:"boolean"==typeof t?e.boolValue=t:"number"==typeof t&&t<=2147483647&&t>=-2147483648&&Number.isInteger(t)?e.intValue=t:"number"==typeof t?e.doubleValue=t:Array.isArray(t)?e.arrayValue={values:t.map((function(t){return _r(t)}))}:t&&(e.kvlistValue={values:fr(t)}),e}function dr(t,e){return t.links.map((function(t){return{traceId:e?t.context.traceId:Mt(t.context.traceId),spanId:e?t.context.spanId:Mt(t.context.spanId),attributes:fr(t.attributes||{}),droppedAttributesCount:0}}))}function Er(t){var e={code:t.code};return void 0!==t.message&&(e.message=t.message),e}function gr(t,e){return void 0===e&&(e={}),{attributes:fr(Object.assign({},e,t?t.attributes:{})),droppedAttributesCount:0}}function vr(t){if(t)return t.serialize()}function Tr(t,e,n){var r=function(t){return t.reduce((function(t,e){var n=t.get(e.resource);n||(n=new Map,t.set(e.resource,n));var r=n.get(e.instrumentationLibrary);return r||(r=new Array,n.set(e.instrumentationLibrary,r)),r.push(e),t}),new Map)}(t);return{resourceSpans:mr(r,Object.assign({},e.attributes),n)}}function yr(t,e,n){return{spans:e.map((function(t){return function(t,e){return{traceId:e?t.spanContext().traceId:Mt(t.spanContext().traceId),spanId:e?t.spanContext().spanId:Mt(t.spanContext().spanId),parentSpanId:t.parentSpanId?e?t.parentSpanId:Mt(t.parentSpanId):void 0,traceState:vr(t.spanContext().traceState),name:t.name,kind:(r=t.kind,o=ur[r],"number"==typeof o?o:tr.trace.v1.Span.SpanKind.SPAN_KIND_UNSPECIFIED),startTimeUnixNano:Qt(t.startTime),endTimeUnixNano:Qt(t.endTime),attributes:fr(t.attributes),droppedAttributesCount:0,events:(n=t.events,n.map((function(t){return{timeUnixNano:Qt(t.time),name:t.name,attributes:fr(t.attributes||{}),droppedAttributesCount:0}}))),droppedEventsCount:0,status:Er(t.status),links:dr(t,e),droppedLinksCount:0};var n,r,o}(t,n)})),instrumentationLibrary:t}}function mr(t,e,n){return Array.from(t,(function(t){var r=t[0],o=t[1];return{resource:gr(r,e),instrumentationLibrarySpans:Array.from(o,(function(t){return yr(t[0],t[1],n)}))}}))}var Sr=globalThis&&globalThis.__extends||function(){var t=function(e,n){return(t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(e,n)};return function(e,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),Or=function(t){function e(e){void 0===e&&(e={});var n=t.call(this,e)||this;return n._headers=Object.assign(n._headers,Et(Dt().OTEL_EXPORTER_OTLP_TRACES_HEADERS)),n}return Sr(e,t),e.prototype.convert=function(t){return Tr(t,this,!0)},e.prototype.getDefaultUrl=function(t){return"string"==typeof t.url?t.url:Dt().OTEL_EXPORTER_OTLP_TRACES_ENDPOINT.length>0?Dt().OTEL_EXPORTER_OTLP_TRACES_ENDPOINT:Dt().OTEL_EXPORTER_OTLP_ENDPOINT.length>0?(e=Dt().OTEL_EXPORTER_OTLP_ENDPOINT,n="/v1/traces",e.match(/v\d\/(traces|metrics)$/)?e:e+n):"http://localhost:55681/v1/traces";var e,n},e}(hr);export{Or as O,Be as R,Vt as S,Qe as W,tn as Z,We as a,ut as t};
