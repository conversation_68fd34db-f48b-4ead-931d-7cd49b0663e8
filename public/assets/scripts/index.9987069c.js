import{W as e,R as t,S as n,a as o,O as r,Z as i,t as a}from"./vendor.4c9b4c60.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver((e=>{for(const n of e)if("childList"===n.type)for(const e of n.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)})).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerpolicy&&(t.referrerPolicy=e.referrerpolicy),"use-credentials"===e.crossorigin?t.credentials="include":"anonymous"===e.crossorigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();const s=globalThis.sessionStorage,l=new e({resource:new t({[n.SERVICE_NAME]:"eoh-customer-portal"})});l.addSpanProcessor(new o(new r({url:"https://oo.infra.opsfolio.com/api/default/v1/traces",headers:{Authorization:"Basic ********************************************************"}}))),l.register({contextManager:new i}),a.setGlobalTracerProvider(l);const c=a.getTracer("web-tracer");function d(e){const t=document.cookie.match(new RegExp(`(?:^|; )${e.replace(/([\\.$?*|{}()\[\]\\/+^])/g,"\\$1")}=([^;]*)`));return t?decodeURIComponent(t[1]):null}function u(){return{username:d("zitadel_user_name")||"Anonymous",email:d("zitadel_user_email")||"<EMAIL>",userId:d("zitadel_user_id")||"unknown-user",organizationId:d("zitadel_tenant_id")||"unknown-organization",userRole:d("zitadel_user_role")||"unknown-user-role"}}function m(e,t={}){return{...u(),url:window.location.href,pageTitle:document.title,userAgent:navigator.userAgent,sessionId:(null==s?void 0:s.getItem("sessionId"))||null,timestamp:Date.now(),title:e||"No Title",...t&&{details:JSON.stringify(t)}}}function g(e,t){try{const n=c.startSpan(e);n.setAttributes(t),n.end()}catch(n){console.error(`Error sending telemetry for ${e}:`,n)}}document.addEventListener("DOMContentLoaded",(()=>{const e=m("Page Load");null==s||s.setItem("pageLoadTime",Date.now().toString()),g("documentLoad",e)})),window.addEventListener("beforeunload",(()=>{const e=Date.now(),t=Number(s.getItem("pageLoadTime"))||e,n=e-t,o=m("Time Spent on Page");o.timeSpentMs=n,o.startTime=new Date(t).toISOString(),o.endTime=new Date(e).toISOString(),g("time-on-page",o)})),document.addEventListener("click",(e=>{var t;const n=e.target.closest("button, a, [data-trackable]");if(!n)return;g("element-click",m(`Click on ${n.tagName}`,{url:window.location.href,title:n.getAttribute("title")||(null==(t=n.textContent)?void 0:t.trim())||"No Title"}))})),globalThis.setAttributes=m,globalThis.setOTTracer=g,globalThis.getUserMetadata=u;
