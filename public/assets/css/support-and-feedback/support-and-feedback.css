/* Widget styles */
.github-contact-us-widget {
  position: fixed;
  right: -64px;
  top: 50%;
  background-color: #000;
  color: white;
  padding: 10px 20px;
  cursor: pointer;
  border-radius: 5px 5px 0 0;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  transform: rotate(270deg);
}

.github-contact-us-widget-popup {
  display: none;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: white;
  width: 400px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  padding: 30px;
  border-radius: 8px;
  z-index: 1000;
}

.github-contact-us-widget-popup h2 {
  margin: 0 0 20px;
  font-size: 22px;
  text-align: center;
}

.github-contact-us-widget-popup label {
  display: block;
  margin: 10px 0 5px;
  font-size: 14px;
}

.github-contact-us-widget-popup input,
.github-contact-us-widget-popup textarea {
  width: calc(100%);
  padding: 8px;
  margin-bottom: 15px;
  border: 1px solid #ccc;
  border-radius: 4px;
  display: block;
  margin: 0 auto 15px;
  margin-left: 0px!important;
  margin-right: 0px!important;
}

.github-contact-us-widget-popup input[readonly] {
  background-color: #f5f5f5;
  color: #555;
  border: 1px solid #ccc;
  cursor: not-allowed;
}

.github-contact-us-widget-popup input[readonly]:focus {
  outline: none;
  box-shadow: none;
}
.github-contact-us-widget-popup .attach-screenshot-checkbox {
  display: inline-block;
  margin-top: 11px;
  width: 20px;
  height: 20px;
  margin-right: 4px;
}
.github-contact-us-widget-popup textarea {
  resize: none;
}

.github-contact-us-widget-popup button {
  padding: 10px 15px;
  background-color: #000;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  width: calc(100%);
  margin: 0 auto;
  display: block;
}

.github-contact-us-widget-popup button:hover {
  background-color: #000;
}

/* Overlay for popup */
.github-contact-us-widget-popup-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
}

/* Success message container */
.github-contact-us-widget-success-notification {
  background-color: #4caf50;
  color: white;
  padding: 6px 12px;
  border-radius: 5px;
  font-size: 16px;
  font-weight: normal;
  text-align: center;
  width: 100%;
}

.github-contact-us-loader {
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid #3498db;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
}
.dark .github-contact-us-widget-popup{
  background-color:   #374151!important;
  color: #f9fafb;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
