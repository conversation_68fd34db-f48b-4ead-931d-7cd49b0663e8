
<!-- ⚠️ DEMO DATA NOTICE: This is synthetic demonstration data created for EOH theme showcase purposes. All test results, names, and references are fictional and designed for educational use only. -->
<h1>Test Report: TC-EOH-SEC-0146</h1>
    
    <p><b>Title:</b> verify that field length validation is applied to the Title field (3 to 50 characters) in the Create Report popup</p>
    <p><b>Status:</b> <span class="status-passed">PASSED</span></p>
    <p><b>Start Time:</b> 2024-07-03T05:08:03.136Z</p>
    <p><b>End Time:</b> 2024-07-03T05:08:36.304Z</p>
    <p><b>Total Duration:</b> 33.17 seconds</p>
    
      <h2>Test Steps</h2>
      <table border="1" cellspacing="0" cellpadding="5">
        <tr>
          <th>Step</th>
          <th>Name</th>
          <th>Status</th>
          <th>Start Time</th>
          <th>End Time</th>
        </tr>
        
          <tr>
            <td>1</td>
            <td>Navigate to the URL and ensure the page loads successfully</td>
            <td class="status-passed">passed</td>
            <td>2024-07-03T05:08:03.140Z</td>
            <td>2024-07-03T05:08:06.048Z</td>
          </tr>
          <tr>
            <td>2</td>
            <td>Perform login with valid credentials</td>
            <td class="status-passed">passed</td>
            <td>2024-07-03T05:08:06.052Z</td>
            <td>2024-07-03T05:08:14.452Z</td>
          </tr>
          <tr>
            <td>3</td>
            <td>Select 'Demo Organization Inc.' as the tenant name from the top-level tenant selection dropdown</td>
            <td class="status-passed">passed</td>
            <td>2024-07-03T05:08:14.506Z</td>
            <td>2024-07-03T05:08:16.542Z</td>
          </tr>
          <tr>
            <td>4</td>
            <td>Navigate to the review Page</td>
            <td class="status-passed">passed</td>
            <td>2024-07-03T05:08:16.544Z</td>
            <td>2024-07-03T05:08:18.440Z</td>
          </tr>
          <tr>
            <td>5</td>
            <td>Navigate to the AICPA Page</td>
            <td class="status-passed">passed</td>
            <td>2024-07-03T05:08:18.442Z</td>
            <td>2024-07-03T05:08:19.108Z</td>
          </tr>
          <tr>
            <td>6</td>
            <td>Verify SOC2 Type I link navigation</td>
            <td class="status-passed">passed</td>
            <td>2024-07-03T05:08:19.108Z</td>
            <td>2024-07-03T05:08:21.490Z</td>
          </tr>
          <tr>
            <td>7</td>
            <td>Search for review session</td>
            <td class="status-passed">passed</td>
            <td>2024-07-03T05:08:21.491Z</td>
            <td>2024-07-03T05:08:21.513Z</td>
          </tr>
          <tr>
            <td>8</td>
            <td>Click the View button</td>
            <td class="status-passed">passed</td>
            <td>2024-07-03T05:08:21.514Z</td>
            <td>2024-07-03T05:08:25.469Z</td>
          </tr>
          <tr>
            <td>9</td>
            <td>Click the Document Tab</td>
            <td class="status-passed">passed</td>
            <td>2024-07-03T05:08:25.469Z</td>
            <td>2024-07-03T05:08:27.520Z</td>
          </tr>
          <tr>
            <td>10</td>
            <td>Click the Create Report button</td>
            <td class="status-passed">passed</td>
            <td>2024-07-03T05:08:27.522Z</td>
            <td>2024-07-03T05:08:29.588Z</td>
          </tr>
          <tr>
            <td>11</td>
            <td>Validation check for title with less than 3 characters</td>
            <td class="status-passed">passed</td>
            <td>2024-07-03T05:08:29.589Z</td>
            <td>2024-07-03T05:08:31.832Z</td>
          </tr>
          <tr>
            <td>12</td>
            <td>Validation check for title with more than 50 characters</td>
            <td class="status-passed">passed</td>
            <td>2024-07-03T05:08:31.834Z</td>
            <td>2024-07-03T05:08:34.005Z</td>
          </tr>
          <tr>
            <td>13</td>
            <td>Validation check for title with valid title between 3 and 50 characters</td>
            <td class="status-passed">passed</td>
            <td>2024-07-03T05:08:34.006Z</td>
            <td>2024-07-03T05:08:36.196Z</td>
          </tr>
          <tr>
            <td>14</td>
            <td>Close the browser</td>
            <td class="status-passed">passed</td>
            <td>2024-07-03T05:08:36.196Z</td>
            <td>2024-07-03T05:08:36.315Z</td>
          </tr>
      </table>