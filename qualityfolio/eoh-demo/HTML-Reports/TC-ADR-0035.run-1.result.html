<h1>Test Report: TC-ADR-0035</h1>
    
    <p><b>Title:</b> Check whether after adding a member Add a member popup auto closes or not</p>
    <p><b>Status:</b> <span class="status-passed">PASSED</span></p>
    <p><b>Start Time:</b> 2025-01-23T05:58:28.077Z</p>
    <p><b>End Time:</b> 2025-01-23T05:59:03.226Z</p>
    <p><b>Total Duration:</b> 35.15 seconds</p>
    
      <h2>Test Steps</h2>
      <table border="1" cellspacing="0" cellpadding="5">
        <tr>
          <th>Step</th>
          <th>Name</th>
          <th>Status</th>
          <th>Start Time</th>
          <th>End Time</th>
        </tr>
        
          <tr>
            <td>1</td>
            <td>Navigate to the URL and ensure the page loads successfully</td>
            <td class="status-passed">passed</td>
            <td>2025-01-23T05:58:28.084Z</td>
            <td>2025-01-23T05:58:33.937Z</td>
          </tr>
          <tr>
            <td>2</td>
            <td>Log in with valid  admin credentials. </td>
            <td class="status-passed">passed</td>
            <td>2025-01-23T05:58:33.949Z</td>
            <td>2025-01-23T05:58:34.103Z</td>
          </tr>
          <tr>
            <td>3</td>
            <td>Perform login with valid credentials</td>
            <td class="status-passed">passed</td>
            <td>2025-01-23T05:58:34.104Z</td>
            <td>2025-01-23T05:58:48.414Z</td>
          </tr>
          <tr>
            <td>4</td>
            <td>Navigate to Admin Dashboard</td>
            <td class="status-passed">passed</td>
            <td>2025-01-23T05:58:48.490Z</td>
            <td>2025-01-23T05:58:54.946Z</td>
          </tr>
          <tr>
            <td>5</td>
            <td>Search for audit session</td>
            <td class="status-passed">passed</td>
            <td>2025-01-23T05:58:54.947Z</td>
            <td>2025-01-23T05:58:56.382Z</td>
          </tr>
          <tr>
            <td>6</td>
            <td>Click the View button</td>
            <td class="status-passed">passed</td>
            <td>2025-01-23T05:58:56.383Z</td>
            <td>2025-01-23T05:58:57.673Z</td>
          </tr>
          <tr>
            <td>7</td>
            <td>Verify session title</td>
            <td class="status-passed">passed</td>
            <td>2025-01-23T05:58:57.674Z</td>
            <td>2025-01-23T05:58:58.042Z</td>
          </tr>
          <tr>
            <td>8</td>
            <td>Navigate to the Members tab</td>
            <td class="status-passed">passed</td>
            <td>2025-01-23T05:58:58.044Z</td>
            <td>2025-01-23T05:58:58.113Z</td>
          </tr>
          <tr>
            <td>9</td>
            <td>Open the Add a Member popup</td>
            <td class="status-passed">passed</td>
            <td>2025-01-23T05:58:58.115Z</td>
            <td>2025-01-23T05:58:59.047Z</td>
          </tr>
          <tr>
            <td>10</td>
            <td>Press the 'Add a member' button from the user's list</td>
            <td class="status-passed">passed</td>
            <td>2025-01-23T05:58:59.048Z</td>
            <td>2025-01-23T05:59:00.081Z</td>
          </tr>
          <tr>
            <td>11</td>
            <td>Validate the functionality of the 'Ok' button in the Add a Member confirmation popup</td>
            <td class="status-passed">passed</td>
            <td>2025-01-23T05:59:00.084Z</td>
            <td>2025-01-23T05:59:02.570Z</td>
          </tr>
          <tr>
            <td>12</td>
            <td>Confirm that the Add a Member popup closes automatically.</td>
            <td class="status-passed">passed</td>
            <td>2025-01-23T05:59:02.572Z</td>
            <td>2025-01-23T05:59:02.760Z</td>
          </tr>
          <tr>
            <td>13</td>
            <td>Close the browser</td>
            <td class="status-passed">passed</td>
            <td>2025-01-23T05:59:02.762Z</td>
            <td>2025-01-23T05:59:03.250Z</td>
          </tr>
      </table>