
<!-- ⚠️ DEMO DATA NOTICE: This is synthetic demonstration data created for EOH theme showcase purposes. All test results, names, and references are fictional and designed for educational use only. -->
<h1>Test Report: TC-EOH-PERF-0048</h1>
    <p><b>Run ID:</b> TR-EOH-PERF-0048</p>
    <p><b>Title:</b> Verify that when clicking the 'Layout And Style Checklist' in the Software Construction Checklist expanded list, app navigates to the corresponding Layout And Style Checklist or not</p>
    <p><b>Status:</b> <span class="status-passed">PASSED</span></p>
    <p><b>Start Time:</b> 2024-02-14T11:02:50.592Z</p>
    <p><b>End Time:</b> 2024-02-14T11:03:43.214Z</p>
    <p><b>Total Duration:</b> 52.62 seconds</p>
    
      <h2>Test Steps</h2>
      <table border="1" cellspacing="0" cellpadding="5">
        <tr>
          <th>Step</th>
          <th>Name</th>
          <th>Status</th>
          <th>Start Time</th>
          <th>End Time</th>
        </tr>
        
          <tr>
            <td>1</td>
            <td>Navigate to the URL and ensure the page loads successfully</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T11:02:50.593Z</td>
            <td>2024-02-14T11:03:01.821Z</td>
          </tr>
          <tr>
            <td>2</td>
            <td>Perform login with valid credentials</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T11:03:01.824Z</td>
            <td>2024-02-14T11:03:23.901Z</td>
          </tr>
          <tr>
            <td>3</td>
            <td>Select 'Demo Organization Inc.' as the tenant name from the top-level tenant selection dropdown</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T11:03:23.902Z</td>
            <td>2024-02-14T11:03:24.050Z</td>
          </tr>
          <tr>
            <td>4</td>
            <td>Click on the 'Policies and Evidence' menu</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T11:03:24.051Z</td>
            <td>2024-02-14T11:03:26.576Z</td>
          </tr>
          <tr>
            <td>5</td>
            <td>Click on the 'Code Quality Infrastructure' tab</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T11:03:26.577Z</td>
            <td>2024-02-14T11:03:32.371Z</td>
          </tr>
          <tr>
            <td>6</td>
            <td>Click on the 'Software construction checklist' expansion arrow in the Code Quality Infrastructure page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T11:03:32.372Z</td>
            <td>2024-02-14T11:03:34.426Z</td>
          </tr>
          <tr>
            <td>7</td>
            <td>Click on the 'Layout And Style Checklist' control under Software construction checklist</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T11:03:34.427Z</td>
            <td>2024-02-14T11:03:40.527Z</td>
          </tr>
          <tr>
            <td>8</td>
            <td>Check whether the page navigates to the 'Layout And Style Checklist' page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T11:03:40.528Z</td>
            <td>2024-02-14T11:03:42.281Z</td>
          </tr>
          <tr>
            <td>9</td>
            <td>Close-browser</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T11:03:42.282Z</td>
            <td>2024-02-14T11:03:43.237Z</td>
          </tr>
      </table>