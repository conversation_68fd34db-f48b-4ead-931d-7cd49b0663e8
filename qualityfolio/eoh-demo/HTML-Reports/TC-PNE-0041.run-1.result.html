
<!-- ⚠️ DEMO DATA NOTICE: This is synthetic demonstration data created for EOH theme showcase purposes. All test results, names, and references are fictional and designed for educational use only. -->
<h1>Test Report: TC-EOH-PERF-0041</h1>
    <p><b>Run ID:</b> TR-EOH-PERF-0041</p>
    <p><b>Title:</b> Verify that when clicking the 'Developer Testing Checklist' in the Software Construction Checklist expanded list, app navigates to the corresponding Developer Testing Checklist or not</p>
    <p><b>Status:</b> <span class="status-passed">PASSED</span></p>
    <p><b>Start Time:</b> 2024-02-14T10:54:50.016Z</p>
    <p><b>End Time:</b> 2024-02-14T10:55:55.876Z</p>
    <p><b>Total Duration:</b> 65.86 seconds</p>
    
      <h2>Test Steps</h2>
      <table border="1" cellspacing="0" cellpadding="5">
        <tr>
          <th>Step</th>
          <th>Name</th>
          <th>Status</th>
          <th>Start Time</th>
          <th>End Time</th>
        </tr>
        
          <tr>
            <td>1</td>
            <td>Navigate to the URL and ensure the page loads successfully</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:54:50.041Z</td>
            <td>2024-02-14T10:55:04.094Z</td>
          </tr>
          <tr>
            <td>2</td>
            <td>Perform login with valid credentials</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:55:04.106Z</td>
            <td>2024-02-14T10:55:26.215Z</td>
          </tr>
          <tr>
            <td>3</td>
            <td>Select 'Demo Organization Inc.' as the tenant name from the top-level tenant selection dropdown</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:55:26.226Z</td>
            <td>2024-02-14T10:55:26.464Z</td>
          </tr>
          <tr>
            <td>4</td>
            <td>Click on the 'Policies and Evidence' menu</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:55:26.575Z</td>
            <td>2024-02-14T10:55:30.060Z</td>
          </tr>
          <tr>
            <td>5</td>
            <td>Click on the 'Code Quality Infrastructure' tab</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:55:30.062Z</td>
            <td>2024-02-14T10:55:36.053Z</td>
          </tr>
          <tr>
            <td>6</td>
            <td>Click on the 'Software construction checklist' expansion arrow in the Code Quality Infrastructure page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:55:36.055Z</td>
            <td>2024-02-14T10:55:40.373Z</td>
          </tr>
          <tr>
            <td>7</td>
            <td>Click on the 'Developer Testing Checklist' control under Software construction checklist</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:55:40.374Z</td>
            <td>2024-02-14T10:55:49.075Z</td>
          </tr>
          <tr>
            <td>8</td>
            <td>Check whether the page navigates to the 'Developer Testing Checklist' page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:55:49.078Z</td>
            <td>2024-02-14T10:55:50.031Z</td>
          </tr>
          <tr>
            <td>9</td>
            <td>Close-browser</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:55:50.032Z</td>
            <td>2024-02-14T10:55:55.937Z</td>
          </tr>
      </table>