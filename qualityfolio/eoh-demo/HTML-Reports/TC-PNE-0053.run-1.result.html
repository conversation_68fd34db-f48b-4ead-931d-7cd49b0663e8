
<!-- ⚠️ DEMO DATA NOTICE: This is synthetic demonstration data created for EOH theme showcase purposes. All test results, names, and references are fictional and designed for educational use only. -->
<h1>Test Report: TC-EOH-PERF-0053</h1>
    <p><b>Run ID:</b> TR-EOH-PERF-0053</p>
    <p><b>Title:</b> Verify that when clicking the 'Refactoring Checklist' in the Software Construction Checklist expanded list, app navigates to the corresponding Refactoring Checklist or not</p>
    <p><b>Status:</b> <span class="status-passed">PASSED</span></p>
    <p><b>Start Time:</b> 2024-02-14T11:52:13.679Z</p>
    <p><b>End Time:</b> 2024-02-14T11:52:56.448Z</p>
    <p><b>Total Duration:</b> 42.77 seconds</p>
    
      <h2>Test Steps</h2>
      <table border="1" cellspacing="0" cellpadding="5">
        <tr>
          <th>Step</th>
          <th>Name</th>
          <th>Status</th>
          <th>Start Time</th>
          <th>End Time</th>
        </tr>
        
          <tr>
            <td>1</td>
            <td>Navigate to the URL and ensure the page loads successfully</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T11:52:13.688Z</td>
            <td>2024-02-14T11:52:21.374Z</td>
          </tr>
          <tr>
            <td>2</td>
            <td>Perform login with valid credentials</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T11:52:21.385Z</td>
            <td>2024-02-14T11:52:42.089Z</td>
          </tr>
          <tr>
            <td>3</td>
            <td>Select 'Demo Organization Inc.' as the tenant name from the top-level tenant selection dropdown</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T11:52:42.094Z</td>
            <td>2024-02-14T11:52:42.297Z</td>
          </tr>
          <tr>
            <td>4</td>
            <td>Click on the 'Policies and Evidence' menu</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T11:52:42.332Z</td>
            <td>2024-02-14T11:52:44.345Z</td>
          </tr>
          <tr>
            <td>5</td>
            <td>Click on the 'Code Quality Infrastructure' tab</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T11:52:44.346Z</td>
            <td>2024-02-14T11:52:49.071Z</td>
          </tr>
          <tr>
            <td>6</td>
            <td>Click on the 'Software construction checklist' expansion arrow in the Code Quality Infrastructure page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T11:52:49.072Z</td>
            <td>2024-02-14T11:52:51.030Z</td>
          </tr>
          <tr>
            <td>7</td>
            <td>Click on the 'Refactoring Checklist' control under Software construction checklist</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T11:52:51.031Z</td>
            <td>2024-02-14T11:52:55.576Z</td>
          </tr>
          <tr>
            <td>8</td>
            <td>Check whether the page navigates to the 'Refactoring Checklist' page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T11:52:55.577Z</td>
            <td>2024-02-14T11:52:56.066Z</td>
          </tr>
          <tr>
            <td>9</td>
            <td>Close-browser</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T11:52:56.067Z</td>
            <td>2024-02-14T11:52:56.473Z</td>
          </tr>
      </table>