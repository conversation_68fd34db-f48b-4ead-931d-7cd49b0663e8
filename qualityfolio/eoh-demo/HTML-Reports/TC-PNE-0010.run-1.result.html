
<!-- ⚠️ DEMO DATA NOTICE: This is synthetic demonstration data created for EOH theme showcase purposes. All test results, names, and references are fictional and designed for educational use only. -->
<h1>Test Report: TC-EOH-PERF-0010</h1>
    <p><b>Run ID:</b> TR-EOH-PERF-0010</p>
    <p><b>Title:</b> Verify that when clicking the 'Code Coverage Policy' in the Code Quality expanded list, app navigates to the corresponding Code Coverage Policy or not</p>
    <p><b>Status:</b> <span class="status-passed">PASSED</span></p>
    <p><b>Start Time:</b> 2024-02-14T04:39:01.995Z</p>
    <p><b>End Time:</b> 2024-02-14T04:39:54.354Z</p>
    <p><b>Total Duration:</b> 52.36 seconds</p>
    
      <h2>Test Steps</h2>
      <table border="1" cellspacing="0" cellpadding="5">
        <tr>
          <th>Step</th>
          <th>Name</th>
          <th>Status</th>
          <th>Start Time</th>
          <th>End Time</th>
        </tr>
        
          <tr>
            <td>1</td>
            <td>Navigate to the URL and ensure the page loads successfully</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:39:02.010Z</td>
            <td>2024-02-14T04:39:12.595Z</td>
          </tr>
          <tr>
            <td>2</td>
            <td>Perform login with valid credentials</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:39:12.608Z</td>
            <td>2024-02-14T04:39:36.626Z</td>
          </tr>
          <tr>
            <td>3</td>
            <td>Select 'Demo Organization Inc.' as the tenant name from the top-level tenant selection dropdown</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:39:36.631Z</td>
            <td>2024-02-14T04:39:36.841Z</td>
          </tr>
          <tr>
            <td>4</td>
            <td>Click on the 'Policies and Evidence' menu</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:39:36.873Z</td>
            <td>2024-02-14T04:39:40.086Z</td>
          </tr>
          <tr>
            <td>5</td>
            <td>Click on the 'Code Quality Infrastructure' tab</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:39:40.087Z</td>
            <td>2024-02-14T04:39:45.317Z</td>
          </tr>
          <tr>
            <td>6</td>
            <td>Click on the 'Code Quality' expansion arrow in the Code Quality Infrastructure page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:39:45.318Z</td>
            <td>2024-02-14T04:39:48.577Z</td>
          </tr>
          <tr>
            <td>7</td>
            <td>Click on the 'Code Coverage Policy' control under code quality</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:39:48.578Z</td>
            <td>2024-02-14T04:39:52.136Z</td>
          </tr>
          <tr>
            <td>8</td>
            <td>Check whether the page navigates to the 'Code Coverage Policy' page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:39:52.137Z</td>
            <td>2024-02-14T04:39:52.812Z</td>
          </tr>
          <tr>
            <td>9</td>
            <td>Close-browser</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:39:52.816Z</td>
            <td>2024-02-14T04:39:54.407Z</td>
          </tr>
      </table>