
<!-- ⚠️ DEMO DATA NOTICE: This is synthetic demonstration data created for EOH theme showcase purposes. All test results, names, and references are fictional and designed for educational use only. -->
<h1>Test Report: TC-EOH-PERF-0052</h1>
    <p><b>Run ID:</b> TR-EOH-PERF-0052</p>
    <p><b>Title:</b> Verify that when clicking the 'Pseudocode Programming Process Checklist' in the Software Construction Checklist expanded list, app navigates to the corresponding Pseudocode Programming Process Checklist or not</p>
    <p><b>Status:</b> <span class="status-passed">PASSED</span></p>
    <p><b>Start Time:</b> 2024-02-14T10:56:44.212Z</p>
    <p><b>End Time:</b> 2024-02-14T10:57:45.129Z</p>
    <p><b>Total Duration:</b> 60.92 seconds</p>
    
      <h2>Test Steps</h2>
      <table border="1" cellspacing="0" cellpadding="5">
        <tr>
          <th>Step</th>
          <th>Name</th>
          <th>Status</th>
          <th>Start Time</th>
          <th>End Time</th>
        </tr>
        
          <tr>
            <td>1</td>
            <td>Navigate to the URL and ensure the page loads successfully</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:56:44.214Z</td>
            <td>2024-02-14T10:57:01.853Z</td>
          </tr>
          <tr>
            <td>2</td>
            <td>Perform login with valid credentials</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:57:01.854Z</td>
            <td>2024-02-14T10:57:20.267Z</td>
          </tr>
          <tr>
            <td>3</td>
            <td>Select 'Demo Organization Inc.' as the tenant name from the top-level tenant selection dropdown</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:57:20.271Z</td>
            <td>2024-02-14T10:57:20.427Z</td>
          </tr>
          <tr>
            <td>4</td>
            <td>Click on the 'Policies and Evidence' menu</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:57:20.428Z</td>
            <td>2024-02-14T10:57:25.219Z</td>
          </tr>
          <tr>
            <td>5</td>
            <td>Click on the 'Code Quality Infrastructure' tab</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:57:25.220Z</td>
            <td>2024-02-14T10:57:32.823Z</td>
          </tr>
          <tr>
            <td>6</td>
            <td>Click on the 'Software construction checklist' expansion arrow in the Code Quality Infrastructure page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:57:32.854Z</td>
            <td>2024-02-14T10:57:34.818Z</td>
          </tr>
          <tr>
            <td>7</td>
            <td>Click on the 'Pseudocode Programming Process Checklist' control under Software construction checklist</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:57:34.819Z</td>
            <td>2024-02-14T10:57:38.916Z</td>
          </tr>
          <tr>
            <td>8</td>
            <td>Check whether the page navigates to the 'Pseudocode Programming Process Checklist' page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:57:38.919Z</td>
            <td>2024-02-14T10:57:40.649Z</td>
          </tr>
          <tr>
            <td>9</td>
            <td>Close-browser</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:57:40.649Z</td>
            <td>2024-02-14T10:57:45.142Z</td>
          </tr>
      </table>