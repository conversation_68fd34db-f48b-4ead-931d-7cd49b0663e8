<h1>Test Report: TC-AUD-0137</h1>
    
    <p><b>Title:</b> Verify that To Do comments containing either a checkbox or a due date in the format due:YYYY-MM-DD are correctly listed under the Action tab</p>
    <p><b>Status:</b> <span class="status-passed">PASSED</span></p>
    <p><b>Start Time:</b> 2025-07-01T11:42:10.226Z</p>
    <p><b>End Time:</b> 2025-07-01T11:42:56.554Z</p>
    <p><b>Total Duration:</b> 46.33 seconds</p>
    
      <h2>Test Steps</h2>
      <table border="1" cellspacing="0" cellpadding="5">
        <tr>
          <th>Step</th>
          <th>Name</th>
          <th>Status</th>
          <th>Start Time</th>
          <th>End Time</th>
        </tr>
        
          <tr>
            <td>1</td>
            <td>Navigate to the URL and ensure the page loads successfully</td>
            <td class="status-passed">passed</td>
            <td>2025-07-01T11:42:10.231Z</td>
            <td>2025-07-01T11:42:14.019Z</td>
          </tr>
          <tr>
            <td>2</td>
            <td>Perform login with valid credentials</td>
            <td class="status-passed">passed</td>
            <td>2025-07-01T11:42:14.023Z</td>
            <td>2025-07-01T11:42:21.063Z</td>
          </tr>
          <tr>
            <td>3</td>
            <td>Select 'Netspective' as the tenant name from the top-level tenant selection dropdown</td>
            <td class="status-passed">passed</td>
            <td>2025-07-01T11:42:21.116Z</td>
            <td>2025-07-01T11:42:23.146Z</td>
          </tr>
          <tr>
            <td>4</td>
            <td>Navigate to the Audit Page</td>
            <td class="status-passed">passed</td>
            <td>2025-07-01T11:42:23.149Z</td>
            <td>2025-07-01T11:42:25.161Z</td>
          </tr>
          <tr>
            <td>5</td>
            <td>Navigate to the AICPA Page</td>
            <td class="status-passed">passed</td>
            <td>2025-07-01T11:42:25.163Z</td>
            <td>2025-07-01T11:42:25.704Z</td>
          </tr>
          <tr>
            <td>6</td>
            <td>Verify SOC2 Type I link navigation</td>
            <td class="status-passed">passed</td>
            <td>2025-07-01T11:42:25.705Z</td>
            <td>2025-07-01T11:42:28.086Z</td>
          </tr>
          <tr>
            <td>7</td>
            <td>Search for audit session</td>
            <td class="status-passed">passed</td>
            <td>2025-07-01T11:42:28.086Z</td>
            <td>2025-07-01T11:42:28.104Z</td>
          </tr>
          <tr>
            <td>8</td>
            <td>Click the View button</td>
            <td class="status-passed">passed</td>
            <td>2025-07-01T11:42:28.105Z</td>
            <td>2025-07-01T11:42:32.092Z</td>
          </tr>
          <tr>
            <td>9</td>
            <td>Go to Document (Interaction) Tab</td>
            <td class="status-passed">passed</td>
            <td>2025-07-01T11:42:32.092Z</td>
            <td>2025-07-01T11:42:33.181Z</td>
          </tr>
          <tr>
            <td>10</td>
            <td>Submit comment: Audit planning</td>
            <td class="status-passed">passed</td>
            <td>2025-07-01T11:42:33.183Z</td>
            <td>2025-07-01T11:42:34.274Z</td>
          </tr>
          <tr>
            <td>11</td>
            <td>Check Action tab for absence of first comment</td>
            <td class="status-passed">passed</td>
            <td>2025-07-01T11:42:34.274Z</td>
            <td>2025-07-01T11:42:37.558Z</td>
          </tr>
          <tr>
            <td>12</td>
            <td>page.click(//button[@id='interaction-tab'])</td>
            <td class="status-passed">passed</td>
            <td>2025-07-01T11:42:37.558Z</td>
            <td>2025-07-01T11:42:37.591Z</td>
          </tr>
          <tr>
            <td>13</td>
            <td>page.waitForTimeout</td>
            <td class="status-passed">passed</td>
            <td>2025-07-01T11:42:37.592Z</td>
            <td>2025-07-01T11:42:39.592Z</td>
          </tr>
          <tr>
            <td>14</td>
            <td>Submit comment: This task is due: 2024-12-31</td>
            <td class="status-passed">passed</td>
            <td>2025-07-01T11:42:39.592Z</td>
            <td>2025-07-01T11:42:40.652Z</td>
          </tr>
          <tr>
            <td>15</td>
            <td>Check Action tab for presence of second comment</td>
            <td class="status-passed">passed</td>
            <td>2025-07-01T11:42:40.653Z</td>
            <td>2025-07-01T11:42:46.392Z</td>
          </tr>
          <tr>
            <td>16</td>
            <td>page.click(//button[@id='interaction-tab'])</td>
            <td class="status-passed">passed</td>
            <td>2025-07-01T11:42:46.393Z</td>
            <td>2025-07-01T11:42:46.435Z</td>
          </tr>
          <tr>
            <td>17</td>
            <td>page.waitForTimeout</td>
            <td class="status-passed">passed</td>
            <td>2025-07-01T11:42:46.437Z</td>
            <td>2025-07-01T11:42:47.444Z</td>
          </tr>
          <tr>
            <td>18</td>
            <td>Submit comment:  - [x] Task with checkbox and due:2024-12-31</td>
            <td class="status-passed">passed</td>
            <td>2025-07-01T11:42:47.444Z</td>
            <td>2025-07-01T11:42:50.502Z</td>
          </tr>
          <tr>
            <td>19</td>
            <td>Check Action tab for presence of second comment</td>
            <td class="status-passed">passed</td>
            <td>2025-07-01T11:42:50.504Z</td>
            <td>2025-07-01T11:42:51.616Z</td>
          </tr>
          <tr>
            <td>20</td>
            <td>page.click(//button[@id='interaction-tab'])</td>
            <td class="status-passed">passed</td>
            <td>2025-07-01T11:42:51.621Z</td>
            <td>2025-07-01T11:42:51.654Z</td>
          </tr>
          <tr>
            <td>21</td>
            <td>page.waitForTimeout</td>
            <td class="status-passed">passed</td>
            <td>2025-07-01T11:42:51.655Z</td>
            <td>2025-07-01T11:42:52.656Z</td>
          </tr>
          <tr>
            <td>22</td>
            <td>Submit comment:  - [x] Only checkbox</td>
            <td class="status-passed">passed</td>
            <td>2025-07-01T11:42:52.658Z</td>
            <td>2025-07-01T11:42:53.749Z</td>
          </tr>
          <tr>
            <td>23</td>
            <td>Check Action tab for presence of second comment</td>
            <td class="status-passed">passed</td>
            <td>2025-07-01T11:42:53.749Z</td>
            <td>2025-07-01T11:42:56.466Z</td>
          </tr>
          <tr>
            <td>24</td>
            <td>Close the browser</td>
            <td class="status-passed">passed</td>
            <td>2025-07-01T11:42:56.467Z</td>
            <td>2025-07-01T11:42:56.568Z</td>
          </tr>
      </table>