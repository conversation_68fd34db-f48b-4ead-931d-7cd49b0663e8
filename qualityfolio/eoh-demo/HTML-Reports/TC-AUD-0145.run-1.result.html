<h1>Test Report: TC-AUD-0145</h1>
    
    <p><b>Title:</b> Verify that it is not possible to generate report with duplication title</p>
    <p><b>Status:</b> <span class="status-passed">PASSED</span></p>
    <p><b>Start Time:</b> 2025-07-03T03:41:07.754Z</p>
    <p><b>End Time:</b> 2025-07-03T03:41:39.539Z</p>
    <p><b>Total Duration:</b> 31.79 seconds</p>
    
      <h2>Test Steps</h2>
      <table border="1" cellspacing="0" cellpadding="5">
        <tr>
          <th>Step</th>
          <th>Name</th>
          <th>Status</th>
          <th>Start Time</th>
          <th>End Time</th>
        </tr>
        
          <tr>
            <td>1</td>
            <td>Navigate to the URL and ensure the page loads successfully</td>
            <td class="status-passed">passed</td>
            <td>2025-07-03T03:41:07.758Z</td>
            <td>2025-07-03T03:41:10.604Z</td>
          </tr>
          <tr>
            <td>2</td>
            <td>Perform login with valid credentials</td>
            <td class="status-passed">passed</td>
            <td>2025-07-03T03:41:10.609Z</td>
            <td>2025-07-03T03:41:18.093Z</td>
          </tr>
          <tr>
            <td>3</td>
            <td>Select 'Netspective' as the tenant name from the top-level tenant selection dropdown</td>
            <td class="status-passed">passed</td>
            <td>2025-07-03T03:41:18.149Z</td>
            <td>2025-07-03T03:41:20.184Z</td>
          </tr>
          <tr>
            <td>4</td>
            <td>Navigate to the Audit Page</td>
            <td class="status-passed">passed</td>
            <td>2025-07-03T03:41:20.185Z</td>
            <td>2025-07-03T03:41:22.033Z</td>
          </tr>
          <tr>
            <td>5</td>
            <td>Navigate to the AICPA Page</td>
            <td class="status-passed">passed</td>
            <td>2025-07-03T03:41:22.035Z</td>
            <td>2025-07-03T03:41:22.662Z</td>
          </tr>
          <tr>
            <td>6</td>
            <td>Verify SOC2 Type I link navigation</td>
            <td class="status-passed">passed</td>
            <td>2025-07-03T03:41:22.662Z</td>
            <td>2025-07-03T03:41:25.029Z</td>
          </tr>
          <tr>
            <td>7</td>
            <td>Search for audit session</td>
            <td class="status-passed">passed</td>
            <td>2025-07-03T03:41:25.029Z</td>
            <td>2025-07-03T03:41:25.048Z</td>
          </tr>
          <tr>
            <td>8</td>
            <td>Click the View button</td>
            <td class="status-passed">passed</td>
            <td>2025-07-03T03:41:25.049Z</td>
            <td>2025-07-03T03:41:28.996Z</td>
          </tr>
          <tr>
            <td>9</td>
            <td>Click the Document Tab</td>
            <td class="status-passed">passed</td>
            <td>2025-07-03T03:41:28.997Z</td>
            <td>2025-07-03T03:41:31.048Z</td>
          </tr>
          <tr>
            <td>10</td>
            <td>Copy the name of existing report</td>
            <td class="status-passed">passed</td>
            <td>2025-07-03T03:41:31.050Z</td>
            <td>2025-07-03T03:41:33.064Z</td>
          </tr>
          <tr>
            <td>11</td>
            <td>Click the Create Report button</td>
            <td class="status-passed">passed</td>
            <td>2025-07-03T03:41:33.064Z</td>
            <td>2025-07-03T03:41:35.167Z</td>
          </tr>
          <tr>
            <td>12</td>
            <td>Fill the Report Title field</td>
            <td class="status-passed">passed</td>
            <td>2025-07-03T03:41:35.168Z</td>
            <td>2025-07-03T03:41:35.205Z</td>
          </tr>
          <tr>
            <td>13</td>
            <td>Check that the Access Type checkbox is -public</td>
            <td class="status-passed">passed</td>
            <td>2025-07-03T03:41:35.205Z</td>
            <td>2025-07-03T03:41:35.215Z</td>
          </tr>
          <tr>
            <td>14</td>
            <td>Select Certificate Template option</td>
            <td class="status-passed">passed</td>
            <td>2025-07-03T03:41:35.215Z</td>
            <td>2025-07-03T03:41:35.232Z</td>
          </tr>
          <tr>
            <td>15</td>
            <td>Select Auditor option</td>
            <td class="status-passed">passed</td>
            <td>2025-07-03T03:41:35.236Z</td>
            <td>2025-07-03T03:41:35.256Z</td>
          </tr>
          <tr>
            <td>16</td>
            <td>Fill the Report Date field</td>
            <td class="status-passed">passed</td>
            <td>2025-07-03T03:41:35.258Z</td>
            <td>2025-07-03T03:41:35.294Z</td>
          </tr>
          <tr>
            <td>17</td>
            <td>Select Report Template option</td>
            <td class="status-passed">passed</td>
            <td>2025-07-03T03:41:35.294Z</td>
            <td>2025-07-03T03:41:35.315Z</td>
          </tr>
          <tr>
            <td>18</td>
            <td>Click the Submit button</td>
            <td class="status-passed">passed</td>
            <td>2025-07-03T03:41:35.316Z</td>
            <td>2025-07-03T03:41:39.430Z</td>
          </tr>
          <tr>
            <td>19</td>
            <td>Verify that the error message for duplicated title is displayed</td>
            <td class="status-passed">passed</td>
            <td>2025-07-03T03:41:39.432Z</td>
            <td>2025-07-03T03:41:39.442Z</td>
          </tr>
          <tr>
            <td>20</td>
            <td>Close the browser</td>
            <td class="status-passed">passed</td>
            <td>2025-07-03T03:41:39.443Z</td>
            <td>2025-07-03T03:41:39.550Z</td>
          </tr>
      </table>