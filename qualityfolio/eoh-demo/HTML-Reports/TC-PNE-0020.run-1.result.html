
<!-- ⚠️ DEMO DATA NOTICE: This is synthetic demonstration data created for EOH theme showcase purposes. All test results, names, and references are fictional and designed for educational use only. -->
<h1>Test Report: TC-EOH-PERF-0020</h1>
    <p><b>Run ID:</b> TR-EOH-PERF-0020</p>
    <p><b>Title:</b> Verify that when clicking the 'IDE Extensions Policy' in the Code Quality expanded list, app navigates to the corresponding IDE Extensions Policy or not</p>
    <p><b>Status:</b> <span class="status-passed">PASSED</span></p>
    <p><b>Start Time:</b> 2024-02-14T04:40:49.050Z</p>
    <p><b>End Time:</b> 2024-02-14T04:41:45.897Z</p>
    <p><b>Total Duration:</b> 56.85 seconds</p>
    
      <h2>Test Steps</h2>
      <table border="1" cellspacing="0" cellpadding="5">
        <tr>
          <th>Step</th>
          <th>Name</th>
          <th>Status</th>
          <th>Start Time</th>
          <th>End Time</th>
        </tr>
        
          <tr>
            <td>1</td>
            <td>Navigate to the URL and ensure the page loads successfully</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:40:49.071Z</td>
            <td>2024-02-14T04:41:00.593Z</td>
          </tr>
          <tr>
            <td>2</td>
            <td>Perform login with valid credentials</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:41:00.605Z</td>
            <td>2024-02-14T04:41:23.214Z</td>
          </tr>
          <tr>
            <td>3</td>
            <td>Select 'Demo Organization Inc.' as the tenant name from the top-level tenant selection dropdown</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:41:23.215Z</td>
            <td>2024-02-14T04:41:24.435Z</td>
          </tr>
          <tr>
            <td>4</td>
            <td>Click on the 'Policies and Evidence' menu</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:41:24.459Z</td>
            <td>2024-02-14T04:41:29.659Z</td>
          </tr>
          <tr>
            <td>5</td>
            <td>Click on the 'Code Quality Infrastructure' tab</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:41:29.660Z</td>
            <td>2024-02-14T04:41:35.717Z</td>
          </tr>
          <tr>
            <td>6</td>
            <td>Click on the 'Code Quality' expansion arrow in the Code Quality Infrastructure page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:41:35.719Z</td>
            <td>2024-02-14T04:41:39.646Z</td>
          </tr>
          <tr>
            <td>7</td>
            <td>Click on the 'IDE Extensions Policy' control under code quality</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:41:39.647Z</td>
            <td>2024-02-14T04:41:42.814Z</td>
          </tr>
          <tr>
            <td>8</td>
            <td>Check whether the page navigates to the 'IDE Extensions Policy' page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:41:42.815Z</td>
            <td>2024-02-14T04:41:43.765Z</td>
          </tr>
          <tr>
            <td>9</td>
            <td>Close-browser</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:41:43.769Z</td>
            <td>2024-02-14T04:41:45.956Z</td>
          </tr>
      </table>