
<!-- ⚠️ DEMO DATA NOTICE: This is synthetic demonstration data created for EOH theme showcase purposes. All test results, names, and references are fictional and designed for educational use only. -->
<h1>Test Report: TC-EOH-PERF-0058</h1>
    <p><b>Run ID:</b> TR-EOH-PERF-0058</p>
    <p><b>Title:</b> Verify that when clicking the 'Unusual Data Types Checklist' in the Software Construction Checklist expanded list, app navigates to the corresponding Unusual Data Types Checklist or not</p>
    <p><b>Status:</b> <span class="status-passed">PASSED</span></p>
    <p><b>Start Time:</b> 2024-02-14T11:03:43.243Z</p>
    <p><b>End Time:</b> 2024-02-14T11:04:27.299Z</p>
    <p><b>Total Duration:</b> 44.06 seconds</p>
    
      <h2>Test Steps</h2>
      <table border="1" cellspacing="0" cellpadding="5">
        <tr>
          <th>Step</th>
          <th>Name</th>
          <th>Status</th>
          <th>Start Time</th>
          <th>End Time</th>
        </tr>
        
          <tr>
            <td>1</td>
            <td>Navigate to the URL and ensure the page loads successfully</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T11:03:43.245Z</td>
            <td>2024-02-14T11:03:51.202Z</td>
          </tr>
          <tr>
            <td>2</td>
            <td>Perform login with valid credentials</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T11:03:51.204Z</td>
            <td>2024-02-14T11:04:12.183Z</td>
          </tr>
          <tr>
            <td>3</td>
            <td>Select 'Demo Organization Inc.' as the tenant name from the top-level tenant selection dropdown</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T11:04:12.186Z</td>
            <td>2024-02-14T11:04:12.334Z</td>
          </tr>
          <tr>
            <td>4</td>
            <td>Click on the 'Policies and Evidence' menu</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T11:04:12.334Z</td>
            <td>2024-02-14T11:04:14.929Z</td>
          </tr>
          <tr>
            <td>5</td>
            <td>Click on the 'Code Quality Infrastructure' tab</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T11:04:14.930Z</td>
            <td>2024-02-14T11:04:20.379Z</td>
          </tr>
          <tr>
            <td>6</td>
            <td>Click on the 'Software construction checklist' expansion arrow in the Code Quality Infrastructure page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T11:04:20.380Z</td>
            <td>2024-02-14T11:04:22.414Z</td>
          </tr>
          <tr>
            <td>7</td>
            <td>Click on the 'Unusual Data Types Checklist' control under Software construction checklist</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T11:04:22.415Z</td>
            <td>2024-02-14T11:04:26.079Z</td>
          </tr>
          <tr>
            <td>8</td>
            <td>Check whether the page navigates to the 'Unusual Data Types Checklist' page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T11:04:26.081Z</td>
            <td>2024-02-14T11:04:26.978Z</td>
          </tr>
          <tr>
            <td>9</td>
            <td>Close-browser</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T11:04:26.979Z</td>
            <td>2024-02-14T11:04:27.309Z</td>
          </tr>
      </table>