
<!-- ⚠️ DEMO DATA NOTICE: This is synthetic demonstration data created for EOH theme showcase purposes. All test results, names, and references are fictional and designed for educational use only. -->
<h1>Test Report: TC-EOH-PERF-0051</h1>
    <p><b>Run ID:</b> TR-EOH-PERF-0051</p>
    <p><b>Title:</b> Verify that when clicking the 'Programming Tools Checklist' in the Software Construction Checklist expanded list, app navigates to the corresponding Programming Tools Checklist or not</p>
    <p><b>Status:</b> <span class="status-passed">PASSED</span></p>
    <p><b>Start Time:</b> 2024-02-14T10:56:44.558Z</p>
    <p><b>End Time:</b> 2024-02-14T10:57:39.077Z</p>
    <p><b>Total Duration:</b> 54.52 seconds</p>
    
      <h2>Test Steps</h2>
      <table border="1" cellspacing="0" cellpadding="5">
        <tr>
          <th>Step</th>
          <th>Name</th>
          <th>Status</th>
          <th>Start Time</th>
          <th>End Time</th>
        </tr>
        
          <tr>
            <td>1</td>
            <td>Navigate to the URL and ensure the page loads successfully</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:56:44.573Z</td>
            <td>2024-02-14T10:56:55.691Z</td>
          </tr>
          <tr>
            <td>2</td>
            <td>Perform login with valid credentials</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:56:55.705Z</td>
            <td>2024-02-14T10:57:19.180Z</td>
          </tr>
          <tr>
            <td>3</td>
            <td>Select 'Demo Organization Inc.' as the tenant name from the top-level tenant selection dropdown</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:57:19.182Z</td>
            <td>2024-02-14T10:57:19.477Z</td>
          </tr>
          <tr>
            <td>4</td>
            <td>Click on the 'Policies and Evidence' menu</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:57:19.516Z</td>
            <td>2024-02-14T10:57:22.416Z</td>
          </tr>
          <tr>
            <td>5</td>
            <td>Click on the 'Code Quality Infrastructure' tab</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:57:22.417Z</td>
            <td>2024-02-14T10:57:29.279Z</td>
          </tr>
          <tr>
            <td>6</td>
            <td>Click on the 'Software construction checklist' expansion arrow in the Code Quality Infrastructure page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:57:29.280Z</td>
            <td>2024-02-14T10:57:33.046Z</td>
          </tr>
          <tr>
            <td>7</td>
            <td>Click on the 'Programming Tools Checklist' control under Software construction checklist</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:57:33.047Z</td>
            <td>2024-02-14T10:57:36.720Z</td>
          </tr>
          <tr>
            <td>8</td>
            <td>Check whether the page navigates to the 'Programming Tools Checklist' page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:57:36.722Z</td>
            <td>2024-02-14T10:57:38.208Z</td>
          </tr>
          <tr>
            <td>9</td>
            <td>Close-browser</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:57:38.210Z</td>
            <td>2024-02-14T10:57:39.126Z</td>
          </tr>
      </table>