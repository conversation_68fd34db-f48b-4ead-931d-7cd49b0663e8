
<!-- ⚠️ DEMO DATA NOTICE: This is synthetic demonstration data created for EOH theme showcase purposes. All test results, names, and references are fictional and designed for educational use only. -->
<h1>Test Report: TC-EOH-review-0007</h1>
    
    <p><b>Title:</b> Check whether Assigned Engagements can be sorted in ascending or descending order using the due date sorting option</p>
    <p><b>Status:</b> <span class="status-passed">PASSED</span></p>
    <p><b>Start Time:</b> 2024-03-17T09:49:46.926Z</p>
    <p><b>End Time:</b> 2024-03-17T09:50:06.112Z</p>
    <p><b>Total Duration:</b> 19.19 seconds</p>
    
      <h2>Test Steps</h2>
      <table border="1" cellspacing="0" cellpadding="5">
        <tr>
          <th>Step</th>
          <th>Name</th>
          <th>Status</th>
          <th>Start Time</th>
          <th>End Time</th>
        </tr>
        
          <tr>
            <td>1</td>
            <td>Navigate to the URL and ensure the page loads successfully</td>
            <td class="status-passed">passed</td>
            <td>2024-03-17T09:49:46.930Z</td>
            <td>2024-03-17T09:49:50.277Z</td>
          </tr>
          <tr>
            <td>2</td>
            <td>Perform login with valid credentials</td>
            <td class="status-passed">passed</td>
            <td>2024-03-17T09:49:50.285Z</td>
            <td>2024-03-17T09:50:02.748Z</td>
          </tr>
          <tr>
            <td>3</td>
            <td>Select 'Demo Organization Inc.' as the tenant name from the top-level tenant selection dropdown</td>
            <td class="status-passed">passed</td>
            <td>2024-03-17T09:50:02.765Z</td>
            <td>2024-03-17T09:50:05.872Z</td>
          </tr>
          <tr>
            <td>4</td>
            <td>Check whether the due date column is in ascending order on the dashboard</td>
            <td class="status-passed">passed</td>
            <td>2024-03-17T09:50:05.875Z</td>
            <td>2024-03-17T09:50:05.956Z</td>
          </tr>
          <tr>
            <td>5</td>
            <td>Sort the due date column in descending order</td>
            <td class="status-passed">passed</td>
            <td>2024-03-17T09:50:05.956Z</td>
            <td>2024-03-17T09:50:06.021Z</td>
          </tr>
          <tr>
            <td>6</td>
            <td>Close the browser</td>
            <td class="status-passed">passed</td>
            <td>2024-03-17T09:50:06.021Z</td>
            <td>2024-03-17T09:50:06.122Z</td>
          </tr>
      </table>