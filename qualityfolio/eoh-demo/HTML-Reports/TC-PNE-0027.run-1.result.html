
<!-- ⚠️ DEMO DATA NOTICE: This is synthetic demonstration data created for EOH theme showcase purposes. All test results, names, and references are fictional and designed for educational use only. -->
<h1>Test Report: TC-EOH-PERF-0027</h1>
    <p><b>Run ID:</b> TR-EOH-PERF-0027</p>
    <p><b>Title:</b> Verify that when clicking the 'Source Control README Policy' in the Code Quality expanded list, app navigates to the corresponding Source Control README Policy or not</p>
    <p><b>Status:</b> <span class="status-passed">PASSED</span></p>
    <p><b>Start Time:</b> 2024-02-14T04:42:24.931Z</p>
    <p><b>End Time:</b> 2024-02-14T04:43:20.553Z</p>
    <p><b>Total Duration:</b> 55.62 seconds</p>
    
      <h2>Test Steps</h2>
      <table border="1" cellspacing="0" cellpadding="5">
        <tr>
          <th>Step</th>
          <th>Name</th>
          <th>Status</th>
          <th>Start Time</th>
          <th>End Time</th>
        </tr>
        
          <tr>
            <td>1</td>
            <td>Navigate to the URL and ensure the page loads successfully</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:42:24.933Z</td>
            <td>2024-02-14T04:42:43.375Z</td>
          </tr>
          <tr>
            <td>2</td>
            <td>Perform login with valid credentials</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:42:43.377Z</td>
            <td>2024-02-14T04:43:03.330Z</td>
          </tr>
          <tr>
            <td>3</td>
            <td>Select 'Demo Organization Inc.' as the tenant name from the top-level tenant selection dropdown</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:43:03.333Z</td>
            <td>2024-02-14T04:43:03.626Z</td>
          </tr>
          <tr>
            <td>4</td>
            <td>Click on the 'Policies and Evidence' menu</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:43:03.627Z</td>
            <td>2024-02-14T04:43:06.685Z</td>
          </tr>
          <tr>
            <td>5</td>
            <td>Click on the 'Code Quality Infrastructure' tab</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:43:06.686Z</td>
            <td>2024-02-14T04:43:11.375Z</td>
          </tr>
          <tr>
            <td>6</td>
            <td>Click on the 'Code Quality' expansion arrow in the Code Quality Infrastructure page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:43:11.377Z</td>
            <td>2024-02-14T04:43:14.969Z</td>
          </tr>
          <tr>
            <td>7</td>
            <td>Click on the 'Source Control README Policy' control under code quality</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:43:14.970Z</td>
            <td>2024-02-14T04:43:18.372Z</td>
          </tr>
          <tr>
            <td>8</td>
            <td>Check whether the page navigates to the 'Source Control README Policy' page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:43:18.373Z</td>
            <td>2024-02-14T04:43:19.366Z</td>
          </tr>
          <tr>
            <td>9</td>
            <td>Close-browser</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:43:19.366Z</td>
            <td>2024-02-14T04:43:20.567Z</td>
          </tr>
      </table>