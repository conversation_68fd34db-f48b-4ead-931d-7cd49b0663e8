
<!-- ⚠️ DEMO DATA NOTICE: This is synthetic demonstration data created for EOH theme showcase purposes. All test results, names, and references are fictional and designed for educational use only. -->
<h1>Test Report: TC-EOH-PERF-0039</h1>
    <p><b>Run ID:</b> TR-EOH-PERF-0039</p>
    <p><b>Title:</b> Verify that when clicking the 'Defensive Programming Checklist' in the Software Construction Checklist expanded list, app navigates to the corresponding Defensive Programming Checklist or not</p>
    <p><b>Status:</b> <span class="status-passed">PASSED</span></p>
    <p><b>Start Time:</b> 2024-02-14T10:53:59.030Z</p>
    <p><b>End Time:</b> 2024-02-14T10:55:09.628Z</p>
    <p><b>Total Duration:</b> 70.60 seconds</p>
    
      <h2>Test Steps</h2>
      <table border="1" cellspacing="0" cellpadding="5">
        <tr>
          <th>Step</th>
          <th>Name</th>
          <th>Status</th>
          <th>Start Time</th>
          <th>End Time</th>
        </tr>
        
          <tr>
            <td>1</td>
            <td>Navigate to the URL and ensure the page loads successfully</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:53:59.033Z</td>
            <td>2024-02-14T10:54:18.253Z</td>
          </tr>
          <tr>
            <td>2</td>
            <td>Perform login with valid credentials</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:54:18.256Z</td>
            <td>2024-02-14T10:54:48.120Z</td>
          </tr>
          <tr>
            <td>3</td>
            <td>Select 'Demo Organization Inc.' as the tenant name from the top-level tenant selection dropdown</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:54:48.122Z</td>
            <td>2024-02-14T10:54:48.458Z</td>
          </tr>
          <tr>
            <td>4</td>
            <td>Click on the 'Policies and Evidence' menu</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:54:48.460Z</td>
            <td>2024-02-14T10:54:54.312Z</td>
          </tr>
          <tr>
            <td>5</td>
            <td>Click on the 'Code Quality Infrastructure' tab</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:54:54.313Z</td>
            <td>2024-02-14T10:54:59.246Z</td>
          </tr>
          <tr>
            <td>6</td>
            <td>Click on the 'Software construction checklist' expansion arrow in the Code Quality Infrastructure page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:54:59.247Z</td>
            <td>2024-02-14T10:55:03.118Z</td>
          </tr>
          <tr>
            <td>7</td>
            <td>Click on the 'Defensive Programming Checklist' control under Software construction checklist</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:55:03.119Z</td>
            <td>2024-02-14T10:55:07.228Z</td>
          </tr>
          <tr>
            <td>8</td>
            <td>Check whether the page navigates to the 'Defensive Programming Checklist' page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:55:07.230Z</td>
            <td>2024-02-14T10:55:08.294Z</td>
          </tr>
          <tr>
            <td>9</td>
            <td>Close-browser</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:55:08.295Z</td>
            <td>2024-02-14T10:55:09.641Z</td>
          </tr>
      </table>