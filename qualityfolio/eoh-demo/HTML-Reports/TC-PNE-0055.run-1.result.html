
<!-- ⚠️ DEMO DATA NOTICE: This is synthetic demonstration data created for EOH theme showcase purposes. All test results, names, and references are fictional and designed for educational use only. -->
<h1>Test Report: TC-EOH-PERF-0055</h1>
    <p><b>Run ID:</b> TR-EOH-PERF-0055</p>
    <p><b>Title:</b> Verify that when clicking the 'Software Quality Landscape Checklist' in the Software Construction Checklist expanded list, app navigates to the corresponding or not</p>
    <p><b>Status:</b> <span class="status-passed">PASSED</span></p>
    <p><b>Start Time:</b> 2024-02-14T10:57:11.339Z</p>
    <p><b>End Time:</b> 2024-02-14T10:58:04.057Z</p>
    <p><b>Total Duration:</b> 52.72 seconds</p>
    
      <h2>Test Steps</h2>
      <table border="1" cellspacing="0" cellpadding="5">
        <tr>
          <th>Step</th>
          <th>Name</th>
          <th>Status</th>
          <th>Start Time</th>
          <th>End Time</th>
        </tr>
        
          <tr>
            <td>1</td>
            <td>Navigate to the URL and ensure the page loads successfully</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:57:11.340Z</td>
            <td>2024-02-14T10:57:19.665Z</td>
          </tr>
          <tr>
            <td>2</td>
            <td>Perform login with valid credentials</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:57:19.667Z</td>
            <td>2024-02-14T10:57:48.242Z</td>
          </tr>
          <tr>
            <td>3</td>
            <td>Select 'Demo Organization Inc.' as the tenant name from the top-level tenant selection dropdown</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:57:48.247Z</td>
            <td>2024-02-14T10:57:48.586Z</td>
          </tr>
          <tr>
            <td>4</td>
            <td>Click on the 'Policies and Evidence' menu</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:57:48.587Z</td>
            <td>2024-02-14T10:57:52.816Z</td>
          </tr>
          <tr>
            <td>5</td>
            <td>Click on the 'Code Quality Infrastructure' tab</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:57:52.817Z</td>
            <td>2024-02-14T10:57:57.745Z</td>
          </tr>
          <tr>
            <td>6</td>
            <td>Click on the 'Software construction checklist' expansion arrow in the Code Quality Infrastructure page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:57:57.747Z</td>
            <td>2024-02-14T10:57:59.806Z</td>
          </tr>
          <tr>
            <td>7</td>
            <td>Click on the 'Software Quality Landscape Checklist' control under Software construction checklist</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:57:59.807Z</td>
            <td>2024-02-14T10:58:02.996Z</td>
          </tr>
          <tr>
            <td>8</td>
            <td>Check whether the page navigates to the 'Software Quality Landscape Checklist' page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:58:02.997Z</td>
            <td>2024-02-14T10:58:03.591Z</td>
          </tr>
          <tr>
            <td>9</td>
            <td>Close-browser</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:58:03.592Z</td>
            <td>2024-02-14T10:58:04.073Z</td>
          </tr>
      </table>