
<!-- ⚠️ DEMO DATA NOTICE: This is synthetic demonstration data created for EOH theme showcase purposes. All test results, names, and references are fictional and designed for educational use only. -->
<h1>Test Report: TC-EOH-PERF-0038</h1>
    <p><b>Run ID:</b> TR-EOH-PERF-0038</p>
    <p><b>Title:</b> Verify that when clicking the 'Debugging Checklist' in the Software Construction Checklist expanded list, app navigates to the corresponding Debugging Checklist or not</p>
    <p><b>Status:</b> <span class="status-passed">PASSED</span></p>
    <p><b>Start Time:</b> 2024-02-14T10:53:52.963Z</p>
    <p><b>End Time:</b> 2024-02-14T10:55:04.421Z</p>
    <p><b>Total Duration:</b> 71.46 seconds</p>
    
      <h2>Test Steps</h2>
      <table border="1" cellspacing="0" cellpadding="5">
        <tr>
          <th>Step</th>
          <th>Name</th>
          <th>Status</th>
          <th>Start Time</th>
          <th>End Time</th>
        </tr>
        
          <tr>
            <td>1</td>
            <td>Navigate to the URL and ensure the page loads successfully</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:53:53.032Z</td>
            <td>2024-02-14T10:54:12.806Z</td>
          </tr>
          <tr>
            <td>2</td>
            <td>Perform login with valid credentials</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:54:12.819Z</td>
            <td>2024-02-14T10:54:42.637Z</td>
          </tr>
          <tr>
            <td>3</td>
            <td>Select 'Demo Organization Inc.' as the tenant name from the top-level tenant selection dropdown</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:54:42.639Z</td>
            <td>2024-02-14T10:54:43.054Z</td>
          </tr>
          <tr>
            <td>4</td>
            <td>Click on the 'Policies and Evidence' menu</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:54:43.121Z</td>
            <td>2024-02-14T10:54:48.875Z</td>
          </tr>
          <tr>
            <td>5</td>
            <td>Click on the 'Code Quality Infrastructure' tab</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:54:48.877Z</td>
            <td>2024-02-14T10:54:53.513Z</td>
          </tr>
          <tr>
            <td>6</td>
            <td>Click on the 'Software construction checklist' expansion arrow in the Code Quality Infrastructure page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:54:53.515Z</td>
            <td>2024-02-14T10:54:56.584Z</td>
          </tr>
          <tr>
            <td>7</td>
            <td>Click on the 'Debugging Checklist' control under Software construction checklist</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:54:56.585Z</td>
            <td>2024-02-14T10:55:01.831Z</td>
          </tr>
          <tr>
            <td>8</td>
            <td>Check whether the page navigates to the 'Debugging Checklist' page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:55:01.833Z</td>
            <td>2024-02-14T10:55:03.280Z</td>
          </tr>
          <tr>
            <td>9</td>
            <td>Close-browser</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:55:03.282Z</td>
            <td>2024-02-14T10:55:04.531Z</td>
          </tr>
      </table>