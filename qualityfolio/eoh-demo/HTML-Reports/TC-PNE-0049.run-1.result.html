
<!-- ⚠️ DEMO DATA NOTICE: This is synthetic demonstration data created for EOH theme showcase purposes. All test results, names, and references are fictional and designed for educational use only. -->
<h1>Test Report: TC-EOH-PERF-0049</h1>
    <p><b>Run ID:</b> TR-EOH-PERF-0049</p>
    <p><b>Title:</b> Verify that when clicking the 'Naming Variables Checklist' in the Software Construction Checklist expanded list, app navigates to the corresponding Naming Variables Checklist or not</p>
    <p><b>Status:</b> <span class="status-passed">PASSED</span></p>
    <p><b>Start Time:</b> 2024-02-14T11:03:02.858Z</p>
    <p><b>End Time:</b> 2024-02-14T11:03:47.238Z</p>
    <p><b>Total Duration:</b> 44.38 seconds</p>
    
      <h2>Test Steps</h2>
      <table border="1" cellspacing="0" cellpadding="5">
        <tr>
          <th>Step</th>
          <th>Name</th>
          <th>Status</th>
          <th>Start Time</th>
          <th>End Time</th>
        </tr>
        
          <tr>
            <td>1</td>
            <td>Navigate to the URL and ensure the page loads successfully</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T11:03:02.859Z</td>
            <td>2024-02-14T11:03:09.772Z</td>
          </tr>
          <tr>
            <td>2</td>
            <td>Perform login with valid credentials</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T11:03:09.773Z</td>
            <td>2024-02-14T11:03:29.494Z</td>
          </tr>
          <tr>
            <td>3</td>
            <td>Select 'Demo Organization Inc.' as the tenant name from the top-level tenant selection dropdown</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T11:03:29.497Z</td>
            <td>2024-02-14T11:03:29.638Z</td>
          </tr>
          <tr>
            <td>4</td>
            <td>Click on the 'Policies and Evidence' menu</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T11:03:29.639Z</td>
            <td>2024-02-14T11:03:32.795Z</td>
          </tr>
          <tr>
            <td>5</td>
            <td>Click on the 'Code Quality Infrastructure' tab</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T11:03:32.796Z</td>
            <td>2024-02-14T11:03:37.731Z</td>
          </tr>
          <tr>
            <td>6</td>
            <td>Click on the 'Software construction checklist' expansion arrow in the Code Quality Infrastructure page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T11:03:37.732Z</td>
            <td>2024-02-14T11:03:42.154Z</td>
          </tr>
          <tr>
            <td>7</td>
            <td>Click on the 'Naming Variables Checklist' control under Software construction checklist</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T11:03:42.155Z</td>
            <td>2024-02-14T11:03:45.963Z</td>
          </tr>
          <tr>
            <td>8</td>
            <td>Check whether the page navigates to the 'Naming Variables Checklist' page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T11:03:45.964Z</td>
            <td>2024-02-14T11:03:46.862Z</td>
          </tr>
          <tr>
            <td>9</td>
            <td>Close-browser</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T11:03:46.862Z</td>
            <td>2024-02-14T11:03:47.266Z</td>
          </tr>
      </table>