
<!-- ⚠️ DEMO DATA NOTICE: This is synthetic demonstration data created for EOH theme showcase purposes. All test results, names, and references are fictional and designed for educational use only. -->
<h1>Test Report: TC-EOH-SUPP-0005</h1>
    
    <p><b>Title:</b> Verify that proper validation is applied for empty fields</p>
    <p><b>Status:</b> <span class="status-passed">PASSED</span></p>
    <p><b>Start Time:</b> 2024-06-12T05:31:40.337Z</p>
    <p><b>End Time:</b> 2024-06-12T05:32:12.849Z</p>
    <p><b>Total Duration:</b> 32.51 seconds</p>
    
      <h2>Test Steps</h2>
      <table border="1" cellspacing="0" cellpadding="5">
        <tr>
          <th>Step</th>
          <th>Name</th>
          <th>Status</th>
          <th>Start Time</th>
          <th>End Time</th>
        </tr>
        
          <tr>
            <td>1</td>
            <td>Navigate to the URL and ensure the page loads successfully</td>
            <td class="status-passed">passed</td>
            <td>2024-06-12T05:31:40.342Z</td>
            <td>2024-06-12T05:31:45.236Z</td>
          </tr>
          <tr>
            <td>2</td>
            <td>Perform login with valid credentials</td>
            <td class="status-passed">passed</td>
            <td>2024-06-12T05:31:45.241Z</td>
            <td>2024-06-12T05:32:01.945Z</td>
          </tr>
          <tr>
            <td>3</td>
            <td>Select 'Demo Organization Inc.' as the tenant name from the top-level tenant selection dropdown</td>
            <td class="status-passed">passed</td>
            <td>2024-06-12T05:32:02.000Z</td>
            <td>2024-06-12T05:32:04.038Z</td>
          </tr>
          <tr>
            <td>4</td>
            <td>Verify that clicking on 'Support & Feedback' opens the corresponding popup with correct title</td>
            <td class="status-passed">passed</td>
            <td>2024-06-12T05:32:04.053Z</td>
            <td>2024-06-12T05:32:08.554Z</td>
          </tr>
          <tr>
            <td>5</td>
            <td>Verify that validation messages are displayed for empty subject fields</td>
            <td class="status-passed">passed</td>
            <td>2024-06-12T05:32:08.554Z</td>
            <td>2024-06-12T05:32:10.660Z</td>
          </tr>
          <tr>
            <td>6</td>
            <td>Verify that validation messages are displayed for empty message field</td>
            <td class="status-passed">passed</td>
            <td>2024-06-12T05:32:10.660Z</td>
            <td>2024-06-12T05:32:12.750Z</td>
          </tr>
          <tr>
            <td>7</td>
            <td>Close the browser</td>
            <td class="status-passed">passed</td>
            <td>2024-06-12T05:32:12.751Z</td>
            <td>2024-06-12T05:32:12.861Z</td>
          </tr>
      </table>