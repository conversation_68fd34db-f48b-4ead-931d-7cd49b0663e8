
<!-- ⚠️ DEMO DATA NOTICE: This is synthetic demonstration data created for EOH theme showcase purposes. All test results, names, and references are fictional and designed for educational use only. -->
<h1>Test Report: TC-EOH-PERF-0060</h1>
    <p><b>Run ID:</b> TR-EOH-PERF-0060</p>
    <p><b>Title:</b> Verify that when clicking the 'Working Classes Checklist' in the Software Construction Checklist expanded list, app navigates to the corresponding Working Classes Checklist or not</p>
    <p><b>Status:</b> <span class="status-passed">PASSED</span></p>
    <p><b>Start Time:</b> 2024-02-14T10:58:04.082Z</p>
    <p><b>End Time:</b> 2024-02-14T10:58:55.618Z</p>
    <p><b>Total Duration:</b> 51.54 seconds</p>
    
      <h2>Test Steps</h2>
      <table border="1" cellspacing="0" cellpadding="5">
        <tr>
          <th>Step</th>
          <th>Name</th>
          <th>Status</th>
          <th>Start Time</th>
          <th>End Time</th>
        </tr>
        
          <tr>
            <td>1</td>
            <td>Navigate to the URL and ensure the page loads successfully</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:58:04.083Z</td>
            <td>2024-02-14T10:58:12.721Z</td>
          </tr>
          <tr>
            <td>2</td>
            <td>Perform login with valid credentials</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:58:12.722Z</td>
            <td>2024-02-14T10:58:39.212Z</td>
          </tr>
          <tr>
            <td>3</td>
            <td>Select 'Demo Organization Inc.' as the tenant name from the top-level tenant selection dropdown</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:58:39.216Z</td>
            <td>2024-02-14T10:58:39.352Z</td>
          </tr>
          <tr>
            <td>4</td>
            <td>Click on the 'Policies and Evidence' menu</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:58:39.353Z</td>
            <td>2024-02-14T10:58:42.510Z</td>
          </tr>
          <tr>
            <td>5</td>
            <td>Click on the 'Code Quality Infrastructure' tab</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:58:42.511Z</td>
            <td>2024-02-14T10:58:49.175Z</td>
          </tr>
          <tr>
            <td>6</td>
            <td>Click on the 'Software construction checklist' expansion arrow in the Code Quality Infrastructure page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:58:49.176Z</td>
            <td>2024-02-14T10:58:51.146Z</td>
          </tr>
          <tr>
            <td>7</td>
            <td>Click on the 'Working Classes Checklist' control under Software construction checklist</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:58:51.147Z</td>
            <td>2024-02-14T10:58:54.773Z</td>
          </tr>
          <tr>
            <td>8</td>
            <td>Check whether the page navigates to the 'Working Classes Checklist' page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:58:54.774Z</td>
            <td>2024-02-14T10:58:55.273Z</td>
          </tr>
          <tr>
            <td>9</td>
            <td>Close-browser</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:58:55.274Z</td>
            <td>2024-02-14T10:58:55.630Z</td>
          </tr>
      </table>