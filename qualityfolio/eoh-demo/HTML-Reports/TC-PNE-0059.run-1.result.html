
<!-- ⚠️ DEMO DATA NOTICE: This is synthetic demonstration data created for EOH theme showcase purposes. All test results, names, and references are fictional and designed for educational use only. -->
<h1>Test Report: TC-EOH-PERF-0059</h1>
    <p><b>Run ID:</b> TR-EOH-PERF-0059</p>
    <p><b>Title:</b> Verify that when clicking the 'Using Conditionals Checklist' in the Software Construction Checklist expanded list, app navigates to the corresponding Using Conditionals Checklist or not</p>
    <p><b>Status:</b> <span class="status-passed">PASSED</span></p>
    <p><b>Start Time:</b> 2024-02-14T10:57:50.838Z</p>
    <p><b>End Time:</b> 2024-02-14T10:58:49.253Z</p>
    <p><b>Total Duration:</b> 58.41 seconds</p>
    
      <h2>Test Steps</h2>
      <table border="1" cellspacing="0" cellpadding="5">
        <tr>
          <th>Step</th>
          <th>Name</th>
          <th>Status</th>
          <th>Start Time</th>
          <th>End Time</th>
        </tr>
        
          <tr>
            <td>1</td>
            <td>Navigate to the URL and ensure the page loads successfully</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:57:50.840Z</td>
            <td>2024-02-14T10:58:07.139Z</td>
          </tr>
          <tr>
            <td>2</td>
            <td>Perform login with valid credentials</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:58:07.141Z</td>
            <td>2024-02-14T10:58:32.583Z</td>
          </tr>
          <tr>
            <td>3</td>
            <td>Select 'Demo Organization Inc.' as the tenant name from the top-level tenant selection dropdown</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:58:32.585Z</td>
            <td>2024-02-14T10:58:32.800Z</td>
          </tr>
          <tr>
            <td>4</td>
            <td>Click on the 'Policies and Evidence' menu</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:58:32.801Z</td>
            <td>2024-02-14T10:58:36.649Z</td>
          </tr>
          <tr>
            <td>5</td>
            <td>Click on the 'Code Quality Infrastructure' tab</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:58:36.650Z</td>
            <td>2024-02-14T10:58:41.220Z</td>
          </tr>
          <tr>
            <td>6</td>
            <td>Click on the 'Software construction checklist' expansion arrow in the Code Quality Infrastructure page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:58:41.222Z</td>
            <td>2024-02-14T10:58:43.175Z</td>
          </tr>
          <tr>
            <td>7</td>
            <td>Click on the 'Using Conditionals Checklist' control under Software construction checklist</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:58:43.176Z</td>
            <td>2024-02-14T10:58:46.422Z</td>
          </tr>
          <tr>
            <td>8</td>
            <td>Check whether the page navigates to the 'Using Conditionals Checklist' page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:58:46.423Z</td>
            <td>2024-02-14T10:58:48.821Z</td>
          </tr>
          <tr>
            <td>9</td>
            <td>Close-browser</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:58:48.821Z</td>
            <td>2024-02-14T10:58:49.274Z</td>
          </tr>
      </table>