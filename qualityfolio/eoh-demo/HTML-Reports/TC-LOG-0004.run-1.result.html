
<!-- ⚠️ DEMO DATA NOTICE: This is synthetic demonstration data created for EOH theme showcase purposes. All test results, names, and references are fictional and designed for educational use only. -->
<h1>Test Report: TC-EOH-AUTH-0004</h1>
    <p><b>Run ID:</b> TR-EOH-AUTH-0004</p>
    <p><b>Title:</b> Invalid username log check</p>
    <p><b>Status:</b> <span class="status-passed">PASSED</span></p>
    <p><b>Start Time:</b> 2024-06-12T06:22:39.166Z</p>
    <p><b>End Time:</b> 2024-06-12T06:22:46.322Z</p>
    <p><b>Total Duration:</b> 7.16 seconds</p>
    
      <h2>Test Steps</h2>
      <table border="1" cellspacing="0" cellpadding="5">
        <tr>
          <th>Step</th>
          <th>Name</th>
          <th>Status</th>
          <th>Start Time</th>
          <th>End Time</th>
        </tr>
        
          <tr>
            <td>1</td>
            <td>Navigate to the URL and ensure the page loads successfully</td>
            <td class="status-passed">passed</td>
            <td>2024-06-12T06:22:39.174Z</td>
            <td>2024-06-12T06:22:44.112Z</td>
          </tr>
          <tr>
            <td>2</td>
            <td>Attempt login with invalid username</td>
            <td class="status-passed">passed</td>
            <td>2024-06-12T06:22:44.123Z</td>
            <td>2024-06-12T06:22:45.726Z</td>
          </tr>
          <tr>
            <td>3</td>
            <td>Verify invalid login error message</td>
            <td class="status-passed">passed</td>
            <td>2024-06-12T06:22:45.728Z</td>
            <td>2024-06-12T06:22:46.348Z</td>
          </tr>
          <tr>
            <td>4</td>
            <td>Close-browser</td>
            <td class="status-passed">passed</td>
            <td>2024-06-12T06:22:46.728Z</td>
            <td>2024-06-12T06:22:47.348Z</td>
          </tr>
      </table>