
<!-- ⚠️ DEMO DATA NOTICE: This is synthetic demonstration data created for EOH theme showcase purposes. All test results, names, and references are fictional and designed for educational use only. -->
<h1>Test Report: TC-EOH-PERF-0028</h1>
    <p><b>Run ID:</b> TR-EOH-PERF-0028</p>
    <p><b>Title:</b> Verify that when clicking the 'System Health Policy' in the Code Quality expanded list, app navigates to the corresponding System Health Policy or not</p>
    <p><b>Status:</b> <span class="status-passed">PASSED</span></p>
    <p><b>Start Time:</b> 2024-02-14T04:42:37.665Z</p>
    <p><b>End Time:</b> 2024-02-14T04:43:24.455Z</p>
    <p><b>Total Duration:</b> 46.79 seconds</p>
    
      <h2>Test Steps</h2>
      <table border="1" cellspacing="0" cellpadding="5">
        <tr>
          <th>Step</th>
          <th>Name</th>
          <th>Status</th>
          <th>Start Time</th>
          <th>End Time</th>
        </tr>
        
          <tr>
            <td>1</td>
            <td>Navigate to the URL and ensure the page loads successfully</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:42:37.674Z</td>
            <td>2024-02-14T04:42:46.320Z</td>
          </tr>
          <tr>
            <td>2</td>
            <td>Perform login with valid credentials</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:42:46.333Z</td>
            <td>2024-02-14T04:43:06.796Z</td>
          </tr>
          <tr>
            <td>3</td>
            <td>Select 'Demo Organization Inc.' as the tenant name from the top-level tenant selection dropdown</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:43:06.811Z</td>
            <td>2024-02-14T04:43:07.030Z</td>
          </tr>
          <tr>
            <td>4</td>
            <td>Click on the 'Policies and Evidence' menu</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:43:07.059Z</td>
            <td>2024-02-14T04:43:11.892Z</td>
          </tr>
          <tr>
            <td>5</td>
            <td>Click on the 'Code Quality Infrastructure' tab</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:43:11.893Z</td>
            <td>2024-02-14T04:43:16.945Z</td>
          </tr>
          <tr>
            <td>6</td>
            <td>Click on the 'Code Quality' expansion arrow in the Code Quality Infrastructure page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:43:16.946Z</td>
            <td>2024-02-14T04:43:19.725Z</td>
          </tr>
          <tr>
            <td>7</td>
            <td>Click on the 'User Experience Policy' control under code quality</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:43:19.726Z</td>
            <td>2024-02-14T04:43:22.691Z</td>
          </tr>
          <tr>
            <td>8</td>
            <td>Check whether the page navigates to the 'User Experience Policy' page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:43:22.692Z</td>
            <td>2024-02-14T04:43:23.606Z</td>
          </tr>
          <tr>
            <td>9</td>
            <td>Close-browser</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:43:23.607Z</td>
            <td>2024-02-14T04:43:24.479Z</td>
          </tr>
      </table>