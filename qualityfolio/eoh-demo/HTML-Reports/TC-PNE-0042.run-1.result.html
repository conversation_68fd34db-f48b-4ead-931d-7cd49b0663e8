
<!-- ⚠️ DEMO DATA NOTICE: This is synthetic demonstration data created for EOH theme showcase purposes. All test results, names, and references are fictional and designed for educational use only. -->
<h1>Test Report: TC-EOH-PERF-0042</h1>
    <p><b>Run ID:</b> TR-EOH-PERF-0042</p>
    <p><b>Title:</b> Verify that when clicking the 'Fundamental Data Types Checklist' in the Software Construction Checklist expanded list, app navigates to the corresponding Fundamental Data Types Checklist or not</p>
    <p><b>Status:</b> <span class="status-passed">PASSED</span></p>
    <p><b>Start Time:</b> 2024-02-14T10:54:49.386Z</p>
    <p><b>End Time:</b> 2024-02-14T10:55:49.943Z</p>
    <p><b>Total Duration:</b> 60.56 seconds</p>
    
      <h2>Test Steps</h2>
      <table border="1" cellspacing="0" cellpadding="5">
        <tr>
          <th>Step</th>
          <th>Name</th>
          <th>Status</th>
          <th>Start Time</th>
          <th>End Time</th>
        </tr>
        
          <tr>
            <td>1</td>
            <td>Navigate to the URL and ensure the page loads successfully</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:54:49.389Z</td>
            <td>2024-02-14T10:55:02.321Z</td>
          </tr>
          <tr>
            <td>2</td>
            <td>Perform login with valid credentials</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:55:02.324Z</td>
            <td>2024-02-14T10:55:28.112Z</td>
          </tr>
          <tr>
            <td>3</td>
            <td>Select 'Demo Organization Inc.' as the tenant name from the top-level tenant selection dropdown</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:55:28.115Z</td>
            <td>2024-02-14T10:55:28.599Z</td>
          </tr>
          <tr>
            <td>4</td>
            <td>Click on the 'Policies and Evidence' menu</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:55:28.600Z</td>
            <td>2024-02-14T10:55:34.624Z</td>
          </tr>
          <tr>
            <td>5</td>
            <td>Click on the 'Code Quality Infrastructure' tab</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:55:34.635Z</td>
            <td>2024-02-14T10:55:39.618Z</td>
          </tr>
          <tr>
            <td>6</td>
            <td>Click on the 'Software construction checklist' expansion arrow in the Code Quality Infrastructure page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:55:39.620Z</td>
            <td>2024-02-14T10:55:41.685Z</td>
          </tr>
          <tr>
            <td>7</td>
            <td>Click on the 'Fundamental Data Types Checklist' control under Software construction checklist</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:55:41.686Z</td>
            <td>2024-02-14T10:55:46.281Z</td>
          </tr>
          <tr>
            <td>8</td>
            <td>Check whether the page navigates to the 'Fundamental Data Types Checklist' page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:55:46.283Z</td>
            <td>2024-02-14T10:55:47.923Z</td>
          </tr>
          <tr>
            <td>9</td>
            <td>Close-browser</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:55:47.924Z</td>
            <td>2024-02-14T10:55:49.955Z</td>
          </tr>
      </table>