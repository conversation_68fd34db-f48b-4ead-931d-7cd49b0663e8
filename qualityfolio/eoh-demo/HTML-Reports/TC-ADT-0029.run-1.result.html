<h1>Test Report: TC-ADT-0029</h1>
    
    <p><b>Title:</b> Check whether the Confirm Control Status Change popup, generated when attempting to submit the review status without fulfilling the policies, can be closed by clicking the 'x' button</p>
    <p><b>Status:</b> <span class="status-passed">PASSED</span></p>
    <p><b>Start Time:</b> 2025-03-17T11:33:03.297Z</p>
    <p><b>End Time:</b> 2025-03-17T11:33:36.188Z</p>
    <p><b>Total Duration:</b> 32.89 seconds</p>
    
      <h2>Test Steps</h2>
      <table border="1" cellspacing="0" cellpadding="5">
        <tr>
          <th>Step</th>
          <th>Name</th>
          <th>Status</th>
          <th>Start Time</th>
          <th>End Time</th>
        </tr>
        
          <tr>
            <td>1</td>
            <td>Navigate to the URL and ensure the page loads successfully</td>
            <td class="status-passed">passed</td>
            <td>2025-03-17T11:33:03.301Z</td>
            <td>2025-03-17T11:33:06.496Z</td>
          </tr>
          <tr>
            <td>2</td>
            <td>Perform login with valid credentials</td>
            <td class="status-passed">passed</td>
            <td>2025-03-17T11:33:06.502Z</td>
            <td>2025-03-17T11:33:18.556Z</td>
          </tr>
          <tr>
            <td>3</td>
            <td>Select 'Netspective' as the tenant name from the top-level tenant selection dropdown</td>
            <td class="status-passed">passed</td>
            <td>2025-03-17T11:33:18.561Z</td>
            <td>2025-03-17T11:33:21.643Z</td>
          </tr>
          <tr>
            <td>4</td>
            <td>Verify and click the start button</td>
            <td class="status-passed">passed</td>
            <td>2025-03-17T11:33:21.695Z</td>
            <td>2025-03-17T11:33:22.345Z</td>
          </tr>
          <tr>
            <td>5</td>
            <td> Expand CC1 -common criteria section if it is not in expanded state </td>
            <td class="status-passed">passed</td>
            <td>2025-03-17T11:33:22.346Z</td>
            <td>2025-03-17T11:33:24.449Z</td>
          </tr>
          <tr>
            <td>6</td>
            <td> Navigate to CC1-0001 control code detail page</td>
            <td class="status-passed">passed</td>
            <td>2025-03-17T11:33:24.450Z</td>
            <td>2025-03-17T11:33:27.466Z</td>
          </tr>
          <tr>
            <td>7</td>
            <td>Attempt to submit a review note with a status update without verifying the evidence popup</td>
            <td class="status-passed">passed</td>
            <td>2025-03-17T11:33:27.468Z</td>
            <td>2025-03-17T11:33:36.020Z</td>
          </tr>
          <tr>
            <td>8</td>
            <td>Verify that the message popup displays: 'Control status cannot be altered until the review status of the specified policies is fulfilled'</td>
            <td class="status-passed">passed</td>
            <td>2025-03-17T11:33:36.021Z</td>
            <td>2025-03-17T11:33:36.026Z</td>
          </tr>
          <tr>
            <td>9</td>
            <td>Check whether the Confirm Control Status Change popup, generated when attempting to submit the review status without fulfilling the evidence, can be closed by clicking the 'x' button.</td>
            <td class="status-passed">passed</td>
            <td>2025-03-17T11:33:36.026Z</td>
            <td>2025-03-17T11:33:36.089Z</td>
          </tr>
          <tr>
            <td>10</td>
            <td>Close the browser</td>
            <td class="status-passed">passed</td>
            <td>2025-03-17T11:33:36.089Z</td>
            <td>2025-03-17T11:33:36.198Z</td>
          </tr>
      </table>