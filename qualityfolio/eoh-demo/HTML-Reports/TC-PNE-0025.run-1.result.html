
<!-- ⚠️ DEMO DATA NOTICE: This is synthetic demonstration data created for EOH theme showcase purposes. All test results, names, and references are fictional and designed for educational use only. -->
<h1>Test Report: TC-EOH-PERF-0025</h1>
    <p><b>Run ID:</b> TR-EOH-PERF-0025</p>
    <p><b>Title:</b> Verify that when clicking the 'Source Control git Ignore Policy' in the Code Quality expanded list, app navigates to the corresponding Source Control git Ignore Policy or not</p>
    <p><b>Status:</b> <span class="status-passed">PASSED</span></p>
    <p><b>Start Time:</b> 2024-02-14T05:01:05.509Z</p>
    <p><b>End Time:</b> 2024-02-14T05:01:57.322Z</p>
    <p><b>Total Duration:</b> 51.81 seconds</p>
    
      <h2>Test Steps</h2>
      <table border="1" cellspacing="0" cellpadding="5">
        <tr>
          <th>Step</th>
          <th>Name</th>
          <th>Status</th>
          <th>Start Time</th>
          <th>End Time</th>
        </tr>
        
          <tr>
            <td>1</td>
            <td>Navigate to the URL and ensure the page loads successfully</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T05:01:05.524Z</td>
            <td>2024-02-14T05:01:16.587Z</td>
          </tr>
          <tr>
            <td>2</td>
            <td>Perform login with valid credentials</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T05:01:16.603Z</td>
            <td>2024-02-14T05:01:37.588Z</td>
          </tr>
          <tr>
            <td>3</td>
            <td>Select 'Demo Organization Inc.' as the tenant name from the top-level tenant selection dropdown</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T05:01:37.591Z</td>
            <td>2024-02-14T05:01:37.763Z</td>
          </tr>
          <tr>
            <td>4</td>
            <td>Click on the 'Policies and Evidence' menu</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T05:01:37.786Z</td>
            <td>2024-02-14T05:01:40.872Z</td>
          </tr>
          <tr>
            <td>5</td>
            <td>Click on the 'Code Quality Infrastructure' tab</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T05:01:40.873Z</td>
            <td>2024-02-14T05:01:45.489Z</td>
          </tr>
          <tr>
            <td>6</td>
            <td>Click on the 'Code Quality' expansion arrow in the Code Quality Infrastructure page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T05:01:45.490Z</td>
            <td>2024-02-14T05:01:50.496Z</td>
          </tr>
          <tr>
            <td>7</td>
            <td>Click on the 'Source Control git Ignore Policy' control under code quality</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T05:01:50.498Z</td>
            <td>2024-02-14T05:01:56.043Z</td>
          </tr>
          <tr>
            <td>8</td>
            <td>Check whether the page navigates to the 'Source Control git Ignore Policy' page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T05:01:56.044Z</td>
            <td>2024-02-14T05:01:56.904Z</td>
          </tr>
          <tr>
            <td>9</td>
            <td>Close-browser</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T05:01:56.905Z</td>
            <td>2024-02-14T05:01:57.352Z</td>
          </tr>
      </table>