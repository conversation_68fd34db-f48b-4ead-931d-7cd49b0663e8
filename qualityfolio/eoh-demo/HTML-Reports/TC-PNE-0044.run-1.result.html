
<!-- ⚠️ DEMO DATA NOTICE: This is synthetic demonstration data created for EOH theme showcase purposes. All test results, names, and references are fictional and designed for educational use only. -->
<h1>Test Report: TC-EOH-PERF-0044</h1>
    <p><b>Run ID:</b> TR-EOH-PERF-0044</p>
    <p><b>Title:</b> Verify that when clicking the 'General Control Checklist' in the Software Construction Checklist expanded list, app navigates to the corresponding General Control Checklist or not</p>
    <p><b>Status:</b> <span class="status-passed">PASSED</span></p>
    <p><b>Start Time:</b> 2024-02-14T11:02:07.703Z</p>
    <p><b>End Time:</b> 2024-02-14T11:03:02.788Z</p>
    <p><b>Total Duration:</b> 55.09 seconds</p>
    
      <h2>Test Steps</h2>
      <table border="1" cellspacing="0" cellpadding="5">
        <tr>
          <th>Step</th>
          <th>Name</th>
          <th>Status</th>
          <th>Start Time</th>
          <th>End Time</th>
        </tr>
        
          <tr>
            <td>1</td>
            <td>Navigate to the URL and ensure the page loads successfully</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T11:02:07.740Z</td>
            <td>2024-02-14T11:02:23.836Z</td>
          </tr>
          <tr>
            <td>2</td>
            <td>Perform login with valid credentials</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T11:02:23.845Z</td>
            <td>2024-02-14T11:02:43.030Z</td>
          </tr>
          <tr>
            <td>3</td>
            <td>Select 'Demo Organization Inc.' as the tenant name from the top-level tenant selection dropdown</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T11:02:43.036Z</td>
            <td>2024-02-14T11:02:43.263Z</td>
          </tr>
          <tr>
            <td>4</td>
            <td>Click on the 'Policies and Evidence' menu</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T11:02:43.313Z</td>
            <td>2024-02-14T11:02:48.178Z</td>
          </tr>
          <tr>
            <td>5</td>
            <td>Click on the 'Code Quality Infrastructure' tab</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T11:02:48.180Z</td>
            <td>2024-02-14T11:02:55.232Z</td>
          </tr>
          <tr>
            <td>6</td>
            <td>Click on the 'Software construction checklist' expansion arrow in the Code Quality Infrastructure page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T11:02:55.234Z</td>
            <td>2024-02-14T11:02:57.275Z</td>
          </tr>
          <tr>
            <td>7</td>
            <td>Click on the 'General Control Checklist' control under Software construction checklist</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T11:02:57.276Z</td>
            <td>2024-02-14T11:03:01.576Z</td>
          </tr>
          <tr>
            <td>8</td>
            <td>Check whether the page navigates to the 'General Control Checklist' page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T11:03:01.577Z</td>
            <td>2024-02-14T11:03:02.449Z</td>
          </tr>
          <tr>
            <td>9</td>
            <td>Close-browser</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T11:03:02.450Z</td>
            <td>2024-02-14T11:03:02.852Z</td>
          </tr>
      </table>