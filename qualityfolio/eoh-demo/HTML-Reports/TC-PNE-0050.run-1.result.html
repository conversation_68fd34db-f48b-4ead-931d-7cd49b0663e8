
<!-- ⚠️ DEMO DATA NOTICE: This is synthetic demonstration data created for EOH theme showcase purposes. All test results, names, and references are fictional and designed for educational use only. -->
<h1>Test Report: TC-EOH-PERF-0050</h1>
    <p><b>Run ID:</b> TR-EOH-PERF-0050</p>
    <p><b>Title:</b> Verify that when clicking the 'Organizing Straight Line Code Checklist' in the Software Construction Checklist expanded list, app navigates to the corresponding Organizing Straight Line Code Checklist or not</p>
    <p><b>Status:</b> <span class="status-passed">PASSED</span></p>
    <p><b>Start Time:</b> 2024-02-14T10:56:17.507Z</p>
    <p><b>End Time:</b> 2024-02-14T10:57:11.304Z</p>
    <p><b>Total Duration:</b> 53.80 seconds</p>
    
      <h2>Test Steps</h2>
      <table border="1" cellspacing="0" cellpadding="5">
        <tr>
          <th>Step</th>
          <th>Name</th>
          <th>Status</th>
          <th>Start Time</th>
          <th>End Time</th>
        </tr>
        
          <tr>
            <td>1</td>
            <td>Navigate to the URL and ensure the page loads successfully</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:56:17.517Z</td>
            <td>2024-02-14T10:56:26.231Z</td>
          </tr>
          <tr>
            <td>2</td>
            <td>Perform login with valid credentials</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:56:26.242Z</td>
            <td>2024-02-14T10:56:53.878Z</td>
          </tr>
          <tr>
            <td>3</td>
            <td>Select 'Demo Organization Inc.' as the tenant name from the top-level tenant selection dropdown</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:56:53.881Z</td>
            <td>2024-02-14T10:56:54.437Z</td>
          </tr>
          <tr>
            <td>4</td>
            <td>Click on the 'Policies and Evidence' menu</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:56:54.482Z</td>
            <td>2024-02-14T10:56:57.672Z</td>
          </tr>
          <tr>
            <td>5</td>
            <td>Click on the 'Code Quality Infrastructure' tab</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:56:57.673Z</td>
            <td>2024-02-14T10:57:02.495Z</td>
          </tr>
          <tr>
            <td>6</td>
            <td>Click on the 'Software construction checklist' expansion arrow in the Code Quality Infrastructure page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:57:02.496Z</td>
            <td>2024-02-14T10:57:04.523Z</td>
          </tr>
          <tr>
            <td>7</td>
            <td>Click on the 'Organizing Straight Line Code Checklist' control under Software construction checklist</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:57:04.524Z</td>
            <td>2024-02-14T10:57:09.139Z</td>
          </tr>
          <tr>
            <td>8</td>
            <td>Check whether the page navigates to the 'Organizing Straight Line Code Checklist' page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:57:09.140Z</td>
            <td>2024-02-14T10:57:10.502Z</td>
          </tr>
          <tr>
            <td>9</td>
            <td>Close-browser</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:57:10.503Z</td>
            <td>2024-02-14T10:57:11.332Z</td>
          </tr>
      </table>