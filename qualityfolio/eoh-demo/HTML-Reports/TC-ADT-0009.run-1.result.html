
<!-- ⚠️ DEMO DATA NOTICE: This is synthetic demonstration data created for EOH theme showcase purposes. All test results, names, and references are fictional and designed for educational use only. -->
<h1>Test Report: TC-EOH-review-0009</h1>
    
    <p><b>Title:</b> Check whether the Out of quality Quality Compliance,machine validation,manual validation, Accepted by external reviewer all are represented in percentage manner or not in the detail page of session report working or not</p>
    <p><b>Status:</b> <span class="status-passed">PASSED</span></p>
    <p><b>Start Time:</b> 2024-03-17T10:02:52.405Z</p>
    <p><b>End Time:</b> 2024-03-17T10:03:13.116Z</p>
    <p><b>Total Duration:</b> 20.71 seconds</p>
    
      <h2>Test Steps</h2>
      <table border="1" cellspacing="0" cellpadding="5">
        <tr>
          <th>Step</th>
          <th>Name</th>
          <th>Status</th>
          <th>Start Time</th>
          <th>End Time</th>
        </tr>
        
          <tr>
            <td>1</td>
            <td>Navigate to the URL and ensure the page loads successfully</td>
            <td class="status-passed">passed</td>
            <td>2024-03-17T10:02:52.409Z</td>
            <td>2024-03-17T10:02:55.222Z</td>
          </tr>
          <tr>
            <td>2</td>
            <td>Perform login with valid credentials</td>
            <td class="status-passed">passed</td>
            <td>2024-03-17T10:02:55.229Z</td>
            <td>2024-03-17T10:03:08.431Z</td>
          </tr>
          <tr>
            <td>3</td>
            <td>Select 'Demo Organization Inc.' as the tenant name from the top-level tenant selection dropdown</td>
            <td class="status-passed">passed</td>
            <td>2024-03-17T10:03:08.436Z</td>
            <td>2024-03-17T10:03:11.501Z</td>
          </tr>
          <tr>
            <td>4</td>
            <td>Verify and click the start button</td>
            <td class="status-passed">passed</td>
            <td>2024-03-17T10:03:11.549Z</td>
            <td>2024-03-17T10:03:12.167Z</td>
          </tr>
          <tr>
            <td>5</td>
            <td>page.waitForSelector(//body//div//div[@role='tabpanel']//div//div//div//div[1]//div[1]//div[1]//div[1]//div[2])</td>
            <td class="status-passed">passed</td>
            <td>2024-03-17T10:03:12.168Z</td>
            <td>2024-03-17T10:03:12.972Z</td>
          </tr>
          <tr>
            <td>6</td>
            <td>elementHandle.textContent</td>
            <td class="status-passed">passed</td>
            <td>2024-03-17T10:03:12.973Z</td>
            <td>2024-03-17T10:03:12.979Z</td>
          </tr>
          <tr>
            <td>7</td>
            <td>page.waitForSelector(//*[@id="slider-main"]/astro-island/section/div/div[1]/div/div[2]/p[1])</td>
            <td class="status-passed">passed</td>
            <td>2024-03-17T10:03:12.979Z</td>
            <td>2024-03-17T10:03:12.983Z</td>
          </tr>
          <tr>
            <td>8</td>
            <td>elementHandle.textContent</td>
            <td class="status-passed">passed</td>
            <td>2024-03-17T10:03:12.984Z</td>
            <td>2024-03-17T10:03:12.986Z</td>
          </tr>
          <tr>
            <td>9</td>
            <td>expect.toEqual</td>
            <td class="status-passed">passed</td>
            <td>2024-03-17T10:03:12.987Z</td>
            <td>2024-03-17T10:03:12.987Z</td>
          </tr>
          <tr>
            <td>10</td>
            <td>expect.toBeGreaterThanOrEqual</td>
            <td class="status-passed">passed</td>
            <td>2024-03-17T10:03:12.987Z</td>
            <td>2024-03-17T10:03:12.987Z</td>
          </tr>
          <tr>
            <td>11</td>
            <td>page.waitForSelector(//body//div//div[@role='tabpanel']//div//div//div//div[2]//div[1]//div[1]//div[1]//div[2])</td>
            <td class="status-passed">passed</td>
            <td>2024-03-17T10:03:12.988Z</td>
            <td>2024-03-17T10:03:12.993Z</td>
          </tr>
          <tr>
            <td>12</td>
            <td>elementHandle.textContent</td>
            <td class="status-passed">passed</td>
            <td>2024-03-17T10:03:12.994Z</td>
            <td>2024-03-17T10:03:12.996Z</td>
          </tr>
          <tr>
            <td>13</td>
            <td>page.waitForSelector(//*[@id="slider-main"]/astro-island/section/div/div[2]/div/div[2]/p[1])</td>
            <td class="status-passed">passed</td>
            <td>2024-03-17T10:03:12.997Z</td>
            <td>2024-03-17T10:03:13.000Z</td>
          </tr>
          <tr>
            <td>14</td>
            <td>elementHandle.textContent</td>
            <td class="status-passed">passed</td>
            <td>2024-03-17T10:03:13.001Z</td>
            <td>2024-03-17T10:03:13.003Z</td>
          </tr>
          <tr>
            <td>15</td>
            <td>expect.toEqual</td>
            <td class="status-passed">passed</td>
            <td>2024-03-17T10:03:13.003Z</td>
            <td>2024-03-17T10:03:13.003Z</td>
          </tr>
          <tr>
            <td>16</td>
            <td>expect.toBeGreaterThanOrEqual</td>
            <td class="status-passed">passed</td>
            <td>2024-03-17T10:03:13.004Z</td>
            <td>2024-03-17T10:03:13.004Z</td>
          </tr>
          <tr>
            <td>17</td>
            <td>page.waitForSelector(//body//div//div[@role='tabpanel']//div//div//div//div[3]//div[1]//div[1]//div[1]//div[2])</td>
            <td class="status-passed">passed</td>
            <td>2024-03-17T10:03:13.005Z</td>
            <td>2024-03-17T10:03:13.012Z</td>
          </tr>
          <tr>
            <td>18</td>
            <td>elementHandle.textContent</td>
            <td class="status-passed">passed</td>
            <td>2024-03-17T10:03:13.013Z</td>
            <td>2024-03-17T10:03:13.014Z</td>
          </tr>
          <tr>
            <td>19</td>
            <td>page.waitForSelector(//*[@id="slider-main"]/astro-island/section/div/div[3]/div/div[2]/p[1])</td>
            <td class="status-passed">passed</td>
            <td>2024-03-17T10:03:13.015Z</td>
            <td>2024-03-17T10:03:13.018Z</td>
          </tr>
          <tr>
            <td>20</td>
            <td>elementHandle.textContent</td>
            <td class="status-passed">passed</td>
            <td>2024-03-17T10:03:13.019Z</td>
            <td>2024-03-17T10:03:13.021Z</td>
          </tr>
          <tr>
            <td>21</td>
            <td>expect.toEqual</td>
            <td class="status-passed">passed</td>
            <td>2024-03-17T10:03:13.022Z</td>
            <td>2024-03-17T10:03:13.022Z</td>
          </tr>
          <tr>
            <td>22</td>
            <td>expect.toBeGreaterThanOrEqual</td>
            <td class="status-passed">passed</td>
            <td>2024-03-17T10:03:13.023Z</td>
            <td>2024-03-17T10:03:13.023Z</td>
          </tr>
          <tr>
            <td>23</td>
            <td>page.waitForSelector(//body//div//div[@role='tabpanel']//div//div//div//div[4]//div[1]//div[1]//div[1]//div[2])</td>
            <td class="status-passed">passed</td>
            <td>2024-03-17T10:03:13.023Z</td>
            <td>2024-03-17T10:03:13.028Z</td>
          </tr>
          <tr>
            <td>24</td>
            <td>elementHandle.textContent</td>
            <td class="status-passed">passed</td>
            <td>2024-03-17T10:03:13.029Z</td>
            <td>2024-03-17T10:03:13.031Z</td>
          </tr>
          <tr>
            <td>25</td>
            <td>page.waitForSelector(//*[@id="slider-main"]/astro-island/section/div/div[4]/div/div[2]/p[1])</td>
            <td class="status-passed">passed</td>
            <td>2024-03-17T10:03:13.032Z</td>
            <td>2024-03-17T10:03:13.038Z</td>
          </tr>
          <tr>
            <td>26</td>
            <td>elementHandle.textContent</td>
            <td class="status-passed">passed</td>
            <td>2024-03-17T10:03:13.039Z</td>
            <td>2024-03-17T10:03:13.041Z</td>
          </tr>
          <tr>
            <td>27</td>
            <td>expect.toEqual</td>
            <td class="status-passed">passed</td>
            <td>2024-03-17T10:03:13.041Z</td>
            <td>2024-03-17T10:03:13.041Z</td>
          </tr>
          <tr>
            <td>28</td>
            <td>expect.toBeGreaterThanOrEqual</td>
            <td class="status-passed">passed</td>
            <td>2024-03-17T10:03:13.042Z</td>
            <td>2024-03-17T10:03:13.042Z</td>
          </tr>
          <tr>
            <td>29</td>
            <td>Close the browser</td>
            <td class="status-passed">passed</td>
            <td>2024-03-17T10:03:13.042Z</td>
            <td>2024-03-17T10:03:13.126Z</td>
          </tr>
      </table>