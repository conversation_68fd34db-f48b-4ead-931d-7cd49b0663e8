
<!-- ⚠️ DEMO DATA NOTICE: This is synthetic demonstration data created for EOH theme showcase purposes. All test results, names, and references are fictional and designed for educational use only. -->
<h1>Test Report: TC-EOH-AUTH-0005</h1>
    <p><b>Run ID:</b> TR-EOH-AUTH-0005</p>
    <p><b>Title:</b> Login with blank username and password check</p>
    <p><b>Status:</b> <span class="status-passed">PASSED</span></p>
    <p><b>Start Time:</b> 2024-06-12T06:28:12.198Z</p>
    <p><b>End Time:</b> 2024-06-12T06:28:20.297Z</p>
    <p><b>Total Duration:</b> 8.10 seconds</p>
    
      <h2>Test Steps</h2>
      <table border="1" cellspacing="0" cellpadding="5">
        <tr>
          <th>Step</th>
          <th>Name</th>
          <th>Status</th>
          <th>Start Time</th>
          <th>End Time</th>
        </tr>
        
          <tr>
            <td>1</td>
            <td>Navigate to the URL and ensure the page loads successfully</td>
            <td class="status-passed">passed</td>
            <td>2024-06-12T06:28:12.207Z</td>
            <td>2024-06-12T06:28:17.131Z</td>
          </tr>
          <tr>
            <td>2</td>
            <td>Perform login with Empty Username Fields</td>
            <td class="status-passed">passed</td>
            <td>2024-06-12T06:28:17.144Z</td>
            <td>2024-06-12T06:28:17.564Z</td>
          </tr>
          <tr>
            <td>3</td>
            <td>Verify that Login button is in disabled state </td>
            <td class="status-passed">passed</td>
            <td>2024-06-12T06:28:17.566Z</td>
            <td>2024-06-12T06:28:19.602Z</td>
          </tr>
          <tr>
            <td>4</td>
            <td>Close-browser</td>
            <td class="status-passed">passed</td>
            <td>2024-06-12T06:28:19.603Z</td>
            <td>2024-06-12T06:28:20.329Z</td>
          </tr>
      </table>