
<!-- ⚠️ DEMO DATA NOTICE: This is synthetic demonstration data created for EOH theme showcase purposes. All test results, names, and references are fictional and designed for educational use only. -->
<h1>Test Report: TC-EOH-SUPP-0004</h1>
    
    <p><b>Title:</b> Verify that the Message field accepts multi-line input and a reasonable character limit (e.g., 500 or 1000 chars)</p>
    <p><b>Status:</b> <span class="status-failed">FAILED</span></p>
    <p><b>Start Time:</b> 2024-06-12T05:20:15.298Z</p>
    <p><b>End Time:</b> 2024-06-12T05:20:34.981Z</p>
    <p><b>Total Duration:</b> 19.68 seconds</p>
    
      <h2>Test Steps</h2>
      <table border="1" cellspacing="0" cellpadding="5">
        <tr>
          <th>Step</th>
          <th>Name</th>
          <th>Status</th>
          <th>Start Time</th>
          <th>End Time</th>
        </tr>
        
          <tr>
            <td>1</td>
            <td>Navigate to the URL and ensure the page loads successfully</td>
            <td class="status-passed">passed</td>
            <td>2024-06-12T05:20:15.305Z</td>
            <td>2024-06-12T05:20:18.897Z</td>
          </tr>
          <tr>
            <td>2</td>
            <td>Perform login with valid credentials</td>
            <td class="status-passed">passed</td>
            <td>2024-06-12T05:20:18.904Z</td>
            <td>2024-06-12T05:20:27.235Z</td>
          </tr>
          <tr>
            <td>3</td>
            <td>Select 'Demo Organization Inc.' as the tenant name from the top-level tenant selection dropdown</td>
            <td class="status-passed">passed</td>
            <td>2024-06-12T05:20:27.294Z</td>
            <td>2024-06-12T05:20:29.347Z</td>
          </tr>
          <tr>
            <td>4</td>
            <td>Verify that clicking on 'Support & Feedback' opens the corresponding popup with correct title</td>
            <td class="status-passed">passed</td>
            <td>2024-06-12T05:20:29.349Z</td>
            <td>2024-06-12T05:20:34.833Z</td>
          </tr>
          <tr>
            <td>5</td>
            <td>Verify that the submit button is disabled when mandatory fields are not filled</td>
            <td class="status-failed">failed</td>
            <td>2024-06-12T05:20:34.834Z</td>
            <td>2024-06-12T05:20:34.873Z</td>
          </tr>
          <tr>
            <td>6</td>
            <td>Close the browser</td>
            <td class="status-passed">passed</td>
            <td>2024-06-12T05:20:34.875Z</td>
            <td>2024-06-12T05:20:34.998Z</td>
          </tr>
          <tr>
            <td>7</td>
            <td>Worker Cleanup</td>
            <td class="status-passed">passed</td>
            <td>2024-06-12T05:20:34.999Z</td>
            <td>2024-06-12T05:20:34.999Z</td>
          </tr>
      </table>