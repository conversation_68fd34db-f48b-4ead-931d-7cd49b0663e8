<h1>Test Report: TC-ADT-0046</h1>
    
    <p><b>Title:</b> Check whether the members tab should contain the member name, role, status and action or not</p>
    <p><b>Status:</b> <span class="status-passed">PASSED</span></p>
    <p><b>Start Time:</b> 2025-03-18T03:44:36.566Z</p>
    <p><b>End Time:</b> 2025-03-18T03:45:07.955Z</p>
    <p><b>Total Duration:</b> 31.39 seconds</p>
    
      <h2>Test Steps</h2>
      <table border="1" cellspacing="0" cellpadding="5">
        <tr>
          <th>Step</th>
          <th>Name</th>
          <th>Status</th>
          <th>Start Time</th>
          <th>End Time</th>
        </tr>
        
          <tr>
            <td>1</td>
            <td>Navigate to the URL and ensure the page loads successfully</td>
            <td class="status-passed">passed</td>
            <td>2025-03-18T03:44:36.571Z</td>
            <td>2025-03-18T03:44:39.469Z</td>
          </tr>
          <tr>
            <td>2</td>
            <td>Perform login with valid credentials</td>
            <td class="status-passed">passed</td>
            <td>2025-03-18T03:44:39.473Z</td>
            <td>2025-03-18T03:44:51.896Z</td>
          </tr>
          <tr>
            <td>3</td>
            <td>Select 'Netspective' as the tenant name from the top-level tenant selection dropdown</td>
            <td class="status-passed">passed</td>
            <td>2025-03-18T03:44:51.902Z</td>
            <td>2025-03-18T03:44:54.990Z</td>
          </tr>
          <tr>
            <td>4</td>
            <td>Click on the Resume button and navigate to the audit page</td>
            <td class="status-passed">passed</td>
            <td>2025-03-18T03:44:55.040Z</td>
            <td>2025-03-18T03:45:03.713Z</td>
          </tr>
          <tr>
            <td>5</td>
            <td>Click on the member tab and verify that app navigates to members tab or not</td>
            <td class="status-passed">passed</td>
            <td>2025-03-18T03:45:03.716Z</td>
            <td>2025-03-18T03:45:06.773Z</td>
          </tr>
          <tr>
            <td>6</td>
            <td>Enter a already added members name and search for the result</td>
            <td class="status-passed">passed</td>
            <td>2025-03-18T03:45:06.775Z</td>
            <td>2025-03-18T03:45:06.795Z</td>
          </tr>
          <tr>
            <td>7</td>
            <td>Verify that the search result is properly filtered and displyed</td>
            <td class="status-passed">passed</td>
            <td>2025-03-18T03:45:06.796Z</td>
            <td>2025-03-18T03:45:07.812Z</td>
          </tr>
          <tr>
            <td>8</td>
            <td>Verify that added member name is available in the list</td>
            <td class="status-passed">passed</td>
            <td>2025-03-18T03:45:07.813Z</td>
            <td>2025-03-18T03:45:07.828Z</td>
          </tr>
          <tr>
            <td>9</td>
            <td>Verify that added member role is available in the list</td>
            <td class="status-passed">passed</td>
            <td>2025-03-18T03:45:07.829Z</td>
            <td>2025-03-18T03:45:07.847Z</td>
          </tr>
          <tr>
            <td>10</td>
            <td>Verify that added member status is available in the list</td>
            <td class="status-passed">passed</td>
            <td>2025-03-18T03:45:07.847Z</td>
            <td>2025-03-18T03:45:07.873Z</td>
          </tr>
          <tr>
            <td>11</td>
            <td>Close the browser</td>
            <td class="status-passed">passed</td>
            <td>2025-03-18T03:45:07.873Z</td>
            <td>2025-03-18T03:45:07.966Z</td>
          </tr>
      </table>