
<!-- ⚠️ DEMO DATA NOTICE: This is synthetic demonstration data created for EOH theme showcase purposes. All test results, names, and references are fictional and designed for educational use only. -->
<h1>Test Report: TC-EOH-PERF-0016</h1>
    <p><b>Run ID:</b> TR-EOH-PERF-0016</p>
    <p><b>Title:</b> Verify that when clicking the 'Conventional Commit Message Policy' in the Code Quality expanded list, app navigates to the corresponding Conventional Commit Message Policy or not</p>
    <p><b>Status:</b> <span class="status-passed">PASSED</span></p>
    <p><b>Start Time:</b> 2024-02-14T04:39:57.441Z</p>
    <p><b>End Time:</b> 2024-02-14T04:40:52.747Z</p>
    <p><b>Total Duration:</b> 55.31 seconds</p>
    
      <h2>Test Steps</h2>
      <table border="1" cellspacing="0" cellpadding="5">
        <tr>
          <th>Step</th>
          <th>Name</th>
          <th>Status</th>
          <th>Start Time</th>
          <th>End Time</th>
        </tr>
        
          <tr>
            <td>1</td>
            <td>Navigate to the URL and ensure the page loads successfully</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:39:57.443Z</td>
            <td>2024-02-14T04:40:07.713Z</td>
          </tr>
          <tr>
            <td>2</td>
            <td>Perform login with valid credentials</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:40:07.715Z</td>
            <td>2024-02-14T04:40:31.232Z</td>
          </tr>
          <tr>
            <td>3</td>
            <td>Select 'Demo Organization Inc.' as the tenant name from the top-level tenant selection dropdown</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:40:31.235Z</td>
            <td>2024-02-14T04:40:31.411Z</td>
          </tr>
          <tr>
            <td>4</td>
            <td>Click on the 'Policies and Evidence' menu</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:40:31.412Z</td>
            <td>2024-02-14T04:40:37.428Z</td>
          </tr>
          <tr>
            <td>5</td>
            <td>Click on the 'Code Quality Infrastructure' tab</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:40:37.429Z</td>
            <td>2024-02-14T04:40:43.665Z</td>
          </tr>
          <tr>
            <td>6</td>
            <td>Click on the 'Code Quality' expansion arrow in the Code Quality Infrastructure page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:40:43.666Z</td>
            <td>2024-02-14T04:40:45.954Z</td>
          </tr>
          <tr>
            <td>7</td>
            <td>Click on the 'Conventional Commit Message Policy' control under code quality</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:40:45.955Z</td>
            <td>2024-02-14T04:40:49.417Z</td>
          </tr>
          <tr>
            <td>8</td>
            <td>Check whether the page navigates to the 'Conventional Commit Message Policy' page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:40:49.419Z</td>
            <td>2024-02-14T04:40:50.452Z</td>
          </tr>
          <tr>
            <td>9</td>
            <td>Close-browser</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:40:50.453Z</td>
            <td>2024-02-14T04:40:52.853Z</td>
          </tr>
      </table>