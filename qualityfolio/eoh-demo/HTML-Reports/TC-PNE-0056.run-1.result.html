
<!-- ⚠️ DEMO DATA NOTICE: This is synthetic demonstration data created for EOH theme showcase purposes. All test results, names, and references are fictional and designed for educational use only. -->
<h1>Test Report: TC-EOH-PERF-0056</h1>
    <p><b>Run ID:</b> TR-EOH-PERF-0056</p>
    <p><b>Title:</b> Verify that when clicking the 'Table Driven Methods Checklist' in the Software Construction Checklist expanded list, app navigates to the corresponding Table Driven Methods Checklist or not</p>
    <p><b>Status:</b> <span class="status-passed">PASSED</span></p>
    <p><b>Start Time:</b> 2024-02-14T10:57:44.719Z</p>
    <p><b>End Time:</b> 2024-02-14T10:58:37.317Z</p>
    <p><b>Total Duration:</b> 52.60 seconds</p>
    
      <h2>Test Steps</h2>
      <table border="1" cellspacing="0" cellpadding="5">
        <tr>
          <th>Step</th>
          <th>Name</th>
          <th>Status</th>
          <th>Start Time</th>
          <th>End Time</th>
        </tr>
        
          <tr>
            <td>1</td>
            <td>Navigate to the URL and ensure the page loads successfully</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:57:44.734Z</td>
            <td>2024-02-14T10:58:00.417Z</td>
          </tr>
          <tr>
            <td>2</td>
            <td>Perform login with valid credentials</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:58:00.434Z</td>
            <td>2024-02-14T10:58:17.664Z</td>
          </tr>
          <tr>
            <td>3</td>
            <td>Select 'Demo Organization Inc.' as the tenant name from the top-level tenant selection dropdown</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:58:17.666Z</td>
            <td>2024-02-14T10:58:17.864Z</td>
          </tr>
          <tr>
            <td>4</td>
            <td>Click on the 'Policies and Evidence' menu</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:58:17.915Z</td>
            <td>2024-02-14T10:58:22.706Z</td>
          </tr>
          <tr>
            <td>5</td>
            <td>Click on the 'Code Quality Infrastructure' tab</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:58:22.708Z</td>
            <td>2024-02-14T10:58:30.191Z</td>
          </tr>
          <tr>
            <td>6</td>
            <td>Click on the 'Software construction checklist' expansion arrow in the Code Quality Infrastructure page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:58:30.192Z</td>
            <td>2024-02-14T10:58:32.221Z</td>
          </tr>
          <tr>
            <td>7</td>
            <td>Click on the 'Table Driven Methods Checklist' control under Software construction checklist</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:58:32.222Z</td>
            <td>2024-02-14T10:58:35.635Z</td>
          </tr>
          <tr>
            <td>8</td>
            <td>Check whether the page navigates to the 'Table Driven Methods Checklist' page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:58:35.636Z</td>
            <td>2024-02-14T10:58:36.223Z</td>
          </tr>
          <tr>
            <td>9</td>
            <td>Close-browser</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:58:36.224Z</td>
            <td>2024-02-14T10:58:37.365Z</td>
          </tr>
      </table>