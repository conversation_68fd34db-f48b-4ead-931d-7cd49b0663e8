<h1>Test Report: TC-AUD-0132</h1>
    
    <p><b>Title:</b> Verify navigation to the Interaction tab displays the comment text, comment box, and submit button</p>
    <p><b>Status:</b> <span class="status-passed">PASSED</span></p>
    <p><b>Start Time:</b> 2025-07-01T05:04:24.829Z</p>
    <p><b>End Time:</b> 2025-07-01T05:04:49.146Z</p>
    <p><b>Total Duration:</b> 24.32 seconds</p>
    
      <h2>Test Steps</h2>
      <table border="1" cellspacing="0" cellpadding="5">
        <tr>
          <th>Step</th>
          <th>Name</th>
          <th>Status</th>
          <th>Start Time</th>
          <th>End Time</th>
        </tr>
        
          <tr>
            <td>1</td>
            <td>Navigate to the URL and ensure the page loads successfully</td>
            <td class="status-passed">passed</td>
            <td>2025-07-01T05:04:24.833Z</td>
            <td>2025-07-01T05:04:27.947Z</td>
          </tr>
          <tr>
            <td>2</td>
            <td>Perform login with valid credentials</td>
            <td class="status-passed">passed</td>
            <td>2025-07-01T05:04:27.951Z</td>
            <td>2025-07-01T05:04:35.882Z</td>
          </tr>
          <tr>
            <td>3</td>
            <td>Select 'Netspective' as the tenant name from the top-level tenant selection dropdown</td>
            <td class="status-passed">passed</td>
            <td>2025-07-01T05:04:35.939Z</td>
            <td>2025-07-01T05:04:37.969Z</td>
          </tr>
          <tr>
            <td>4</td>
            <td>Navigate to the Audit Page</td>
            <td class="status-passed">passed</td>
            <td>2025-07-01T05:04:37.972Z</td>
            <td>2025-07-01T05:04:39.833Z</td>
          </tr>
          <tr>
            <td>5</td>
            <td>Navigate to the AICPA Page</td>
            <td class="status-passed">passed</td>
            <td>2025-07-01T05:04:39.835Z</td>
            <td>2025-07-01T05:04:40.545Z</td>
          </tr>
          <tr>
            <td>6</td>
            <td>Verify SOC2 Type I link navigation</td>
            <td class="status-passed">passed</td>
            <td>2025-07-01T05:04:40.546Z</td>
            <td>2025-07-01T05:04:42.961Z</td>
          </tr>
          <tr>
            <td>7</td>
            <td>Search for audit session</td>
            <td class="status-passed">passed</td>
            <td>2025-07-01T05:04:42.961Z</td>
            <td>2025-07-01T05:04:42.980Z</td>
          </tr>
          <tr>
            <td>8</td>
            <td>Click the View button</td>
            <td class="status-passed">passed</td>
            <td>2025-07-01T05:04:42.980Z</td>
            <td>2025-07-01T05:04:46.916Z</td>
          </tr>
          <tr>
            <td>9</td>
            <td>Click the Interaction Tab</td>
            <td class="status-passed">passed</td>
            <td>2025-07-01T05:04:46.916Z</td>
            <td>2025-07-01T05:04:49.028Z</td>
          </tr>
          <tr>
            <td>10</td>
            <td>Verify comment text  is visible</td>
            <td class="status-passed">passed</td>
            <td>2025-07-01T05:04:49.029Z</td>
            <td>2025-07-01T05:04:49.037Z</td>
          </tr>
          <tr>
            <td>11</td>
            <td>Verify textarea is visible</td>
            <td class="status-passed">passed</td>
            <td>2025-07-01T05:04:49.038Z</td>
            <td>2025-07-01T05:04:49.044Z</td>
          </tr>
          <tr>
            <td>12</td>
            <td>Verify submit button is visible</td>
            <td class="status-passed">passed</td>
            <td>2025-07-01T05:04:49.045Z</td>
            <td>2025-07-01T05:04:49.050Z</td>
          </tr>
          <tr>
            <td>13</td>
            <td>Close the browser</td>
            <td class="status-passed">passed</td>
            <td>2025-07-01T05:04:49.050Z</td>
            <td>2025-07-01T05:04:49.157Z</td>
          </tr>
      </table>