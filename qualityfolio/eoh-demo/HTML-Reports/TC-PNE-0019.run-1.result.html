
<!-- ⚠️ DEMO DATA NOTICE: This is synthetic demonstration data created for EOH theme showcase purposes. All test results, names, and references are fictional and designed for educational use only. -->
<h1>Test Report: TC-EOH-PERF-0019</h1>
    <p><b>Run ID:</b> TR-EOH-PERF-0019</p>
    <p><b>Title:</b> Verify that when clicking the 'Git hooks Policy' in the Code Quality expanded list, app navigates to the corresponding Git hooks Policy or not</p>
    <p><b>Status:</b> <span class="status-passed">PASSED</span></p>
    <p><b>Start Time:</b> 2024-02-14T04:40:44.811Z</p>
    <p><b>End Time:</b> 2024-02-14T04:41:35.555Z</p>
    <p><b>Total Duration:</b> 50.74 seconds</p>
    
      <h2>Test Steps</h2>
      <table border="1" cellspacing="0" cellpadding="5">
        <tr>
          <th>Step</th>
          <th>Name</th>
          <th>Status</th>
          <th>Start Time</th>
          <th>End Time</th>
        </tr>
        
          <tr>
            <td>1</td>
            <td>Navigate to the URL and ensure the page loads successfully</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:40:44.817Z</td>
            <td>2024-02-14T04:40:55.582Z</td>
          </tr>
          <tr>
            <td>2</td>
            <td>Perform login with valid credentials</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:40:55.584Z</td>
            <td>2024-02-14T04:41:15.777Z</td>
          </tr>
          <tr>
            <td>3</td>
            <td>Select 'Demo Organization Inc.' as the tenant name from the top-level tenant selection dropdown</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:41:15.779Z</td>
            <td>2024-02-14T04:41:16.021Z</td>
          </tr>
          <tr>
            <td>4</td>
            <td>Click on the 'Policies and Evidence' menu</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:41:16.025Z</td>
            <td>2024-02-14T04:41:19.500Z</td>
          </tr>
          <tr>
            <td>5</td>
            <td>Click on the 'Code Quality Infrastructure' tab</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:41:19.501Z</td>
            <td>2024-02-14T04:41:25.068Z</td>
          </tr>
          <tr>
            <td>6</td>
            <td>Click on the 'Code Quality' expansion arrow in the Code Quality Infrastructure page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:41:25.070Z</td>
            <td>2024-02-14T04:41:27.089Z</td>
          </tr>
          <tr>
            <td>7</td>
            <td>Click on the 'Git hooks Policy' control under code quality</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:41:27.090Z</td>
            <td>2024-02-14T04:41:32.486Z</td>
          </tr>
          <tr>
            <td>8</td>
            <td>Check whether the page navigates to the 'Git hooks Policy' page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:41:32.487Z</td>
            <td>2024-02-14T04:41:34.851Z</td>
          </tr>
          <tr>
            <td>9</td>
            <td>Close-browser</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:41:34.851Z</td>
            <td>2024-02-14T04:41:35.571Z</td>
          </tr>
      </table>