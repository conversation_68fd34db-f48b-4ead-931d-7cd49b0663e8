
<!-- ⚠️ DEMO DATA NOTICE: This is synthetic demonstration data created for EOH theme showcase purposes. All test results, names, and references are fictional and designed for educational use only. -->
<h1>Test Report: TC-EOH-PERF-0057</h1>
    <p><b>Run ID:</b> TR-EOH-PERF-0057</p>
    <p><b>Title:</b> Verify that when clicking the 'Unusual Control Structures Checklist' in the Software Construction Checklist expanded list, app navigates to the corresponding Unusual Control Structures Checklist or not</p>
    <p><b>Status:</b> <span class="status-passed">PASSED</span></p>
    <p><b>Start Time:</b> 2024-02-14T10:57:39.139Z</p>
    <p><b>End Time:</b> 2024-02-14T10:58:30.166Z</p>
    <p><b>Total Duration:</b> 51.03 seconds</p>
    
      <h2>Test Steps</h2>
      <table border="1" cellspacing="0" cellpadding="5">
        <tr>
          <th>Step</th>
          <th>Name</th>
          <th>Status</th>
          <th>Start Time</th>
          <th>End Time</th>
        </tr>
        
          <tr>
            <td>1</td>
            <td>Navigate to the URL and ensure the page loads successfully</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:57:39.144Z</td>
            <td>2024-02-14T10:57:50.155Z</td>
          </tr>
          <tr>
            <td>2</td>
            <td>Perform login with valid credentials</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:57:50.157Z</td>
            <td>2024-02-14T10:58:13.685Z</td>
          </tr>
          <tr>
            <td>3</td>
            <td>Select 'Demo Organization Inc.' as the tenant name from the top-level tenant selection dropdown</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:58:13.686Z</td>
            <td>2024-02-14T10:58:13.937Z</td>
          </tr>
          <tr>
            <td>4</td>
            <td>Click on the 'Policies and Evidence' menu</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:58:13.938Z</td>
            <td>2024-02-14T10:58:16.960Z</td>
          </tr>
          <tr>
            <td>5</td>
            <td>Click on the 'Code Quality Infrastructure' tab</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:58:16.962Z</td>
            <td>2024-02-14T10:58:21.916Z</td>
          </tr>
          <tr>
            <td>6</td>
            <td>Click on the 'Software construction checklist' expansion arrow in the Code Quality Infrastructure page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:58:21.919Z</td>
            <td>2024-02-14T10:58:23.782Z</td>
          </tr>
          <tr>
            <td>7</td>
            <td>Click on the 'Unusual Control Structures Checklist' control under Software construction checklist</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:58:23.783Z</td>
            <td>2024-02-14T10:58:27.023Z</td>
          </tr>
          <tr>
            <td>8</td>
            <td>Check whether the page navigates to the 'Unusual Control Structures Checklist' page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:58:27.024Z</td>
            <td>2024-02-14T10:58:29.486Z</td>
          </tr>
          <tr>
            <td>9</td>
            <td>Close-browser</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:58:29.487Z</td>
            <td>2024-02-14T10:58:30.190Z</td>
          </tr>
      </table>