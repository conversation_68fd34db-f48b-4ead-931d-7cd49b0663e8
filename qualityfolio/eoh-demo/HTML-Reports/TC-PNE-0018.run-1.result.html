
<!-- ⚠️ DEMO DATA NOTICE: This is synthetic demonstration data created for EOH theme showcase purposes. All test results, names, and references are fictional and designed for educational use only. -->
<h1>Test Report: TC-EOH-PERF-0018</h1>
    <p><b>Run ID:</b> TR-EOH-PERF-0018</p>
    <p><b>Title:</b> Verify that when clicking the 'Engineering Sandbox Policy' in the Code Quality expanded list, app navigates to the corresponding Engineering Sandbox Policy or not</p>
    <p><b>Status:</b> <span class="status-passed">PASSED</span></p>
    <p><b>Start Time:</b> 2024-02-14T04:40:41.202Z</p>
    <p><b>End Time:</b> 2024-02-14T04:41:40.717Z</p>
    <p><b>Total Duration:</b> 59.52 seconds</p>
    
      <h2>Test Steps</h2>
      <table border="1" cellspacing="0" cellpadding="5">
        <tr>
          <th>Step</th>
          <th>Name</th>
          <th>Status</th>
          <th>Start Time</th>
          <th>End Time</th>
        </tr>
        
          <tr>
            <td>1</td>
            <td>Navigate to the URL and ensure the page loads successfully</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:40:41.204Z</td>
            <td>2024-02-14T04:40:51.986Z</td>
          </tr>
          <tr>
            <td>2</td>
            <td>Perform login with valid credentials</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:40:51.989Z</td>
            <td>2024-02-14T04:41:19.756Z</td>
          </tr>
          <tr>
            <td>3</td>
            <td>Select 'Demo Organization Inc.' as the tenant name from the top-level tenant selection dropdown</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:41:19.765Z</td>
            <td>2024-02-14T04:41:20.109Z</td>
          </tr>
          <tr>
            <td>4</td>
            <td>Click on the 'Policies and Evidence' menu</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:41:20.110Z</td>
            <td>2024-02-14T04:41:25.853Z</td>
          </tr>
          <tr>
            <td>5</td>
            <td>Click on the 'Code Quality Infrastructure' tab</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:41:25.854Z</td>
            <td>2024-02-14T04:41:30.355Z</td>
          </tr>
          <tr>
            <td>6</td>
            <td>Click on the 'Code Quality' expansion arrow in the Code Quality Infrastructure page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:41:30.356Z</td>
            <td>2024-02-14T04:41:33.579Z</td>
          </tr>
          <tr>
            <td>7</td>
            <td>Click on the 'Engineering Sandbox Policy' control under code quality</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:41:33.580Z</td>
            <td>2024-02-14T04:41:37.692Z</td>
          </tr>
          <tr>
            <td>8</td>
            <td>Check whether the page navigates to the 'Engineering Sandbox Policy' page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:41:37.694Z</td>
            <td>2024-02-14T04:41:38.652Z</td>
          </tr>
          <tr>
            <td>9</td>
            <td>Close-browser</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:41:38.652Z</td>
            <td>2024-02-14T04:41:40.731Z</td>
          </tr>
      </table>