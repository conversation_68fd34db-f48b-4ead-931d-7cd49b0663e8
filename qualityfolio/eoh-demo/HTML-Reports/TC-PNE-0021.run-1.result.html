
<!-- ⚠️ DEMO DATA NOTICE: This is synthetic demonstration data created for EOH theme showcase purposes. All test results, names, and references are fictional and designed for educational use only. -->
<h1>Test Report: TC-EOH-PERF-0021</h1>
    <p><b>Run ID:</b> TR-EOH-PERF-0021</p>
    <p><b>Title:</b> Verify that when clicking the 'IDE Policy' in the Code Quality expanded list, app navigates to the corresponding IDE Policy or not</p>
    <p><b>Status:</b> <span class="status-passed">PASSED</span></p>
    <p><b>Start Time:</b> 2024-02-14T04:40:52.904Z</p>
    <p><b>End Time:</b> 2024-02-14T04:41:46.361Z</p>
    <p><b>Total Duration:</b> 53.46 seconds</p>
    
      <h2>Test Steps</h2>
      <table border="1" cellspacing="0" cellpadding="5">
        <tr>
          <th>Step</th>
          <th>Name</th>
          <th>Status</th>
          <th>Start Time</th>
          <th>End Time</th>
        </tr>
        
          <tr>
            <td>1</td>
            <td>Navigate to the URL and ensure the page loads successfully</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:40:52.906Z</td>
            <td>2024-02-14T04:41:02.017Z</td>
          </tr>
          <tr>
            <td>2</td>
            <td>Perform login with valid credentials</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:41:02.019Z</td>
            <td>2024-02-14T04:41:27.264Z</td>
          </tr>
          <tr>
            <td>3</td>
            <td>Select 'Demo Organization Inc.' as the tenant name from the top-level tenant selection dropdown</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:41:27.268Z</td>
            <td>2024-02-14T04:41:27.489Z</td>
          </tr>
          <tr>
            <td>4</td>
            <td>Click on the 'Policies and Evidence' menu</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:41:27.490Z</td>
            <td>2024-02-14T04:41:32.720Z</td>
          </tr>
          <tr>
            <td>5</td>
            <td>Click on the 'Code Quality Infrastructure' tab</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:41:32.722Z</td>
            <td>2024-02-14T04:41:38.770Z</td>
          </tr>
          <tr>
            <td>6</td>
            <td>Click on the 'Code Quality' expansion arrow in the Code Quality Infrastructure page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:41:38.772Z</td>
            <td>2024-02-14T04:41:40.862Z</td>
          </tr>
          <tr>
            <td>7</td>
            <td>Click on the 'IDE Policy' control under code quality</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:41:40.864Z</td>
            <td>2024-02-14T04:41:44.593Z</td>
          </tr>
          <tr>
            <td>8</td>
            <td>Check whether the page navigates to the 'IDE Policy' page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:41:44.594Z</td>
            <td>2024-02-14T04:41:45.203Z</td>
          </tr>
          <tr>
            <td>9</td>
            <td>Close-browser</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:41:45.203Z</td>
            <td>2024-02-14T04:41:46.374Z</td>
          </tr>
      </table>