
<!-- ⚠️ DEMO DATA NOTICE: This is synthetic demonstration data created for EOH theme showcase purposes. All test results, names, and references are fictional and designed for educational use only. -->
<h1>Test Report: TC-EOH-PERF-0046</h1>
    <p><b>Run ID:</b> TR-EOH-PERF-0046</p>
    <p><b>Title:</b> Verify that when clicking the 'Integration Checklist' in the Software Construction Checklist expanded list, app navigates to the corresponding Integration Checklist or not</p>
    <p><b>Status:</b> <span class="status-passed">PASSED</span></p>
    <p><b>Start Time:</b> 2024-02-14T10:55:52.121Z</p>
    <p><b>End Time:</b> 2024-02-14T10:56:52.546Z</p>
    <p><b>Total Duration:</b> 60.42 seconds</p>
    
      <h2>Test Steps</h2>
      <table border="1" cellspacing="0" cellpadding="5">
        <tr>
          <th>Step</th>
          <th>Name</th>
          <th>Status</th>
          <th>Start Time</th>
          <th>End Time</th>
        </tr>
        
          <tr>
            <td>1</td>
            <td>Navigate to the URL and ensure the page loads successfully</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:55:52.133Z</td>
            <td>2024-02-14T10:56:03.494Z</td>
          </tr>
          <tr>
            <td>2</td>
            <td>Perform login with valid credentials</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:56:03.503Z</td>
            <td>2024-02-14T10:56:26.385Z</td>
          </tr>
          <tr>
            <td>3</td>
            <td>Select 'Demo Organization Inc.' as the tenant name from the top-level tenant selection dropdown</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:56:26.388Z</td>
            <td>2024-02-14T10:56:27.023Z</td>
          </tr>
          <tr>
            <td>4</td>
            <td>Click on the 'Policies and Evidence' menu</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:56:27.067Z</td>
            <td>2024-02-14T10:56:33.837Z</td>
          </tr>
          <tr>
            <td>5</td>
            <td>Click on the 'Code Quality Infrastructure' tab</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:56:33.838Z</td>
            <td>2024-02-14T10:56:43.692Z</td>
          </tr>
          <tr>
            <td>6</td>
            <td>Click on the 'Software construction checklist' expansion arrow in the Code Quality Infrastructure page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:56:43.693Z</td>
            <td>2024-02-14T10:56:46.184Z</td>
          </tr>
          <tr>
            <td>7</td>
            <td>Click on the 'Integration Checklist' control under Software construction checklist</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:56:46.185Z</td>
            <td>2024-02-14T10:56:50.504Z</td>
          </tr>
          <tr>
            <td>8</td>
            <td>Check whether the page navigates to the 'Integration Checklist' page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:56:50.505Z</td>
            <td>2024-02-14T10:56:51.597Z</td>
          </tr>
          <tr>
            <td>9</td>
            <td>Close-browser</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:56:51.599Z</td>
            <td>2024-02-14T10:56:52.586Z</td>
          </tr>
      </table>