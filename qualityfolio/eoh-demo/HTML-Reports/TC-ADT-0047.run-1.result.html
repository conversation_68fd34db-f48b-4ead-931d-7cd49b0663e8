<h1>Test Report: TC-ADT-0047</h1>
    
    <p><b>Title:</b> Ensure that upon clicking the displayed search results,which are are presented in a succinct format, and upon navigation, the page should display a message indicating 'Access Denied'</p>
    <p><b>Status:</b> <span class="status-failed">FAILED</span></p>
    <p><b>Start Time:</b> 2025-03-18T03:45:35.784Z</p>
    <p><b>End Time:</b> 2025-03-18T03:48:55.896Z</p>
    <p><b>Total Duration:</b> 200.11 seconds</p>
    
      <h2>Test Steps</h2>
      <table border="1" cellspacing="0" cellpadding="5">
        <tr>
          <th>Step</th>
          <th>Name</th>
          <th>Status</th>
          <th>Start Time</th>
          <th>End Time</th>
        </tr>
        
          <tr>
            <td>1</td>
            <td>Navigate to the URL and ensure the page loads successfully</td>
            <td class="status-passed">passed</td>
            <td>2025-03-18T03:45:35.788Z</td>
            <td>2025-03-18T03:45:39.008Z</td>
          </tr>
          <tr>
            <td>2</td>
            <td>Perform login with valid credentials</td>
            <td class="status-passed">passed</td>
            <td>2025-03-18T03:45:39.012Z</td>
            <td>2025-03-18T03:45:51.384Z</td>
          </tr>
          <tr>
            <td>3</td>
            <td>Select 'Netspective' as the tenant name from the top-level tenant selection dropdown</td>
            <td class="status-passed">passed</td>
            <td>2025-03-18T03:45:51.395Z</td>
            <td>2025-03-18T03:45:54.476Z</td>
          </tr>
          <tr>
            <td>4</td>
            <td> Locate the searchbox and enter search keyword</td>
            <td class="status-passed">passed</td>
            <td>2025-03-18T03:45:54.478Z</td>
            <td>2025-03-18T03:45:54.526Z</td>
          </tr>
          <tr>
            <td>5</td>
            <td>Verify that clicking any of the filtered search results, 'Access Denied' message is displayed</td>
            <td class="status-failed">failed</td>
            <td>2025-03-18T03:45:54.527Z</td>
            <td>2025-03-18T03:48:55.828Z</td>
          </tr>
          <tr>
            <td>6</td>
            <td>Close the browser</td>
            <td class="status-passed">passed</td>
            <td>2025-03-18T03:48:55.796Z</td>
            <td>2025-03-18T03:48:55.911Z</td>
          </tr>
          <tr>
            <td>7</td>
            <td>Worker Cleanup</td>
            <td class="status-passed">passed</td>
            <td>2025-03-18T03:48:55.911Z</td>
            <td>2025-03-18T03:48:55.911Z</td>
          </tr>
      </table>