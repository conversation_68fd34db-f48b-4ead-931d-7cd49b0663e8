
<!-- ⚠️ DEMO DATA NOTICE: This is synthetic demonstration data created for EOH theme showcase purposes. All test results, names, and references are fictional and designed for educational use only. -->
<h1>Test Report: TC-EOH-PERF-0008</h1>
    <p><b>Run ID:</b> TR-EOH-PERF-0008</p>
    <p><b>Title:</b> Verify that when clicking the 'Application Performance Policy' in the Code Quality expanded list, app navigates to the corresponding Application Performance Policy or not</p>
    <p><b>Status:</b> <span class="status-passed">PASSED</span></p>
    <p><b>Start Time:</b> 2024-02-14T04:39:01.923Z</p>
    <p><b>End Time:</b> 2024-02-14T04:39:51.803Z</p>
    <p><b>Total Duration:</b> 49.88 seconds</p>
    
      <h2>Test Steps</h2>
      <table border="1" cellspacing="0" cellpadding="5">
        <tr>
          <th>Step</th>
          <th>Name</th>
          <th>Status</th>
          <th>Start Time</th>
          <th>End Time</th>
        </tr>
        
          <tr>
            <td>1</td>
            <td>Navigate to the URL and ensure the page loads successfully</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:39:01.974Z</td>
            <td>2024-02-14T04:39:13.948Z</td>
          </tr>
          <tr>
            <td>2</td>
            <td>Perform login with valid credentials</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:39:13.961Z</td>
            <td>2024-02-14T04:39:35.884Z</td>
          </tr>
          <tr>
            <td>3</td>
            <td>Select 'Demo Organization Inc.' as the tenant name from the top-level tenant selection dropdown</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:39:35.885Z</td>
            <td>2024-02-14T04:39:36.043Z</td>
          </tr>
          <tr>
            <td>4</td>
            <td>Click on the 'Policies and Evidence' menu</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:39:36.074Z</td>
            <td>2024-02-14T04:39:39.117Z</td>
          </tr>
          <tr>
            <td>5</td>
            <td>Click on the 'Code Quality Infrastructure' tab</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:39:39.119Z</td>
            <td>2024-02-14T04:39:43.018Z</td>
          </tr>
          <tr>
            <td>6</td>
            <td>Click on the 'Code Quality' expansion arrow in the Code Quality Infrastructure page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:39:43.019Z</td>
            <td>2024-02-14T04:39:46.026Z</td>
          </tr>
          <tr>
            <td>7</td>
            <td>Click on the 'Application Performance Policy' control under code quality</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:39:46.027Z</td>
            <td>2024-02-14T04:39:49.149Z</td>
          </tr>
          <tr>
            <td>8</td>
            <td>Check whether the page navigates to the 'Application Performance Policy' page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:39:49.150Z</td>
            <td>2024-02-14T04:39:51.457Z</td>
          </tr>
          <tr>
            <td>9</td>
            <td>Close-browser</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:39:51.458Z</td>
            <td>2024-02-14T04:39:51.875Z</td>
          </tr>
      </table>