
<!-- ⚠️ DEMO DATA NOTICE: This is synthetic demonstration data created for EOH theme showcase purposes. All test results, names, and references are fictional and designed for educational use only. -->
<h1>Test Report: TC-EOH-PERF-0045</h1>
    <p><b>Run ID:</b> TR-EOH-PERF-0045</p>
    <p><b>Title:</b> Verify that when clicking the 'High Quality Routines Checklist' in the Software Construction Checklist expanded list, app navigates to the corresponding High Quality Routines Checklist or not</p>
    <p><b>Status:</b> <span class="status-passed">PASSED</span></p>
    <p><b>Start Time:</b> 2024-02-14T11:12:19.539Z</p>
    <p><b>End Time:</b> 2024-02-14T11:12:56.782Z</p>
    <p><b>Total Duration:</b> 37.24 seconds</p>
    
      <h2>Test Steps</h2>
      <table border="1" cellspacing="0" cellpadding="5">
        <tr>
          <th>Step</th>
          <th>Name</th>
          <th>Status</th>
          <th>Start Time</th>
          <th>End Time</th>
        </tr>
        
          <tr>
            <td>1</td>
            <td>Navigate to the URL and ensure the page loads successfully</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T11:12:19.547Z</td>
            <td>2024-02-14T11:12:25.501Z</td>
          </tr>
          <tr>
            <td>2</td>
            <td>Perform login with valid credentials</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T11:12:25.513Z</td>
            <td>2024-02-14T11:12:44.011Z</td>
          </tr>
          <tr>
            <td>3</td>
            <td>Select 'Demo Organization Inc.' as the tenant name from the top-level tenant selection dropdown</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T11:12:44.017Z</td>
            <td>2024-02-14T11:12:44.150Z</td>
          </tr>
          <tr>
            <td>4</td>
            <td>Click on the 'Policies and Evidence' menu</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T11:12:44.189Z</td>
            <td>2024-02-14T11:12:46.284Z</td>
          </tr>
          <tr>
            <td>5</td>
            <td>Click on the 'Code Quality Infrastructure' tab</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T11:12:46.285Z</td>
            <td>2024-02-14T11:12:50.795Z</td>
          </tr>
          <tr>
            <td>6</td>
            <td>Click on the 'Software construction checklist' expansion arrow in the Code Quality Infrastructure page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T11:12:50.796Z</td>
            <td>2024-02-14T11:12:52.754Z</td>
          </tr>
          <tr>
            <td>7</td>
            <td>Click on the 'High Quality Routines Checklist' control under Software construction checklist</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T11:12:52.755Z</td>
            <td>2024-02-14T11:12:55.891Z</td>
          </tr>
          <tr>
            <td>8</td>
            <td>Check whether the page navigates to the 'High Quality Routines Checklist' page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T11:12:55.892Z</td>
            <td>2024-02-14T11:12:56.376Z</td>
          </tr>
          <tr>
            <td>9</td>
            <td>Close-browser</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T11:12:56.377Z</td>
            <td>2024-02-14T11:12:56.810Z</td>
          </tr>
      </table>