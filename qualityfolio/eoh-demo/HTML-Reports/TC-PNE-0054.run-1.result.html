
<!-- ⚠️ DEMO DATA NOTICE: This is synthetic demonstration data created for EOH theme showcase purposes. All test results, names, and references are fictional and designed for educational use only. -->
<h1>Test Report: TC-EOH-PERF-0054</h1>
    <p><b>Run ID:</b> TR-EOH-PERF-0054</p>
    <p><b>Title:</b> Verify that when clicking the 'Requirements Checklist' in the Software Construction Checklist expanded list, app navigates to the corresponding Requirements Checklist or not</p>
    <p><b>Status:</b> <span class="status-passed">PASSED</span></p>
    <p><b>Start Time:</b> 2024-02-14T10:56:52.598Z</p>
    <p><b>End Time:</b> 2024-02-14T10:57:50.809Z</p>
    <p><b>Total Duration:</b> 58.21 seconds</p>
    
      <h2>Test Steps</h2>
      <table border="1" cellspacing="0" cellpadding="5">
        <tr>
          <th>Step</th>
          <th>Name</th>
          <th>Status</th>
          <th>Start Time</th>
          <th>End Time</th>
        </tr>
        
          <tr>
            <td>1</td>
            <td>Navigate to the URL and ensure the page loads successfully</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:56:52.602Z</td>
            <td>2024-02-14T10:57:03.858Z</td>
          </tr>
          <tr>
            <td>2</td>
            <td>Perform login with valid credentials</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:57:03.861Z</td>
            <td>2024-02-14T10:57:26.247Z</td>
          </tr>
          <tr>
            <td>3</td>
            <td>Select 'Demo Organization Inc.' as the tenant name from the top-level tenant selection dropdown</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:57:26.249Z</td>
            <td>2024-02-14T10:57:26.453Z</td>
          </tr>
          <tr>
            <td>4</td>
            <td>Click on the 'Policies and Evidence' menu</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:57:26.454Z</td>
            <td>2024-02-14T10:57:31.936Z</td>
          </tr>
          <tr>
            <td>5</td>
            <td>Click on the 'Code Quality Infrastructure' tab</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:57:31.939Z</td>
            <td>2024-02-14T10:57:41.684Z</td>
          </tr>
          <tr>
            <td>6</td>
            <td>Click on the 'Software construction checklist' expansion arrow in the Code Quality Infrastructure page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:57:41.685Z</td>
            <td>2024-02-14T10:57:43.947Z</td>
          </tr>
          <tr>
            <td>7</td>
            <td>Click on the 'Requirements Checklist' control under Software construction checklist</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:57:43.949Z</td>
            <td>2024-02-14T10:57:48.905Z</td>
          </tr>
          <tr>
            <td>8</td>
            <td>Check whether the page navigates to the 'Requirements Checklist' page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:57:48.906Z</td>
            <td>2024-02-14T10:57:49.809Z</td>
          </tr>
          <tr>
            <td>9</td>
            <td>Close-browser</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:57:49.810Z</td>
            <td>2024-02-14T10:57:50.824Z</td>
          </tr>
      </table>