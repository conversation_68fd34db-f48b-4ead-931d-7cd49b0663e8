
<!-- ⚠️ DEMO DATA NOTICE: This is synthetic demonstration data created for EOH theme showcase purposes. All test results, names, and references are fictional and designed for educational use only. -->
<h1>Test Report: TC-EOH-PERF-0035</h1>
    <p><b>Run ID:</b> TR-EOH-PERF-0035</p>
    <p><b>Title:</b> Verify that when clicking the 'Commenting Technique Checklist' in the Software Construction Checklist expanded list, app navigates to the corresponding Commenting Technique Checklist or not</p>
    <p><b>Status:</b> <span class="status-passed">PASSED</span></p>
    <p><b>Start Time:</b> 2024-02-14T10:52:53.861Z</p>
    <p><b>End Time:</b> 2024-02-14T10:53:59.166Z</p>
    <p><b>Total Duration:</b> 65.31 seconds</p>
    
      <h2>Test Steps</h2>
      <table border="1" cellspacing="0" cellpadding="5">
        <tr>
          <th>Step</th>
          <th>Name</th>
          <th>Status</th>
          <th>Start Time</th>
          <th>End Time</th>
        </tr>
        
          <tr>
            <td>1</td>
            <td>Navigate to the URL and ensure the page loads successfully</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:52:53.886Z</td>
            <td>2024-02-14T10:53:09.410Z</td>
          </tr>
          <tr>
            <td>2</td>
            <td>Perform login with valid credentials</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:53:09.425Z</td>
            <td>2024-02-14T10:53:32.369Z</td>
          </tr>
          <tr>
            <td>3</td>
            <td>Select 'Demo Organization Inc.' as the tenant name from the top-level tenant selection dropdown</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:53:32.370Z</td>
            <td>2024-02-14T10:53:32.774Z</td>
          </tr>
          <tr>
            <td>4</td>
            <td>Click on the 'Policies and Evidence' menu</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:53:32.825Z</td>
            <td>2024-02-14T10:53:39.716Z</td>
          </tr>
          <tr>
            <td>5</td>
            <td>Click on the 'Code Quality Infrastructure' tab</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:53:39.717Z</td>
            <td>2024-02-14T10:53:49.200Z</td>
          </tr>
          <tr>
            <td>6</td>
            <td>Click on the 'Software construction checklist' expansion arrow in the Code Quality Infrastructure page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:53:49.202Z</td>
            <td>2024-02-14T10:53:51.477Z</td>
          </tr>
          <tr>
            <td>7</td>
            <td>Click on the 'Commenting Technique Checklist' control under Software construction checklist</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:53:51.478Z</td>
            <td>2024-02-14T10:53:56.945Z</td>
          </tr>
          <tr>
            <td>8</td>
            <td>Check whether the page navigates to the 'Commenting Technique Checklist' page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:53:56.949Z</td>
            <td>2024-02-14T10:53:57.882Z</td>
          </tr>
          <tr>
            <td>9</td>
            <td>Close-browser</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T10:53:57.883Z</td>
            <td>2024-02-14T10:53:59.267Z</td>
          </tr>
      </table>