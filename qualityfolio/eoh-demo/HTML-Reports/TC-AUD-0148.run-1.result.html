
<!-- ⚠️ DEMO DATA NOTICE: This is synthetic demonstration data created for EOH theme showcase purposes. All test results, names, and references are fictional and designed for educational use only. -->
<h1>Test Report: TC-EOH-SEC-0148</h1>
    
    <p><b>Title:</b> Validate character limit for Authentication Token field in the Create Report popup</p>
    <p><b>Status:</b> <span class="status-passed">PASSED</span></p>
    <p><b>Start Time:</b> 2024-07-03T05:49:43.877Z</p>
    <p><b>End Time:</b> 2024-07-03T05:50:18.111Z</p>
    <p><b>Total Duration:</b> 34.23 seconds</p>
    
      <h2>Test Steps</h2>
      <table border="1" cellspacing="0" cellpadding="5">
        <tr>
          <th>Step</th>
          <th>Name</th>
          <th>Status</th>
          <th>Start Time</th>
          <th>End Time</th>
        </tr>
        
          <tr>
            <td>1</td>
            <td>Navigate to the URL and ensure the page loads successfully</td>
            <td class="status-passed">passed</td>
            <td>2024-07-03T05:49:43.881Z</td>
            <td>2024-07-03T05:49:46.807Z</td>
          </tr>
          <tr>
            <td>2</td>
            <td>Perform login with valid credentials</td>
            <td class="status-passed">passed</td>
            <td>2024-07-03T05:49:46.812Z</td>
            <td>2024-07-03T05:49:55.994Z</td>
          </tr>
          <tr>
            <td>3</td>
            <td>Select 'Demo Organization Inc.' as the tenant name from the top-level tenant selection dropdown</td>
            <td class="status-passed">passed</td>
            <td>2024-07-03T05:49:56.047Z</td>
            <td>2024-07-03T05:49:58.080Z</td>
          </tr>
          <tr>
            <td>4</td>
            <td>Navigate to the review Page</td>
            <td class="status-passed">passed</td>
            <td>2024-07-03T05:49:58.083Z</td>
            <td>2024-07-03T05:50:00.013Z</td>
          </tr>
          <tr>
            <td>5</td>
            <td>Navigate to the AICPA Page</td>
            <td class="status-passed">passed</td>
            <td>2024-07-03T05:50:00.014Z</td>
            <td>2024-07-03T05:50:00.732Z</td>
          </tr>
          <tr>
            <td>6</td>
            <td>Verify SOC2 Type I link navigation</td>
            <td class="status-passed">passed</td>
            <td>2024-07-03T05:50:00.732Z</td>
            <td>2024-07-03T05:50:03.120Z</td>
          </tr>
          <tr>
            <td>7</td>
            <td>Search for review session</td>
            <td class="status-passed">passed</td>
            <td>2024-07-03T05:50:03.120Z</td>
            <td>2024-07-03T05:50:03.139Z</td>
          </tr>
          <tr>
            <td>8</td>
            <td>Click the View button</td>
            <td class="status-passed">passed</td>
            <td>2024-07-03T05:50:03.140Z</td>
            <td>2024-07-03T05:50:07.111Z</td>
          </tr>
          <tr>
            <td>9</td>
            <td>Click the Document Tab</td>
            <td class="status-passed">passed</td>
            <td>2024-07-03T05:50:07.112Z</td>
            <td>2024-07-03T05:50:09.158Z</td>
          </tr>
          <tr>
            <td>10</td>
            <td>Click the Create Report button</td>
            <td class="status-passed">passed</td>
            <td>2024-07-03T05:50:09.159Z</td>
            <td>2024-07-03T05:50:11.222Z</td>
          </tr>
          <tr>
            <td>11</td>
            <td>Validation check for title with less than 3 characters</td>
            <td class="status-passed">passed</td>
            <td>2024-07-03T05:50:11.222Z</td>
            <td>2024-07-03T05:50:13.554Z</td>
          </tr>
          <tr>
            <td>12</td>
            <td>Validation check for title with more than 20 characters</td>
            <td class="status-passed">passed</td>
            <td>2024-07-03T05:50:13.554Z</td>
            <td>2024-07-03T05:50:15.762Z</td>
          </tr>
          <tr>
            <td>13</td>
            <td>Validation check for title with valid title between 3 and 20 characters</td>
            <td class="status-passed">passed</td>
            <td>2024-07-03T05:50:15.763Z</td>
            <td>2024-07-03T05:50:17.981Z</td>
          </tr>
          <tr>
            <td>14</td>
            <td>Close the browser</td>
            <td class="status-passed">passed</td>
            <td>2024-07-03T05:50:17.981Z</td>
            <td>2024-07-03T05:50:18.125Z</td>
          </tr>
      </table>