
<!-- ⚠️ DEMO DATA NOTICE: This is synthetic demonstration data created for EOH theme showcase purposes. All test results, names, and references are fictional and designed for educational use only. -->
<h1>Test Report: TC-EOH-PERF-0009</h1>
    <p><b>Run ID:</b> TR-EOH-PERF-0009</p>
    <p><b>Title:</b> Verify that when clicking the 'Code Continuous Integration Policy' in the Code Quality expanded list, app navigates to the corresponding Code Continuous Integration Policy or not</p>
    <p><b>Status:</b> <span class="status-passed">PASSED</span></p>
    <p><b>Start Time:</b> 2024-02-14T04:39:01.979Z</p>
    <p><b>End Time:</b> 2024-02-14T04:39:57.379Z</p>
    <p><b>Total Duration:</b> 55.40 seconds</p>
    
      <h2>Test Steps</h2>
      <table border="1" cellspacing="0" cellpadding="5">
        <tr>
          <th>Step</th>
          <th>Name</th>
          <th>Status</th>
          <th>Start Time</th>
          <th>End Time</th>
        </tr>
        
          <tr>
            <td>1</td>
            <td>Navigate to the URL and ensure the page loads successfully</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:39:02.000Z</td>
            <td>2024-02-14T04:39:14.169Z</td>
          </tr>
          <tr>
            <td>2</td>
            <td>Perform login with valid credentials</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:39:14.180Z</td>
            <td>2024-02-14T04:39:37.556Z</td>
          </tr>
          <tr>
            <td>3</td>
            <td>Select 'Demo Organization Inc.' as the tenant name from the top-level tenant selection dropdown</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:39:37.558Z</td>
            <td>2024-02-14T04:39:37.895Z</td>
          </tr>
          <tr>
            <td>4</td>
            <td>Click on the 'Policies and Evidence' menu</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:39:37.925Z</td>
            <td>2024-02-14T04:39:42.609Z</td>
          </tr>
          <tr>
            <td>5</td>
            <td>Click on the 'Code Quality Infrastructure' tab</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:39:42.610Z</td>
            <td>2024-02-14T04:39:48.013Z</td>
          </tr>
          <tr>
            <td>6</td>
            <td>Click on the 'Code Quality' expansion arrow in the Code Quality Infrastructure page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:39:48.016Z</td>
            <td>2024-02-14T04:39:50.448Z</td>
          </tr>
          <tr>
            <td>7</td>
            <td>Click on the 'Code Continuous Integration Policy' control under code quality</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:39:50.455Z</td>
            <td>2024-02-14T04:39:53.733Z</td>
          </tr>
          <tr>
            <td>8</td>
            <td>Check whether the page navigates to the 'Code Continuous Integration Policy' page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:39:53.737Z</td>
            <td>2024-02-14T04:39:54.656Z</td>
          </tr>
          <tr>
            <td>9</td>
            <td>Close-browser</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:39:54.658Z</td>
            <td>2024-02-14T04:39:57.432Z</td>
          </tr>
      </table>