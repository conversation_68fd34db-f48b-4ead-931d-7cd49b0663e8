<h1>Test Report: TC-ADR-0012</h1>
    
    <p><b>Title:</b> Verify Duplicate Session Name Handling During Session creating with an already existing name</p>
    <p><b>Status:</b> <span class="status-passed">PASSED</span></p>
    <p><b>Start Time:</b> 2024-12-19T04:57:30.449Z</p>
    <p><b>End Time:</b> 2024-12-19T04:57:59.374Z</p>
    <p><b>Total Duration:</b> 28.93 seconds</p>
    
      <h2>Test Steps</h2>
      <table border="1" cellspacing="0" cellpadding="5">
        <tr>
          <th>Step</th>
          <th>Name</th>
          <th>Status</th>
          <th>Start Time</th>
          <th>End Time</th>
        </tr>
        
          <tr>
            <td>1</td>
            <td>Navigate to the URL and ensure the page loads successfully</td>
            <td class="status-passed">passed</td>
            <td>2024-12-19T04:57:30.460Z</td>
            <td>2024-12-19T04:57:35.735Z</td>
          </tr>
          <tr>
            <td>2</td>
            <td>Log in with valid  admin credentials. </td>
            <td class="status-passed">passed</td>
            <td>2024-12-19T04:57:35.748Z</td>
            <td>2024-12-19T04:57:35.961Z</td>
          </tr>
          <tr>
            <td>3</td>
            <td>Perform login with valid credentials</td>
            <td class="status-passed">passed</td>
            <td>2024-12-19T04:57:35.962Z</td>
            <td>2024-12-19T04:57:49.218Z</td>
          </tr>
          <tr>
            <td>4</td>
            <td>Navigate to Admin Dashboard</td>
            <td class="status-passed">passed</td>
            <td>2024-12-19T04:57:49.273Z</td>
            <td>2024-12-19T04:57:55.742Z</td>
          </tr>
          <tr>
            <td>5</td>
            <td>Verify 'Create New Session' button functionality</td>
            <td class="status-passed">passed</td>
            <td>2024-12-19T04:57:55.743Z</td>
            <td>2024-12-19T04:57:57.220Z</td>
          </tr>
          <tr>
            <td>6</td>
            <td>Fill tenant name and due date with an already existing name</td>
            <td class="status-passed">passed</td>
            <td>2024-12-19T04:57:57.222Z</td>
            <td>2024-12-19T04:57:57.506Z</td>
          </tr>
          <tr>
            <td>7</td>
            <td>Save the session</td>
            <td class="status-passed">passed</td>
            <td>2024-12-19T04:57:57.509Z</td>
            <td>2024-12-19T04:57:57.599Z</td>
          </tr>
          <tr>
            <td>8</td>
            <td>Check if session exists validation is shown</td>
            <td class="status-passed">passed</td>
            <td>2024-12-19T04:57:57.601Z</td>
            <td>2024-12-19T04:57:57.617Z</td>
          </tr>
          <tr>
            <td>9</td>
            <td>Handle dialog if exists</td>
            <td class="status-passed">passed</td>
            <td>2024-12-19T04:57:57.618Z</td>
            <td>2024-12-19T04:57:57.619Z</td>
          </tr>
          <tr>
            <td>10</td>
            <td>Close session creation</td>
            <td class="status-passed">passed</td>
            <td>2024-12-19T04:57:57.620Z</td>
            <td>2024-12-19T04:57:58.696Z</td>
          </tr>
          <tr>
            <td>11</td>
            <td>Close the browser</td>
            <td class="status-passed">passed</td>
            <td>2024-12-19T04:57:58.701Z</td>
            <td>2024-12-19T04:57:59.425Z</td>
          </tr>
      </table>