
<!-- ⚠️ DEMO DATA NOTICE: This is synthetic demonstration data created for EOH theme showcase purposes. All test results, names, and references are fictional and designed for educational use only. -->
<h1>Test Report: TC-EOH-PERF-0014</h1>
    <p><b>Run ID:</b> TR-EOH-PERF-0014</p>
    <p><b>Title:</b> Verify that when clicking the 'Code Unit Testing Policy' in the Code Quality expanded list, app navigates to the corresponding Code Unit Testing Policy or not</p>
    <p><b>Status:</b> <span class="status-passed">PASSED</span></p>
    <p><b>Start Time:</b> 2024-02-14T04:39:51.889Z</p>
    <p><b>End Time:</b> 2024-02-14T04:40:44.767Z</p>
    <p><b>Total Duration:</b> 52.88 seconds</p>
    
      <h2>Test Steps</h2>
      <table border="1" cellspacing="0" cellpadding="5">
        <tr>
          <th>Step</th>
          <th>Name</th>
          <th>Status</th>
          <th>Start Time</th>
          <th>End Time</th>
        </tr>
        
          <tr>
            <td>1</td>
            <td>Navigate to the URL and ensure the page loads successfully</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:39:51.891Z</td>
            <td>2024-02-14T04:40:01.900Z</td>
          </tr>
          <tr>
            <td>2</td>
            <td>Perform login with valid credentials</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:40:01.902Z</td>
            <td>2024-02-14T04:40:24.713Z</td>
          </tr>
          <tr>
            <td>3</td>
            <td>Select 'Demo Organization Inc.' as the tenant name from the top-level tenant selection dropdown</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:40:24.714Z</td>
            <td>2024-02-14T04:40:25.012Z</td>
          </tr>
          <tr>
            <td>4</td>
            <td>Click on the 'Policies and Evidence' menu</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:40:25.013Z</td>
            <td>2024-02-14T04:40:30.385Z</td>
          </tr>
          <tr>
            <td>5</td>
            <td>Click on the 'Code Quality Infrastructure' tab</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:40:30.386Z</td>
            <td>2024-02-14T04:40:36.254Z</td>
          </tr>
          <tr>
            <td>6</td>
            <td>Click on the 'Code Quality' expansion arrow in the Code Quality Infrastructure page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:40:36.255Z</td>
            <td>2024-02-14T04:40:38.382Z</td>
          </tr>
          <tr>
            <td>7</td>
            <td>Click on the 'Code Unit Testing Policy' control under code quality</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:40:38.384Z</td>
            <td>2024-02-14T04:40:41.556Z</td>
          </tr>
          <tr>
            <td>8</td>
            <td>Check whether the page navigates to the 'Code Unit Testing Policy' page</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:40:41.559Z</td>
            <td>2024-02-14T04:40:43.139Z</td>
          </tr>
          <tr>
            <td>9</td>
            <td>Close-browser</td>
            <td class="status-passed">passed</td>
            <td>2024-02-14T04:40:43.140Z</td>
            <td>2024-02-14T04:40:44.803Z</td>
          </tr>
      </table>