.card-list-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
}

.card {
    flex-wrap: wrap;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    padding: 16px;
    text-align: left;
    transition: transform 0.3s, box-shadow 0.3s;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.card-image {
    width: 190px;
    height: 190px;
    -o-object-fit: cover;
    object-fit: cover;
    border-radius: 100px;
    margin-bottom: 10px;
    margin-top: 5px;
}

.card-title {
    font-size: 18px;
    margin: 10px 0;
}

.card-description {
    font-size: 14px;
    color: #555;
}

.additional-fields {
    margin-top: 10px;
    width: 100%;
}

.additional-fields p {
    margin-top: 0px !important;
    margin-bottom: 0px !important;
}