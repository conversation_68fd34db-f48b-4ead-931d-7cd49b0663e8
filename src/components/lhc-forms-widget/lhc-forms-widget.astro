---
import * as fs from "fs";
import { z } from "zod";
import LformWidget from "../lhc-forms-widget/index";
import moment from "moment";
import { existsSync } from "fs";

// Define the schema for the expected meta information structure
const LHCFormMetaInformationSchema = z.object({
  createdAt: z.string(),
  createdBy: z.string(),
  description: z.string(),
  title: z.string(),
  organization: z.string(),
});

// Define schemas for the structure of LHCFormData
const LHCFormExtensionSchema = z.object({
  url: z.string(),
  valueString: z.string(),
});

const LHCFormItemSchema = z.object({
  _text: z
    .object({
      extension: z.array(LHCFormExtensionSchema).optional(),
    })
    .optional(),
});

const LHCFormDataSchema = z.object({
  item: z.array(LHCFormItemSchema),
});

// Define TypeScript types from Zod schemas
type LHCFormMetaInformation = z.infer<typeof LHCFormMetaInformationSchema>;
type LHCFormData = z.infer<typeof LHCFormDataSchema>;

const { fileName, folderPath,pageSlug } = Astro.props;
console.log(folderPath+fileName)
function isLhcFormJson(filename: string): boolean {
  const regex = /\.lhc-form\./; // Matches ".lhc-form." anywhere in the filename
  return regex.test(filename);
}

if (!isLhcFormJson(fileName)) {
  return "Invalid file: The filename must follow the pattern '<name>.lhc-form.json'. Please check and rename the file accordingly.";
}

// let filePath = `dist/content/${folderPath}/${fileName}`;
// if (process.env.NODE_ENV == "development") {
//   filePath = `src/content/${folderPath}/${fileName}`;
// }

const filePath = `src/content/${folderPath}/${fileName}`;

if (!existsSync(filePath)) {
  return `File not found: ${filePath}. Please check the path.`;
}

const LHCFormString = fs.readFileSync(filePath, "utf8");
const LHCFormData = JSON.parse(LHCFormString);

// Function to extract meta information
function getLHCFormMetaInformation(
  lhcFormData: unknown
): LHCFormMetaInformation | null {
  // Validate and parse the input
  const parsedData = LHCFormDataSchema.safeParse(lhcFormData);
  if (!parsedData.success) return null;

  const metaInfoSection = parsedData.data.item.find((item) =>
    item._text?.extension?.some(
      (ext) =>
        ext.url === "http://hl7.org/fhir/StructureDefinition/rendering-xhtml" &&
        ext.valueString.includes('id="lhcFormMetaInformation"')
    )
  );

  if (!metaInfoSection) return null;

  const xhtmlExtension = metaInfoSection._text?.extension?.find(
    (ext) =>
      ext.url === "http://hl7.org/fhir/StructureDefinition/rendering-xhtml"
  );

  if (!xhtmlExtension?.valueString) return null;

  const match = xhtmlExtension.valueString.match(
    /id="lhcFormMetaInformation" value='([^']+)'/
  );

  try {
    const parsedMeta = match ? JSON.parse(match[1]) : null;
    return parsedMeta ? LHCFormMetaInformationSchema.parse(parsedMeta) : null;
  } catch {
    return null;
  }
}

const metaInfo = getLHCFormMetaInformation(LHCFormData);
---

<head>
  <link
    href="https://lhcforms-static.nlm.nih.gov/lforms-versions/36.8.0/webcomponent/styles.css"
    media="screen"
    rel="stylesheet"
  />
</head>

<div class="w-full max-w-10xl bg-white dark:bg-gray-800 shadow-lg rounded-lg p-6">
  <div
    class="grid grid-cols-1 md:grid-cols-12 gap-4 border-b dark:border-gray-600 mb-2 items-center"
  >
    <div class="col-span-1 md:col-span-8">
      <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-300">
        {metaInfo?.title}
      </h1>
    </div>
    <div class="col-span-1 md:col-span-4">
      <div class="flex justify-end">
        <div class="">
          <div>
            <span class="font-semibold inline-block w-48"></span>
            <span
              class="inline-flex items-center rounded-md bg-gray-50 dark:bg-gray-900 px-2 py-1 text-sm font-medium text-gray-600 dark:text-gray-300 ring-1 ring-gray-500/10 ring-inset"
              >{metaInfo?.organization || "N/A"}</span
            >
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="col-span-12">
    <div class="flex justify-end text-xs text-gray-500 dark:text-gray-300">
      <div class="">
        <div>
          <span class="font-semibold inline-block w-28">Created By: </span>
          <span class="inline-block">
            {metaInfo?.createdBy || "N/A"}
            {
              metaInfo?.createdAt
                ? moment(moment(metaInfo?.createdAt, "MM-DD-Y")).fromNow()
                : ""
            }
          </span>
        </div>
        <!-- <div>
          <span class="font-semibold inline-block w-28">Created At:</span><span
            class="inline-block">{metaInfo?.createdAt || "N/A"}</span
          >
        </div> -->
      </div>
    </div>
  </div>
  <div class="grid grid-cols-1 md:grid-cols-2 gap-2 text-gray-700 dark:text-gray-300">
    <div class="md:col-span-2">
      <div class="mb-2 mt-2">
        {metaInfo?.description || "No description available"}
      </div>
    </div>
  </div>
</div>
<LformWidget data={LHCFormString} fileName={pageSlug} client:only="react" />
