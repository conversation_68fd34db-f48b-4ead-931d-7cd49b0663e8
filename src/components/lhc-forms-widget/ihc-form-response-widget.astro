---

import LformWidget from "../lhc-forms-widget/index";
import { getFormData } from "./service";

const { fileName } = Astro.props;
const data = await getFormData(fileName);
---

<link
  href="https://lhcforms-static.nlm.nih.gov/lforms-versions/36.8.0/webcomponent/styles.css"
  media="screen"
  rel="stylesheet"
/>
<LformWidget
  fileName={data[0].uri.split("/").pop()}
  isUpdate={true}
  data={JSON.stringify(data[0].content, null, 2)}
  client:only="react"
/>
