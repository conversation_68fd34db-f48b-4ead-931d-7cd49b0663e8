---
import themeConfig from "../../../theme.config";
const { showViewMoreButton, compact } = Astro.props;

const newFiles = import.meta.glob("/src/content/**/*.{md,mdx}", {
  eager: true,
});

const whatsNextList = Object.values(newFiles)
  .filter(
    (file) =>
      file.frontmatter?.home?.whatsNext?.category === "whatsNext" &&
      file.frontmatter?.home?.whatsNext?.status === "on going"
  )
  .map((file) => {
    const relativePath = file.file
      .replace(/^.*\/src\/content/, '')
      .replace(/\.(md|mdx)$/, '');

    return {
      title: file.frontmatter?.title,
      path: `${relativePath}`,
      order: file.frontmatter?.home?.whatsNext?.order || 9999,
      summary: file.frontmatter?.summary || "",
    };
  })
  .filter((file) => file.title && file.path)
  .sort((a, b) => a.order - b.order);

// 🔹 Get limit from themeConfig
const widgetKey = 'poamNextSteps';
const widgetConfig = themeConfig.widgetTitle.find(widget => widget.value === widgetKey);
const limit = widgetConfig?.limit || 5;

const limitedWhatsNextList = showViewMoreButton ? whatsNextList.slice(0, limit) : whatsNextList;
---

{whatsNextList.length > 0 && (
  <div class={`lg:col-span-6 ${compact ? '' : 'bg-white dark:bg-gray-800 shadow rounded-lg p-4'}`}>
    <h3 class="text-lg font-semibold text-slate-700 dark:text-gray-300 flex justify-between items-center">
      <div class="flex gap-2 items-center text-xl">
        <span><img src="/assets/images/fi-rr-interactive.svg" class="w-6 h-6" alt="" /></span>
        <span>{widgetConfig?.label}</span>
        <img class="badgen-img-watch" src="https://badgen.net/badge/Status/Waiting?color=red&amp;scale=1.2" alt="Waiting" data-toggle="tooltip" data-html="true" data-placement="auto top" title="Waiting" />
      </div>

      {showViewMoreButton && (
        <a href="/expectations/plan-of-action-and-milestones/pom-whatsnext">
          <button class="rounded-md bg-white/10 px-2.5 py-1.5 text-sm font-semibold text-[#028db7] shadow-sm hover:bg-white/20 hover:text-black dark:hover:text-white" title="View More">
            View More
          </button>
        </a>
      )}
    </h3>

    <ul class="mt-2 space-y-0.5 font-semibold ml-8">
      {limitedWhatsNextList.map((file) => (
        <li>
          <a href={file.path} class="text-sm hover:text-black">
            {file.summary}
          </a>
        </li>
      ))}
    </ul>
  </div>
)}
