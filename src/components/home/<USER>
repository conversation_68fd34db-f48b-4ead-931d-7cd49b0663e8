---
import themeConfig from "../../../theme.config";

const { showViewMoreButton } = Astro.props;
const newFiles = import.meta.glob("/src/content/**/*.{md,mdx}", {
  eager: true,
});


const latestBlogList = Object.values(newFiles)
  .filter((file) => file.frontmatter?.home?.featuredBlog === true) // Filter by "skipTo" category
  .map((file) => {
    // Remove the base path '/src/content' from the file path
    const relativePath = file.file
      .replace(/^.*\/src\/content/, '')  // Remove everything up to '/src/content'
      .replace(/\.(md|mdx)$/, '');            // Remove the '.mdx' extension
    
    return {
      title: file.frontmatter?.title, // Get the title
      path: `${relativePath}` // Correct the path to be relative
    };
  })
  .filter((file) => file.title && file.path); // Ensure title and path exist

const widgetLimit = themeConfig.widgetTitle.find(widget => widget.value === 'featuredBlogs')?.limit || 5;
const limitedBlogList = showViewMoreButton ? latestBlogList.slice(0, widgetLimit) : latestBlogList;
---

{latestBlogList.length > 0 && (
  <div class="lg:col-span-6 bg-white dark:bg-gray-800 shadow rounded-lg p-4">
    <div class="flex items-center justify-between mb-2">
      <div class="flex gap-2 items-center text-xl font-semibold text-slate-700 dark:text-gray-300">
        <img src="/assets/images/fi-rr-interactive.svg" class="w-6 h-6" alt="" />
        <span>{themeConfig.widgetTitle.find(widget => widget.value === 'featuredBlogs')?.label}</span>
      </div>

      {showViewMoreButton && (
        <a href="/blog" title="View More">
          <button class="rounded-md bg-white/10 px-3 py-1.5 text-sm font-semibold text-[#028db7] shadow-sm hover:bg-white/20 hover:text-black dark:hover:text-white">
            View More
          </button>
        </a>
      )}
    </div>

    <ul class="mt-2 space-y-0.5 font-semibold ml-8">
      {limitedBlogList.map((file) => (
        <li>
          <a href={file.path} class="text-sm hover:text-black dark:hover:text-white">
            {file.title}
          </a>
        </li>
      ))}
    </ul>
  </div>
)}

