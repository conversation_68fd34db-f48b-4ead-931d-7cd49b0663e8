---
import themeConfig from "../../../theme.config";
const { showViewMoreButton, compact } = Astro.props;

const newFiles = import.meta.glob("/src/content/**/*.{md,mdx}", {
  eager: true,
});

const ItGovernanceList = Object.values(newFiles)
  .filter((file) => file.frontmatter?.home?.ItGovernance?.category === "it-governance")
  .map((file) => {
    const relativePath = file.file
      .replace(/^.*\/src\/content/, '')
      .replace(/\.(md|mdx)$/, '');

    return {
      title: file.frontmatter?.title,
      path: relativePath
    };
  })
  .filter((file) => file.title && file.path);
  const widgetLimit = themeConfig.widgetTitle.find(widget => widget.value === 'itGovernance')?.limit || 5;
---

{ItGovernanceList.length > 0 && (
  <div class={`lg:col-span-6 ${compact ? '' : 'bg-white dark:bg-gray-800 shadow rounded-lg p-4'}`}>
    <h3 class="text-lg font-semibold text-slate-700 dark:text-gray-300 flex justify-between items-center">
      <div class="flex gap-2 items-center text-xl">
        <span><img src="/assets/images/fi-rr-shield-check.svg" class="w-6 h-6" alt="" /></span>
        <span>{themeConfig.widgetTitle.find(widget => widget.value === 'itGovernance')?.label}</span>
      </div>
      {showViewMoreButton && (
        <a href="/expectations/compliance-readiness/list">
          <button class="rounded-md bg-white/10 px-2.5 py-1.5 text-sm font-semibold text-[#028db7] shadow-sm hover:bg-white/20 hover:text-black dark:hover:text-white" title="View More">
            View More
          </button>
        </a>
      )}
    </h3>

    {(showViewMoreButton ? ItGovernanceList.slice(0, widgetLimit) : ItGovernanceList).length > 0 ? (
      <ul class="mt-2 space-y-0.5 font-semibold ml-8">
        {(showViewMoreButton ? ItGovernanceList.slice(0, widgetLimit) : ItGovernanceList).map((file) => (
          <li>
            <a href={file.path} class="text-sm hover:text-black">
              {file.title}
            </a>
          </li>
        ))}
      </ul>
    ) : (
      <p class="text-gray-600">No IT Governance items available.</p>
    )}
  </div>
)}
