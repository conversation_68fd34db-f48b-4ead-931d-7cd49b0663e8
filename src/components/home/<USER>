---
const newFiles = import.meta.glob("/src/content/**/*.{md,mdx}", {
  eager: true,
});

const whatsNextList = Object.values(newFiles)
  .filter(
    (file) =>
      file.frontmatter?.home?.whatsNext?.category === "whatsNext" &&
      file.frontmatter?.home?.whatsNext?.status === "on going"
  )
  .map((file) => {
    // Remove the base path '/src/content' from the file path
    const relativePath = file.file
      .replace(/^.*\/src\/content/, '')  // Remove everything up to '/src/content'
      .replace(/\.(md|mdx)$/, '');             // Remove the '.mdx' extension

    return {
      title: file.frontmatter?.title, // Get the title
      path: `${relativePath}`, // Correct the path to be relative
      order: file.frontmatter?.home?.whatsNext?.order || 9999, // Default large number if order is missing
      summary: file.frontmatter?.summary || "", // Default empty string if summary is missing
    };
  })
  .filter((file) => file.title && file.path).sort((a, b) => a.order - b.order); // Sort based on order value; // Ensure title and path exist


---

{whatsNextList.length > 0 && (
  <div class="lg:col-span-6 bg-white dark:bg-gray-800 shadow rounded-lg p-4">
    <h3 class="text-lg font-semibold text-slate-700 dark:text-gray-300">
      <div class="flex gap-2 items-center text-xl">
        <span><img src="/assets/images/fi-rr-interactive.svg" class="w-6 h-6" alt="" /></span>
        <span>Plan of Action and Milestones (POA&M) – Next Steps</span>
        <img class="badgen-img-watch" src="https://badgen.net/badge/Status/Waiting?color=red&amp;scale=1.2" alt="Waiting" data-toggle="tooltip" data-html="true" data-placement="auto top" title="Waiting">
      </div>
    </h3>

    <ul class="mt-2 space-y-0.5 font-semibold ml-8">
      {whatsNextList.map((file) => {
        return (
          <li>
            <a href={file.path} class="text-sm hover:text-black">
              {file.summary}
            </a>
          </li>
        );
      })}
    </ul>
  </div>
)}
