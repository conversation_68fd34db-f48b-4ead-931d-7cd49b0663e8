---
const newFiles = import.meta.glob("/src/content/**/*.{md,mdx}", {
  eager: true,
});


const ItGovernanceList = Object.values(newFiles)
  .filter((file) => file.frontmatter?.home?.ItGovernance?.category === "it-governance")
  .map((file) => {
    // Remove the base path '/src/content' and file extensions (.md or .mdx)
    const relativePath = file.file
      .replace(/^.*\/src\/content/, '')  // Remove everything up to '/src/content'
      .replace(/\.(md|mdx)$/, '');       // Remove '.md' or '.mdx' extensions
    
    return {
      title: file.frontmatter?.title, // Get the title
      path: relativePath // Correct the path to be relative
    };
  })
  .filter((file) => file.title && file.path); // Ensure title and path exist


---

{ItGovernanceList.length > 0 && (
  <div class="lg:col-span-6 bg-white dark:bg-gray-800 shadow rounded-lg p-4">
    <h3 class="text-lg font-semibold text-slate-700 dark:text-gray-300">
      <div class="flex gap-2 items-center text-xl">
        <span><img src="/assets/images/fi-rr-shield-check.svg" class="w-6 h-6" alt="" /></span>
        <span>IT Governance, Risk and Compliance </span>
      </div>
    </h3>

    <ul class="mt-2 space-y-0.5 font-semibold ml-8">
      {ItGovernanceList.map((file) => {
        return (
          <li>
            <a href={file.path} class="text-sm hover:text-black">
              {file.title}
            </a>
          </li>
        );
      })}
    </ul>
  </div>
)}
