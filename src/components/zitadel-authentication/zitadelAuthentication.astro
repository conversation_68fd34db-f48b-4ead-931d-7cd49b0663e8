---
import ZitadelLogout from "./login";
import querystring from "querystring";

// Destructure Astro.props for clean access
const {
  clientId,
  authority,
  redirectUri,
  postLogoutRedirectUri,
  organizationId,
  projectId,
  operation,
} = Astro.props;

// Helper function to construct the logout URL
function createZitadelLogoutUrl() {
  const params = {
    id_token_hint: "<Token>",
    post_logout_redirect_uri: postLogoutRedirectUri,
    state: "<State>",
  };
  return `${authority}/oidc/v1/end_session?${querystring.stringify(params)}`;
}

const zitadelLogoutUrl = createZitadelLogoutUrl();
---

<ZitadelLogout
  client:only="react"
  organizationId={organizationId}
  clientId={clientId}
  authority={authority}
  redirectUri={redirectUri}
  postLogoutRedirectUri={postLogoutRedirectUri}
  projectId={projectId}
/>

<script is:inline define:vars={{ operation }}>
  if (operation === "login") {
    const state = new URL(window.location.href).searchParams.get("state");
    if (state) {
      document.cookie = `zitadel_state=${encodeURIComponent(state)}; path=/;`;
      localStorage.setItem("zitadel_state", state);
    }
  }
</script>

<script is:inline define:vars={{ zitadelLogoutUrl, operation }}>
  const deleteCookie = (key, domain = null) => {
    const cookieString = `${key}=; expires=Thu, 01 Jan 1970 00:00:01 GMT; path=/;`;
    document.cookie = domain
      ? `${cookieString} domain=${domain}`
      : cookieString;
  };

  if (operation === "logout") {
    const logoutAndClearData = () => {
      let logoutPath = zitadelLogoutUrl;

      // Replace placeholders with actual values
      logoutPath = decodeURI(logoutPath)
        .replace("<Token>", localStorage.getItem("zitadel_token"))
        .replace("<State>", localStorage.getItem("zitadel_state"));

      localStorage.removeItem("zitadel_token");
      localStorage.removeItem("zitadel_state");
      // Clear cookies, localStorage, and sessionStorage
      const cookiesToDelete = [
        "zitadel_state",
        "zitadel_token",
        "zitadel_user_name",
        "zitadel_access_token",
        "zitadel_user_id",
        "zitadel_tenant_id",
        "zitadel_user_email",
        "zitadel_user_roles",
        "zitadel_user_role",
      ];

      cookiesToDelete.forEach((key) => deleteCookie(key));
      deleteCookie("__Secure-zitadel.useragent", "idi.opsfolio.com");

      // start : Need to remove shortly
      deleteCookie("authState");
      deleteCookie("user_roles");
      deleteCookie("User");
      deleteCookie("is-logged-in");
      deleteCookie("email");
      deleteCookie("userId");
      deleteCookie("otContext");
      deleteCookie("token");
      deleteCookie("gravatar");
      deleteCookie("tenantId");
      deleteCookie("organizationName");
      // end : Need to remove shortly

      localStorage.clear();
      sessionStorage.clear();

      // Redirect to the logout URL
      window.location.href = logoutPath;
    };

    if (localStorage.getItem("zitadel_token")) {
      setTimeout(logoutAndClearData, 1000);
    }
  }
</script>
