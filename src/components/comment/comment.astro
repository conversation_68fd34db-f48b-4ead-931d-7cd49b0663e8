---
import CommentLog from "./comment";
import CommentService from "../../components/comment/commentService";

const path = Astro.url.pathname;
const comments = await CommentService.getComments(path);
const activities = await CommentService.getActivityLogs(path);
const user = Astro.cookies.get("zitadel_user_id")
const commentData = [...activities, ...comments];
commentData.sort((a, b) => {
  return new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime();
});
const {source,discussionEnabled}=Astro.props

---
{discussionEnabled ? (
  user && user.value ? (
    <CommentLog activities={commentData} url={path}  source={source}  client:only="react"/>
  ) : (
    <p class="text-md mt-1">You must be logged in to view comments.</p>
  )
) : null}
