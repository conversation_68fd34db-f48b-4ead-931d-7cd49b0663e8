---
import { Sequelize } from "sequelize";
import type { IGridStyle, IList } from "../../types";

import { decodeEntities } from "../../utils/decodeEntities";
import Table from "../Table/Table";
import AgGridComponent from "../AgGrid/AgGridComponent";
import { nmapDBPath,reportConfig } from "../../utils/env.ts";
import sqlFormatter from "@sqltools/formatter";

export interface Props {
  title: string;
  gridStyle: IGridStyle;
  connection: string;
  query: string;  
  page: string;
}

const {title,gridStyle = "xhtml",connection,page,} = Astro.props;
let cnMsg = "";
let sequelize: Sequelize | null = null;


const agGridSupportThemes = [
  "aggrid-default",
  "ag-theme-alpine",
  "ag-theme-material",
];

let children = "";
let errorlist = false;
let dataresult = false;
let list = [];

if (!Astro.slots.has("default")) {
  console.error("No children found in the slot.");
  //return new Response("No database connection", { status: 500 }); // Return an empty string if there's no content
}

children = String(await Astro.slots.render("default"));
console.log("Inside slot rendering");

let decodedChildren = decodeEntities(children);
console.log("Decoded children:", decodedChildren);

// Check if the connection exists in the config
if (!reportConfig[connection]) {
  console.error("No database connection available.");
  //return new Response("No database connection", { status: 500 });
}
//console.log("Connection key:", connection);
//console.log("Database path:", reportConfig[connection]);

if( reportConfig[connection]!== undefined){
  await createSequelize();
}else{
  cnMsg = "No database connection available.";
  //throw new Error("bar");
}

async function createSequelize() {
  try {
    if (!reportConfig[connection] || typeof reportConfig[connection] !== "string") {
      throw new Error(`Invalid database path: ${reportConfig[connection]}`);
      //cnMsg = "No connection to the database ";
    }

    // Trim the connection string to remove accidental spaces
    const dbPath = reportConfig[connection].trim();
    //console.log("Using DB Path:", dbPath);

    sequelize = new Sequelize(`sqlite:${dbPath}`);

    //console.log("Database connection successful");
  } catch (error) {
    cnMsg = "Error connecting to the database";
    console.error("Error connecting to the database:", error);
  }
}


if (!sequelize) {
  console.error("Sequelize instance is null. Cannot execute query.");
  //return;
}else{

  list = await sequelize.query(decodedChildren)
  .then((response) => {
    if (response.length > 0) {
      const returnArray: IList = { keys: [] };
      if (response[0][0] !== undefined) {
        const keys = Object.keys(response[0][0] as object);
        const responsetotype = response[0] as string[];
        returnArray.keys = keys;
        returnArray.array = responsetotype;
        return returnArray;
      } else {
        dataresult = true;
      }
    }
    return null;
  })
  .catch((err) => {
    console.error("Query execution error:", err);
    cnMsg = "Error executing the query";
  });
}
  
const chartData = JSON.stringify(list);
var linesnum = sqlFormatter.format(decodedChildren).split("\n");
---

<li class="link-card">
  <div class="card-wrapper">
    <h2>
      {title}
      <span>&rarr;</span>
    </h2>

    {
      errorlist ? (
        <div>
          <b> {"Database error please contact administrator!"}</b>
        </div>
      ) : dataresult ? (
        <div>
          <b> {"No data to display!"}</b>
        </div>
      ) : cnMsg ? (
        <div>
          <b> {cnMsg}</b>
        </div>
      ) : (
      <div class="sql-content">
         {agGridSupportThemes.includes(gridStyle) ? (



<div class="max-w-sm w-full bg-white rounded-lg shadow-sm dark:bg-gray-800 p-4 md:p-6">

  <div class="py-6" id="bar-chart"></div>

</div>

    <details
              id={title}
              class="mt-5 break-words"
              data-persist-state="closed"
            >
              <summary class="w-full">View SQL</summary>
              <div
                class={
                  linesnum.length > 3
                    ? "sql_style sql-format-color-shadow"
                    : "sql_style_1 sql-format-color-shadow"
                }
              >
                {sqlFormatter.format(decodedChildren.trim())}
              </div>
            </details>
  ) : (
    <Table list={list} />
  )}
          
        
      </div>
      )
    }
  </div>
</li>

<style>
  .link-card {
    list-style: none;
    display: flex;
    padding: 0.15rem;
    background-color: rgba(223, 214, 214, 0.17);
    background-image: var(--accent-gradient);
    background-size: 400%;
    border-radius: 0.5rem;
    background-position: 100%;
    transition: background-position 0.6s cubic-bezier(0.22, 1, 0.36, 1);
    box-shadow:
      0 4px 6px -1px rgba(39, 39, 39, 0.1),
      0 2px 4px -2px rgba(0, 0, 0, 0.1);
  }

  h2 {
    margin: 0;
    font-size: 1.25rem;
    transition: color 0.6s cubic-bezier(0.22, 1, 0.36, 1);
  }

  .link-card:is(:hover, :focus-within) {
    background-position: 0;
  }
  .link-card:is(:hover, :focus-within) h2 {
    color: rgb(var(--accent));
  }

  .card-wrapper {
    padding: 10px;
    width: 100%;
  }
  .sql-format-color-shadow {
    color: white !important;
    text-shadow: none !important;
    background-color: var(--tw-prose-pre-bg) !important;
  }
  .sql_style {
    z-index: 1;
    font-family: Consolas, Monaco, "Andale Mono", "Ubuntu Mono", monospace;
    word-wrap: normal;
    white-space: pre;
    height: 100%;
    width: 100%;
    pointer-events: none;
    text-shadow: none;
    padding: 20px;
  }
  .sql_style_1 {
    z-index: 1;
    font-family: Consolas, Monaco, "Andale Mono", "Ubuntu Mono", monospace;
    word-wrap: normal;
    white-space: pre-line;
    height: 100%;
    width: 100%;
    pointer-events: none;
    text-shadow: none;
    padding: 20px;
  }
</style>

<script define:vars={{ list }}>
if (list && list.array) {
    const logLevels = list.array.map(item => item["Log Level"]);
    const counts = list.array.map(item => item["Count"]);
    //console.log("Log Levels:", logLevels);
    //console.log("Counts:", counts);
} else {
    console.error("list or list.array is undefined!");
}

const logColorMap = {
  'INFO': "#31C48D",  // Green
  'WARNING': "#FACC15", // Yellow
  'CRITICAL': "#E11D48" // Red
};

const getBarChartOptions = (logLevels, counts, colors) => {
  return {
    series: [{
      name: "Log Count",
      data: counts,
      color: "#31C48D",
    }],
    chart: {
      type: "bar",
      height: 400,
      width: "100%",
      toolbar: {
        show: false,
      }
    },
    plotOptions: {
      bar: {
        horizontal: false,
        columnWidth: "50%",
        borderRadius: 6,
        dataLabels: {
          position: "top",
        },
      },
    },
    dataLabels: {
      enabled: true,
      style: {
        fontFamily: "Inter, sans-serif",
      },
    },
    legend: {
      show: true,
      position: "bottom",
    },
    tooltip: {
      shared: true,
      intersect: false,
      y: {
        formatter: function (value) {
          return value + " logs";
        }
      }
    },
    xaxis: {
      categories: logLevels,
      labels: {
        show: true,
        style: {
          fontFamily: "Inter, sans-serif",
          cssClass: 'text-xs font-normal fill-gray-500 dark:fill-gray-400'
        }
      }
    },
    yaxis: {
      labels: {
        show: true,
        style: {
          fontFamily: "Inter, sans-serif",
          cssClass: 'text-xs font-normal fill-gray-500 dark:fill-gray-400'
        }
      }
    },
    grid: {
      show: true,
      strokeDashArray: 4,
      padding: {
        left: 2,
        right: 2,
        top: -20
      },
    }
  };
};

setTimeout(() => {
  if (document.getElementById("bar-chart") && typeof ApexCharts !== 'undefined' && list && list.array) {
    const logLevels = list.array.map(item => item["Log Level"]);
    const counts = list.array.map(item => item["Count"]);
    const colors = logLevels.map(level => logColorMap[level] || "#CCCCCC"); // Default gray for unknown levels

    // console.log("Dynamic Log Levels:", logLevels);
    // console.log("Dynamic Counts:", counts);
    // console.log("Dynamic Colors:", colors);

    const chart = new ApexCharts(document.getElementById("bar-chart"), getBarChartOptions(logLevels, counts, colors));
    chart.render();
  }
}, 500);
</script>
