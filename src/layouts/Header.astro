---
import PageFind from "../components/PageFind.astro";
import { isZitadelEnabled } from "../utils/env";
import { Gravatar } from "../components/profile/gravatar/Gravatar";
import themeConfig from "../../theme.config";

const { logo, headerMenu, darkmodeLogo } = themeConfig || {};

const userId = Astro.cookies.get("zitadel_user_id")?.value || "";
const userRole = Astro.cookies.get("zitadel_user_role")?.value || "";
const email = Astro.cookies.get("zitadel_user_email");
const userName = Astro.cookies.get("zitadel_user_name");
const currentUrl = new URL(Astro.request.url);
const darkModeHide =
  currentUrl.pathname.includes('fleetfolio-service') || currentUrl.pathname.includes('qualityfolio');
---

<header
  class="bg-white shadow-sm border-b dark:border-gray-600 dark:bg-gray-900"
>
  <div
    class="max-w-full mx-auto px-3 sm:px-4 lg:px-4 flex justify-between items-center sm:flex-row flex-col"
  >
    <div class="flex items-center">
      <div
        class="text-lg font-bold text-gray-900 dark:text-gray-300 py-4 block dark:hidden"
      >
        <a href="/"><img src={logo} class="mr-3 h-10" alt="logo" /></a>
      </div>
      <div
        class="text-lg font-bold text-gray-900 dark:text-gray-300 py-4 hidden dark:block"
      >
        <a href="/"><img src={darkmodeLogo} class="mr-3 h-10" alt="logo" /></a>
      </div>
      <nav class="lg:flex flex-wrap font-semibold -mb-1.5">
        {
          headerMenu.map(
            ({ label, path, requiresAuth }) =>
              (!requiresAuth || userId) && (
                <a
                  href={path}
                  data-path={path}
                  class="menu-link py-4 px-3 inline-flex dark:text-gray-300 dark:border-gray-300"
                >
                  {label}
                </a>
              ),
          )
        }
      </nav>
    </div>
    <div class="flex items-center space-x-4 py-3">
      <PageFind />
      <!-- <div class="relative flex items-center space-x-4">
        <button
          class="relative text-gray-600 hover:text-gray-900 focus:outline-none"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="w-6 h-6"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            stroke-width="2"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.118V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.118c0 .417-.162.816-.405 1.113L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"
            ></path>
          </svg>
        </button>
        <span
          class="absolute top-[-13px] left-[-7px] bg-red-500 text-white text-xs font-bold px-2 py-0.5 rounded-full"
        >
          3
        </span>
      </div> -->
      {
        isZitadelEnabled && userId ? (
          <a
            href="#"
            href="#"
            id="dropdownDefaultButton"
            data-dropdown-toggle="dropdown"
          >
            <Gravatar
              userEmail={email ? email.value : ""}
              width={9}
              height={9}
            />
          </a>
        ) : (
          <span class="rounded-full overflow-hidden w-8 h-8">
            <a
              href="#"
              href="#"
              id="dropdownDefaultButton"
              data-dropdown-toggle="dropdown"
            >
              <img src="/assets/images/avatar-img.png" alt="" />
            </a>
          </span>
        )
      }
      <!-- Dropdown menu -->
      <div
        id="dropdown"
        class="z-10 hidden bg-white divide-y divide-gray-100 rounded-lg shadow w-44 dark:bg-gray-700"
      >
        <ul
          class="py-2 text-sm text-gray-700 dark:text-gray-200"
          aria-labelledby="dropdownDefaultButton"
        >
          {
            isZitadelEnabled && userId ? (
              <li>
                <span class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                  Welcome {userName ? userName.value.split(" ")[0] : ""}
                </span>
              </li>
            ) : (
              ""
            )
          }
          <li>
            <a
              href="/"
              class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white"
              >Dashboard</a
            >
          </li>
          {
            isZitadelEnabled && userId ? (
              <li>
                <a
                  href="/my-profile"
                  class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white"
                >
                  Profile
                </a>
              </li>
            ) : (
              ""
            )
          }

          {
            isZitadelEnabled && userRole.includes("admin") ? (
              <li>
                <a
                  href="/admin"
                  class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white"
                >
                  Administration
                </a>
              </li>
            ) : (
              ""
            )
          }

          {
            isZitadelEnabled &&
              (userId ? (
                <li>
                  <a
                    href="/logout"
                    class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white"
                  >
                    Sign out
                  </a>
                </li>
              ) : (
                <li>
                  <a
                    href="/post-authorization"
                    class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white"
                  >
                    Sign in
                  </a>
                </li>
              ))
          }
        </ul>
      </div>
      {!darkModeHide && (
        <button
          id="toggle-theme"
          class="text-muted dark:bg-gray-900 dark:text-gray-400 dark:focus:ring-gray-700 dark:hover:bg-gray-700 focus:outline-none focus:ring-4 focus:ring-gray-200 hover:bg-gray-100 inline-flex items-center p-2.5 rounded-lg text-sm"
          type="button"
          title="Toggle between Dark and Light mode"
          aria-label="Toggle between Dark and Light mode"
          data-aw-toggle-color-scheme=""
          ><svg
            class="w-6 h-6 md:h-5 md:inline-block md:w-5"
            data-icon="tabler:sun"
            height="1em"
            width="1em"
            ><symbol id="ai:tabler:sun" viewBox="0 0 24 24"
              ><path
                d="M8 12a4 4 0 1 0 8 0a4 4 0 1 0-8 0m-5 0h1m8-9v1m8 8h1m-9 8v1M5.6 5.6l.7.7m12.1-.7l-.7.7m0 11.4l.7.7m-12.1-.7l-.7.7"
                fill="none"
                stroke="currentColor"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"></path></symbol><use href="#ai:tabler:sun"></use></svg>
          </button>
      )}
    </div>
  </div>
</header>

<script>
  document.addEventListener("DOMContentLoaded", () => {
    const currentPath = window.location.pathname;
    const links = document.querySelectorAll(".menu-link");

    links.forEach((link) => {
      const path = link.dataset.path;

      // Ensure Home is selected only for the exact path "/"
      if (path === "/" && currentPath === path) {
        link.classList.add("text-black", "border-b-2", "border-slate-700");
        link.classList.remove("text-gray-700", "hover:text-black");
      }
      // Check for partial matches for other paths
      else if (path !== "/" && currentPath.startsWith(path)) {
        link.classList.add("text-black", "border-b-2", "border-slate-700");
        link.classList.remove("text-gray-700", "hover:text-black");
      } else {
        link.classList.add("text-gray-700", "hover:text-black");
        link.classList.remove("text-black", "border-b-2", "border-slate-700");
      }
    });
  });
</script>
<script>
  const path = window.location.pathname;
  if (path.includes("fleetfolio-service") || path.includes("qualityfolio")) {
    document.documentElement.classList.remove("dark");
  }
</script>