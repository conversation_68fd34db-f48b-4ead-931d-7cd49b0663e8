---
import Header from "./Header.astro";
import Footer from "./Footer.astro";
import themeConfig from "../../theme.config";

interface Props {
  title: string;
  enableEditButton?: boolean;
  redirect?: string;
  rawMarkdownContent?: string;
}

const { baseHyperLinkColor } = themeConfig || {};


const { title, enableEditButton, redirect, rawMarkdownContent } = Astro.props;

const enableOpenObserve =
  import.meta.env.ENABLE_OPEN_OBSERVE !== undefined
    ? import.meta.env.ENABLE_OPEN_OBSERVE
    : false;
let user: string;
if (Astro.cookies.get("User")?.value !== undefined) {
  user = Astro.cookies.get("User")!.value; // Using non-null assertion operator (!)
} else {
  user = "Unauth";
}
const userName = Astro.cookies.get("zitadel_user_name");
const userEmail = Astro.cookies.get("zitadel_user_email");

const enableSupportAndFeedback =
  import.meta.env.ENABLE_SUPPORT_AND_FEEDBACK !== undefined
    ? import.meta.env.ENABLE_SUPPORT_AND_FEEDBACK
    : false;
---

<!doctype html>
<html lang="en" style={`--hyperlink-color: ${baseHyperLinkColor || "#007BFF"}`}>
  <head>
    {redirect && <meta http-equiv="refresh" content={`0; url=${redirect}`} />}
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width" />
    <link rel="icon" type="image/svg+xml" href="/assets/images/favicon.svg" />
    <link rel="stylesheet" href="/assets/styles/tailwind.min.css" />
    <link rel="stylesheet" href="/assets/styles/basic.css" />
    <link rel="stylesheet" href="/assets/styles/custom.css" />

    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/flowbite/2.0.0/flowbite.min.css"
      rel="stylesheet"
    />
    <link href="/htmloutput/pagefind/pagefind-ui.css" rel="stylesheet" />
    <link
      rel="stylesheet"
      href="/assets/css/support-and-feedback/support-and-feedback.css"
    />
    <script src="/htmloutput/pagefind/pagefind-ui.js" is:inline></script>
    <script
      src="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.js"
    ></script>
    <script
      type="module"
      crossorigin="anonymous"
      src="https://lhcforms-static.nlm.nih.gov/lforms-versions/36.8.0/webcomponent/assets/lib/zone.min.js"
    ></script>
    <script
      type="module"
      crossorigin="anonymous"
      src="https://lhcforms-static.nlm.nih.gov/lforms-versions/36.8.0/webcomponent/lhc-forms.js"
    ></script>
    <script
      type="module"
      crossorigin="anonymous"
      src="https://lhcforms-static.nlm.nih.gov/lforms-versions/36.8.0/fhir/R4/lformsFHIR.min.js"
    ></script>

    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/ag-grid-community/styles/ag-grid.css"
    />

    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/ag-grid-community/styles/ag-theme-alpine.css"
    />
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/ag-grid-community/styles/ag-theme-material.css"
    />

    <script
      src="https://cdn.jsdelivr.net/npm/ag-grid-community/dist/ag-grid-community.min.noStyle.js"
    ></script>
    <script
      src="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.js"
      type="module"
      crossorigin="anonymous"></script>
    <script>
      if (!window.location.href.includes("qualityfolio")) {
        const script = document.createElement("script");
        script.src =
          "https://cdn.jsdelivr.net/npm/apexcharts@3.46.0/dist/apexcharts.min.js";
        script.defer = true;
        document.head.appendChild(script);
      }
    </script>

    {
      enableOpenObserve == "true" && (
        <>
          <script
            type="module"
            crossorigin="anonymous"
            src="/assets/scripts/index.9987069c.js"
          />
          <link rel="modulepreload" href="/assets/scripts/vendor.4c9b4c60.js" />
        </>
      )
    }
    <script is:inline>
      // Apply dark mode before rendering to prevent the white flash
      (function () {
        if (localStorage.getItem("theme") === "dark") {
          document.documentElement.classList.add("dark");
        }
      })();
    </script>
    <meta name="generator" content={Astro.generator} />
    <title>{title}</title>
  </head>
  <body class="bg-gray-40 dark:bg-gray-900 dark:text-white">
    <Header />
    <script define:vars={{ userName, userEmail }} is:inline>
      window.widgetConfig = {
        USER_EMAIL: userEmail ? userEmail.value : "Unauth",
        USER_FULL_NAME: userName ? userName.value : "Unauth",
      };
    </script>
    {
      enableSupportAndFeedback == "true" && (
        <script src="/assets/js/support-and-feedback.js" is:inline />
      )
    }

    <script is:inline>
      document.addEventListener("DOMContentLoaded", () => {
        const themeToggle = document.getElementById("toggle-theme");
        const htmlElement = document.documentElement;

        themeToggle?.addEventListener("click", () => {
          htmlElement.classList.toggle("dark");
          const newTheme = htmlElement.classList.contains("dark")
            ? "dark"
            : "light";
          localStorage.setItem("theme", newTheme);
        });
      });
    </script>

    <slot />
    <Footer enableEditButton={enableEditButton} rawMarkdownContent={rawMarkdownContent} />
  </body>
</html>
<script is:inline>
  document.addEventListener("DOMContentLoaded", function () {
    function removeHtmlExtensionAndFolder(href) {
      return href.replace(/\/htmloutput\//, "/").replace(/\.html$/, "");
    }

    // Function to apply URL modification to links
    function updateLinks() {
      const resultLinks = document.querySelectorAll(
        ".pagefind-ui__result-link"
      );
      resultLinks.forEach((link) => {
        const originalHref = link.getAttribute("href");
        if (originalHref) {
          const newHref = removeHtmlExtensionAndFolder(originalHref);
          link.href = newHref;
        }
      });

      // Remove the class `pagefind-ui__result-thumb` from elements
      const thumbElements = document.querySelectorAll(
        ".pagefind-ui__result-thumb"
      );
      thumbElements.forEach((element) => {
        element.classList.remove("pagefind-ui__result-thumb");
      });
    }

    // Event delegation: Run only when search input is clicked
    document.addEventListener("click", function (event) {
      if (event.target.matches(".pagefind-ui__search-input")) {
        // Add keyup event listeners only after search input is clicked
        window.addEventListener("keydown", function () {
          setTimeout(updateLinks, 1500);
        });
        window.addEventListener("keyup", function () {
          setTimeout(updateLinks, 500);
        });

        window.addEventListener("click", function () {
          setTimeout(updateLinks, 500);
        });
      }
    });

    // Add a click event listener to handle "Load more Results" button
  });
</script>

<style is:global>
  /* Hide separators after hidden breadcrumb items */
  /* Hide separator following hidden links */
  .c-breadcrumbs__link[hidden] + .c-breadcrumbs__separator {
    display: none;
  }

  /* OR hide the whole <li> if the <a> inside is hidden */
  .c-breadcrumbs__crumb:has(.c-breadcrumbs__link[hidden]) {
    display: none;
  }

  main a {
    color: var(--hyperlink-color, #007bff);
    text-decoration: none;
  }
  main a:hover {
    color: #000 !important;
    text-decoration: none;
  }
</style>

<script>
  import "../../public/assets/styles/PanZoomUi.css";
  import { PanZoomUi } from "@beoe/pan-zoom";

  // for BEOE diagrams
  document.querySelectorAll(".beoe").forEach((container) => {
    const element = container.firstElementChild;
    if (!element) return;
    // @ts-expect-error
    new PanZoomUi({ element, container }).on();
  });

  // for content images
  document
    .querySelectorAll(
      ".sl-markdown-content > img[src$='.svg' i]," +
        ".sl-markdown-content > p > img[src$='.svg' i]," +
        // for development environment
        ".sl-markdown-content > img[src$='f=svg' i]," +
        ".sl-markdown-content > img[src$='f=svg' i]"
    )
    .forEach((element) => {
      if (element.parentElement?.tagName === "PICTURE") {
        element = element.parentElement;
      }
      const container = document.createElement("figure");
      container.classList.add("beoe", "not-content");
      element.replaceWith(container);
      container.append(element);
      // @ts-expect-error
      new PanZoomUi({ element, container }).on();
    });
</script>
