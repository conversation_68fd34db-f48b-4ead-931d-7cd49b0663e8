---
import themeConfig from '../../theme.config';
const { version,themeReleaseNotesLink,editLink } = themeConfig || {}; 

const { url } = Astro;
const { enableEditButton } = Astro.props;

// Split the pathname into segments
const pathSegments = url.pathname.split("/").filter(Boolean);

// Initialize edit URL as null
let editUrl = null;

// Condition 1: Only create an edit link if there are exactly 3 parameters in the URL
if (pathSegments.length >= 2) {
  editUrl = `${editLink}${pathSegments.join("/")}.md`;
}

// Condition 2: Special case for "/documentation"
if (url.pathname === "/documentation") {
  editUrl = `${editLink}documentation/theme-documentation/documentation.md`; // Specify the file you want to edit for `/test`
}
---

<footer class="flex mt-4 items-center justify-between text-sm text-slate-700 bg-white border-t border-slate-200 dark:border-gray-600 pt-8 pb-6 px-4 dark:bg-gray-900 dark:text-white">
    <span class="flex-1 text-center">Copyright © 2025. All rights reserved. <br>Powered by <a href={themeReleaseNotesLink} target="_blank" class="underline text-gray-500 dark:text-gray-400">EOH</a>  
      <span>@{version}</span> </span>
    
    { enableEditButton && (
      <button type="button" class="text-white bg-blue-700 hover:bg-blue-800 focus:outline-none focus:ring-4 focus:ring-green-300 font-medium rounded-full text-sm px-5 py-2.5 text-center me-2 mb-2 dark:bg-green-600 dark:hover:bg-green-700 dark:focus:ring-green-800" title="Edit this page">
        <a href={editUrl} class="text-white-500 hover:underline ml-auto" target="_blank">
          Edit Page
        </a>  
      </button>
    )}
</footer>
