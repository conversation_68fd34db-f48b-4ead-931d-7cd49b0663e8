---
title: "Comment Integration"
---

# Comment Intergration

The _Comment_ component allows users to add, view, and engage in discussions. It is integrated into the application and can be enabled or disabled based on the `discussionEnabled` flag.

## Installation & Setup

Ensure that the following environment variables are set in your .env file:

- PUBLIC_RSSD_DB – Specifies the path to the resource surveillance database.
- PUBLIC_PRODUCTION_URL – Defines the production URL of the site.
- PUBLIC_NOVU_COMMENT_NOTIFICATION_TEMPLATE – Represents the template name used for comment notifications.
- PUBLIC_NOTIFICATION_FOR_ALL_MEMBERS - If set to "true," the comment notification will be sent to all members; otherwise, it will be sent only to the mentioned members.

```
PUBLIC_RSSD_DB=src/content/db/rssd/resource-surveillance.sqlite.db
PUBLIC_PRODUCTION_URL="https://demo.hub.opsfolio.com"
PUBLIC_NOVU_COMMENT_NOTIFICATION_TEMPLATE="comment-template"
PUBLIC_NOTIFICATION_FOR_ALL_MEMBERS=false

```

## Usage

To integrate the _Comment_ component, import it and configure the required properties:

```
import Comment from "../../../components/comment/comment.astro";

<Comment source="EOH" discussionEnabled={true} />

```

If discussionEnabled is true, the Comment component will be displayed, allowing users to engage in discussions.

If discussionEnabled is false, the Comment component will be hidden, and no discussion-related features will be available.

Source could be a site name, module name, or specific section where the comments belong.

If using multiple sources, it ensures comments are grouped accordingly.

## ⚠️ Note

For the _Comment_ component to function properly, make sure:

- The _database is generated_ before using the comments feature.
- The `PUBLIC_RSSD_DB` environment variable is correctly set in the `.env` file.

# Demo

import Comment from "../../../components/comment/comment.astro";

<Comment source="Demo" activityTitle="Comments" discussionEnabled={true} />
