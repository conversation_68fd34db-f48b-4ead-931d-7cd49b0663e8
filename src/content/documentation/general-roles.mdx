---
title: "General Roles Definition"
draft: true
---

## CEO

A chief executive officer (CEO) is the highest-ranking executive in a company. Broadly speaking, a chief executive officer’s primary responsibilities include making major corporate
decisions, managing the overall operations and resources of a company, acting as the main point of communication between the board of directors and corporate operations. In many
cases, the chief executive officer serves as the public face of the company. The CEO is elected by the board and its shareholders. They report to the chair and the board, who are
appointed by shareholders.

Specific tasks include:

- Oversee the strategic direction of an organization. Lower-level managers are often more engaged in the day-to-day operating activities of a company. A CEO usually
  synthesizes these results and decides on the long-term plans of a company.
- Implement changes and proposed plans. After crafting the long-term vision, a CEO usually looks to themselves and other executive leadership to begin implementing
  those plans. Changes are often directly implemented by operational managers, but it is ultimately up to the CEO to ensure the long-term plans are being followed
  through.
- Engage in media obligations and public relations. A CEO is often the face of the company, and this includes being involved in media relations. A CEO may speak at
  conferences, address the public on notable changes to the company, or participate in community events.
- Interact with other leadership executives. As companies grow more diverse, it is vital to the success of a company to have a suite of executives that a CEO can rely
  on. Instead of directly overseeing every aspect of a company, a CEO often relies on other leaders to manage their own realm, then engages with them to get a high level understanding of how things are going.
- Maintain accountability with the board. A Board of Directors oversees the entire company's performance and holds a CEO accountable. A CEO often reports to the
  board, delivers updates on strategic plans, and gets feedback from the board regarding the overall direction of the company.
- Monitor company performance. A CEO is ultimately responsible for the financial performance of a company. A CEO may rely on financial or non-financial metrics to
  track how things are going. They usually make reporting requests from their direct employees to get a quick sense of how each area in the company is performing and
  what strategic manoeuvrers should be taken.
- Setting precedence for the working culture and environment. A CEO is responsible for setting the tone at the top and creating the work environment they believe is
  best to drive success. Employees working under a CEO often look to the executive to create and maintain the culture of the organization.

## Privacy Officer

The Privacy Officer is responsible for the organization's Privacy Program including but not limited to daily operations of the program, development, implementation, and maintenance
of policies and procedures, monitoring program compliance, investigation and tracking of incidents and breaches and insuring patients' rights in compliance with federal and state
laws.

Specific tasks include:

- Builds a strategic and comprehensive privacy program that defines, develops, maintains and implements policies and processes that enable consistent, effective
  privacy practices which minimize risk and ensure the confidentiality of protected health information (PHI), paper and/or electronic, across all media types. Ensures
  privacy forms, policies, standards, and procedures are up-to-date.
- Works with organization senior management, security, and corporate compliance officer to establish governance for the privacy program.
- Serves in a leadership role for privacy compliance
- Collaborate with the information security officer to ensure alignment between security and privacy compliance programs including policies, practices,
  investigations, and acts as a liaison to the information systems department.
- Establishes, with the information security officer, an ongoing process to track, investigate and report inappropriate access and disclosure of protected health
  information. Monitor patterns of inappropriate access and/or disclosure of protected health information.
- Performs or oversees initial and periodic information privacy risk assessment/analysis, mitigation and remediation.
- Conducts related ongoing compliance monitoring activities in coordination with the organization's other compliance and operational assessment functions.
- Takes a lead role, to ensure the organization has and maintains appropriate privacy and confidentiality consents, authorization forms and information notices and materials reflecting current organization and legal practices and requirements.
- Oversees, develops and delivers initial and ongoing privacy training to the workforce.
- Participates in the development, implementation, and ongoing compliance monitoring of all business associates and business associate agreements, to ensure
  all privacy concerns, requirements, and responsibilities are addressed.
- Works cooperatively with the Health Information Management (HIM) Director and other applicable organization units in overseeing patient rights to inspect, amend,
  and restrict access to protected health information when appropriate.
- Manages all required breach determination and notification processes under HIPAA and applicable State breach rules and requirements.
- Establishes and administers a process for investigating and acting on privacy and security complaints
- Performs required breach risk assessment, documentation, and mitigation. Works with Human Resources to ensure consistent application of sanctions for privacy
  violations
- Initiates, facilitates and promotes activities to foster information privacy awareness within the organization and related entities.
- Maintains current knowledge of applicable federal and state privacy laws and accreditation standards.
- Works with organization administration, legal counsel, and other related parties to represent the organization's information privacy interests with external parties
  (state or local government bodies) who undertake to adopt or amend privacy
  legislation, regulation, or standard.
- Cooperates with the U.S. Department of Health and Human Service's Office for Civil Rights, State regulators and/or other legal entities in any compliance reviews or
  investigations.
- Serves as information privacy resource to the organization regarding release of information and to all departments for all privacy related issues.

## Project Manager

Project Manager is responsible for planning, analyzing, defining, executing and reviewing
high-level programs in the assigned areas through his/her functional expertise and an
understanding of the enterprise and the industry. He/she advises clients and develops
proposals to meet their business requirements; analyses and proactively responds to
project, financial, technical and operational risks; and is responsible for sustaining an
environment that optimizes individual/team contributions and for ensuring constant
business process improvement in order to create value.
The Project Manager is fully responsible and accountable for every activity associated with
the assigned project(s); including schedules, business resources, sourcing, logistics and
resolving issues; with the objective of fully meeting all the stated objectives of the project(s)
especially the time, quality and cost objectives of the project.

Specific tasks (to be performed directly or through subordinates) include:

- Planning and execution of the project to meet specific client requirements or
  product requirements.
- Application development to meet specific and/or anticipated client requirements.
- Forming, training, developing and directing teams and evaluating their
  performances.
- Review manpower adequacy and initiate action for new recruitment as required.
- Translating customers’ requirements into detailed Business Requirements and
  obtaining client approval and sign off.
- Developing the requirements and obtaining client approval.
- Ensuring that all phases of the SDLC are followed and quality of the products or
  services are not compromised in any way.
- Contributing to the refinement, growth and improvement of the organization’s
  processes.
- Address the training needs of team members.
- Interact with other project/product teams on a regular basis to facilitate knowledge
  creation and sharing.
- Take initiative in resolving issues as they occur. Be proactive in providing solutions to
  roadblocks.
- Interacting with various project/module teams on a regular basis, monitoring
  progress of projects. Introducing preventive measures and initiating corrective
  measures for course correction if the project is seen to deviate.
- Communicating critical project or product issues, to the development team, as they
  become known.
- It is essential that the Project Manager is well aware of the latest technologies so
  that they can help the team in making critical decisions and suggest alternative
  technologies, if required, based on requirements.
- The Project Manager is expected to be technically strong so to be able to provide
  valuable leadership throughout project execution.

## Team Leaders

Team Leaders is responsible for teams assigned to them. Working closely with the Project
Leader, Project Manager or even with CEO, they directly guide and oversee the progress of
project execution/development. They are also responsible for regular reviews, reporting,
monitoring the work and reporting of all members of their teams and providing inputs to
their reporting office on process improvement. They shall ensure that their
projects/development meets the stated objectives within the budgeted time and cost
figures.

Specific tasks include:

- Participating in the definition and planning of the specific modules.
- Be directly responsible for the execution of the plan.
- Play an active role in the design, implementation and developer testing of the
  product or service.
- Do regular code reviews to ensure the quality of the code.
- Provide guidance and support to team members
- Review manpower adequacy/excess and report to the reporting officer on time.
- Assess the training needs of team members and report to the reporting officer.
- Be responsible for the specific projects assigned, which may be project / product
  module development / implementation / customer support / application
  development or other specific tasks.
- Interact with other project teams to facilitate knowledge sharing
- Take initiative in resolving issues as they occur. Be proactive in providing solutions to
  roadblocks.

## Developers

Reporting to Project Leaders or team Leaders, the developers involve in the design and
development of the product components and its integrations.

Specific tasks include:

- Responsible for the Design, detailed design, coding, code reviews and unit testing
  assigned to them.
- Responsible for preparing any other documents related to the project as assigned by
  the reporting officer.
- Providing support to QC team to ensure testability on the functional level.
- Acquire knowledge related to product and technologies involved to successfully
  carry out the work assigned.
- Take initiative in resolving issues as they occur. Be proactive in providing solutions to
  roadblocks.
- Participate in reviews of all projects related documents like HRS, SRS, designs etc

## Quality Assurance and Testing

The QA and Testing team is responsible for ensuring the quality of deliverables - products
and projects - by defining test procedures, test plans and test cases and executing them.

Specific responsibilities include:

- Work closely with the development team to understand product requirements and
  functionalities; Review specifications to identify testing related tasks: test plans, test
  scripts/cases, test environment, etc.
- Develop effective test methodologies and tools for functional testing, negative
  testing, customer scenario testing, stress testing, performance testing, scalability
  testing, UI testing, test automation etc. Implement strategies to increase test
  coverage.
- Create re-usable functional regression test suites.
- Execute test scripts and plans. This may involve individually conducting these tests
  and/or leading and directing a team of test engineers.
- Ensure that test results are properly documented, executed and tracked.
- Communicate, update and track bugs or testing related issues to closure.
- Assist the development teams in reproducing and solving bugs and problems.
- Develop and maintain test automation infrastructure to fully support automated
  testing and reusability.
- Identify key experiences from previous releases and build into test plans to address
  what was learned from those experiences.
- Recognize and identify potential areas where existing policies and procedures
  require change, or where new ones need to be developed.
- Research and recommend new test tools and techniques.
- Identify opportunities for automation throughout the testing cycle.
- Organize and prioritize tasks and promote quality throughout the software
  development process.
- Develop, direct and evaluate the testing staff
- Identify, in the course of work, key business improvement opportunities.

## Test Lead

Specific responsibilities:

- Coordinating the system testing of all the products including testing for functional as
  well as non-functional aspects like performance, stress, scalability, UI etc.
- Prepare test plans for all development activities and scheduling the testing tasks
- Ensure appropriate test environment and data
- Test analysis and final reporting
- Administration of the web-based bug tracking system
- Initiating activities to improve the testing efficiency, usage of tools, improvement on
  existing methodologies, etc.
- Develop, direct and evaluate the QC staff
- Address the training needs of team members
- Implement strategies to increase test coverage.
- Develop and maintain test automation infrastructure to fully support automated
  testing and reusability
- Identify key experiences from previous releases and build into test plans to address
  what was learned from those experiences
- Recognize and identify potential areas where existing policies and procedures
  require change, or where new ones need to be developed
- Initiate research and recommend new test tools and techniques.
- Identify, in the course of work, key business improvement opportunities.

## Test Engineer

- Prepare test designs based on the approved SRS.
- Review test cases prepared from the test designs.
- Prepare appropriate test environment and data
- Test execution, analysis and reporting
- Bug tracking and closing
- Create re-usable functional regression test suites.
- Maintenance of the test ware
- Preparation of test cases based on the requirements.
- Preparing test programs
- Test execution
- Assist the development teams in reproducing and solving bugs
- Providing constructive suggestions on functionality and usability aspects.
- Bug reporting
