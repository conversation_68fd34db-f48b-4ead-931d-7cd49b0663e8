{"resourceType": "Questionnaire", "title": "SOC2 Type I Certification Evidence Collection Form", "status": "draft", "item": [{"item": [{"type": "string", "linkId": "843509936337", "text": "Organization Name", "required": true}, {"type": "string", "linkId": "834824167845", "text": "Organization Address", "required": true}, {"type": "string", "linkId": "574440750588", "text": "Product Name", "required": true}, {"type": "url", "linkId": "924260494641", "text": "Website", "required": true}, {"type": "string", "linkId": "403261366197", "text": "Point of Contact", "required": true}, {"type": "string", "linkId": "647686221633", "text": "Contact Email", "required": true}, {"type": "dateTime", "linkId": "960033154529", "text": "Date of Submission", "required": true}], "type": "group", "linkId": "323289382098", "text": "General Information"}, {"item": [{"item": [{"type": "string", "linkId": "766948998506", "text": "Please provide the URL to share the Employee Handbook or Manual.", "enableWhen": [{"question": "916961188865", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "766948998506_helpText", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, HRMS portal or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "256266245743", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have an employee handbook in place, you can refer our standard version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/policies/common/human-resource-quality-infrastructure/employee-handbook', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "916961188865", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "916961188865_helpText", "type": "display", "text": "As evidence, you can provide the Employee Handbook or Manual.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "916961188865", "code": [{"code": "FII-SCF-HRS-0005"}], "text": "Are core values are communicated from executive management to personnel through policies and the employee handbook?", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "display", "linkId": "529236227520", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have an Organizational structure in place, you can refer our standard version <span style=\"color:blue; text-decoration:none; cursor:pointer;\" onclick=\"window.open('https://suite.opsfolio.com/assets/org_structure.drawio.svg', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "294993139324", "operator": "=", "answerCoding": {"display": "No"}}]}, {"type": "string", "linkId": "850062740124", "text": "Please provide the URL to share the Organizational structure.", "enableWhen": [{"question": "294993139324", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "758996476433", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, Portal, or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"linkId": "294993139324_helpText", "type": "display", "text": "As evidence, you can provide the Organizational structure.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "294993139324", "code": [{"code": "FII-SCF-HRS-0003"}], "text": "Is management's organizational structure with relevant reporting lines documented in an organization chart?", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "string", "linkId": "590639128215", "text": "Please provide the URL to share sample of (5) company job descriptions.", "enableWhen": [{"question": "527640791166", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "480063497129", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, Portal, or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "619691601565", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have an sample of (5) company job descriptions, you can refer our standard version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/policies/common/human-resource-quality-infrastructure/general-roles', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "527640791166", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "527640791166_helpText", "type": "display", "text": "You can provide roles and responsibilities as evidence, including those of the CISO, CFO, Data Privacy Officer, and other relevant roles.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "527640791166", "code": [{"code": "FII-SCF-GOV-0004"}], "text": "Can management provide a sample of (5) company job descriptions?", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "string", "linkId": "299388771480", "text": "Please provide the URL to share the Employee Handbook or Manual.", "enableWhen": [{"question": "460559993229", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "775341028680", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, portal or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "161725988339", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have an employee handbook in place, you can refer our standard version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/policies/common/human-resource-quality-infrastructure/employee-handbook', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "460559993229", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "347124323083", "type": "display", "text": "You can provide Employee Manual or Handbook.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "460559993229", "code": [{"code": "FII-SCF-HRS-0005.1"}], "text": "Is there an employee handbook in place, and does it include the organization's entity values and behavioral standards? If yes, how is it made available for all employees?", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "string", "linkId": "592488151622", "text": "Please provide the URL to share the Hiring procedures.", "enableWhen": [{"question": "717681155587", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "889997111057", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, portal or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "897271751233", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have an Hiring procedures in place, you can refer our standard version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/policies/common/human-resource-quality-infrastructure/hiring-policy', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "717681155587", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "289569131304", "type": "display", "text": "You can provide Hiring procedures.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "717681155587", "code": [{"code": "FII-SCF-HRS-0004"}], "text": "Has management documented formal HR procedures that include the employee on-boarding process?", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "string", "linkId": "268992988810", "text": "Please provide the URL to share the hiring checklist for a sample of employees.", "enableWhen": [{"question": "195460520299", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "947780700524", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, portal or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "423075359378", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have an hiring checklist for a sample of employees, you can refer our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/assets/hiring-and-on-boarding-checklist.xls', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "195460520299", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "850117590152", "type": "display", "text": "You can provide the hiring checklist for a sample of employees", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "195460520299", "code": [{"code": "FII-SCF-HRS-0004"}], "text": "Are new hire checklists utilized, and activities documented to help facilitate new hire procedures?", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "string", "linkId": "253597832407", "text": "Please provide the URL to share the Hiring Policy.", "enableWhen": [{"question": "370468490673", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "448922027993", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, portal or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "212718912052", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have an Hiring Policy, you can refer our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\" onclick=\"window.open('https://suite.opsfolio.com/policies/common/human-resource-quality-infrastructure/hiring-policy', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "370468490673", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "661356300420", "type": "display", "text": "You can provide the evidence that a third party (if a third party is used for recruiting) or Hiring Policy", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "370468490673", "code": [{"code": "FII-SCF-HRS-0009.3"}], "text": "How are candidates recruited for job openings? Evidence could include the recruitment policies and procedures; a PowerPoint deck, a questionnaire, job opening postings, or emails.", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "string", "linkId": "366142256337", "text": "Please provide the URL to share the evidence that Continued Professional Education Training is tracked and monitored for employees (e.g. tracking tool / spreadsheet)", "enableWhen": [{"question": "************", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "497890868029", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, portal or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "143129622083", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have an Evidence that Continued Professional Education Training is tracked and monitored for employees (e.g. tracking tool / spreadsheet), you can refer our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/assets/professional-education-training-details.xlsx', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "************", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "166339955317", "type": "display", "text": "You can provide the evidence that Continued Professional Education Training is tracked and monitored for employees (e.g. tracking tool / spreadsheet)", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "************", "code": [{"code": "FII-SCF-HRS-0004.2"}], "text": "Are there any requirements for Continued Professional Education Training among employees?", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "string", "linkId": "************", "text": "Please provide the URL to share the evidence could include a links to the mentor program guide; a PowerPoint deck; emails", "enableWhen": [{"question": "************", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "************", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, portal or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "************", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have an evidence could include a links to the mentor program guide; a PowerPoint deck; emails, you can refer our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/assets/professional-education-training-details.xlsx', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "************", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "************", "type": "display", "text": "You can provide the evidence could include a links to the mentor program guide; a PowerPoint deck; emails", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "************", "code": [{"code": "FII-SCF-SAT-0002"}], "text": "Is there a mentor program to develop personnel in place?", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "string", "linkId": "************", "text": "Please provide the URL to share the evidence as Termination procedures", "enableWhen": [{"question": "************", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "426019701439", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, portal or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "485012925103", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have an evidence as Termination procedures, you can refer our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/policies/common/human-resource-quality-infrastructure/termination-policy', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "************", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "442634832827", "type": "display", "text": "You can provide the evidence as Termination procedures", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "************", "code": [{"code": "FII-SCF-HRS-0009"}], "text": "Has management documented formal HR procedures that include employee terminations?", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "string", "linkId": "938972869777", "text": "Please provide the URL to share the evidence as Termination checklist for a sample of employees", "enableWhen": [{"question": "935997113209", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "857226746736", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, portal or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "340143268267", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have an evidence as Termination checklist for a sample of employees, you can refer our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/assets/termination-and-Off-boarding-checklist.xls', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "935997113209", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "113292712689", "type": "display", "text": "You can provide the evidence as Termination checklist for a sample of employees", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "935997113209", "code": [{"code": "FII-SCF-HRS-0009.2"}], "text": "Are termination checklists utilized, and activities documented to facilitate termination procedures?", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "string", "linkId": "444995333773", "text": "Please provide the URL to share the evidence as listing of Executive Management (e.g. President, CIO, CTO, CEO, CFO, etc.) Members", "enableWhen": [{"question": "710547647178", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "512302810756", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, portal or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "959436404500", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have an evidence as Listing of Executive Management (e.g. President, CIO, CTO, CEO, CFO, etc.) Members, you can refer our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/assets/executive-management.xlsx', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "710547647178", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "767871626533", "type": "display", "text": "You can provide the evidence as listing of Executive Management (e.g. President, CIO, CTO, CEO, CFO, etc.) Members", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "710547647178", "code": [{"code": "FII-SCF-HRS-0002"}], "text": "Can you provide a listing of executive management members?", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "string", "linkId": "912811701496", "text": "Please provide the URL to share the evidence could include performance evaluation forms, tracking tool/spreadsheet, or certificates", "enableWhen": [{"question": "904763995104", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "435535481665", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, portal or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "279251979325", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have an evidence of performance evaluation forms, tracking tool/spreadsheet, or certificates, you can refer our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/assets/performance_appraisal_form.xlsx', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "904763995104", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "428080909354", "type": "display", "text": "You can provide the evidence could include performance evaluation forms, tracking tool/spreadsheet, or certificates", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "904763995104", "code": [{"code": "FII-SCF-HRS-0003.2"}], "text": "Are formal performance evaluations completed and documented? If yes, on what frequency and does this apply to all personnel? This includes executive management members such as C<PERSON>, CTO, CEO.", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "string", "linkId": "951162573926", "text": "Please provide the URL to share the evidence could include employee training documentation", "enableWhen": [{"question": "398369140416", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "447060333583", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, portal or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "947012786695", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have an evidence of employee training documentation, you can refer our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/assets/professional-education-training-details.xlsx', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "398369140416", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "744424251184", "type": "display", "text": "You can provide the evidence could include employee training documentation", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "398369140416", "code": [{"code": "FII-SCF-GOV-0005"}], "text": "Do employees complete periodic information security training?", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "string", "linkId": "945880403298", "text": "Please provide the URL to share the evidence could include cross-training employees documentation", "enableWhen": [{"question": "441480275309", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "265258165661", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, portal or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "466149180645", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have an evidence of cross-training employees documentation, you can refer our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/assets/professional-education-training-details.xlsx', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "441480275309", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "452773432373", "type": "display", "text": "You can provide the evidence as cross-training employees documentation", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "441480275309", "code": [{"code": "FII-SCF-GOV-0005.1"}], "text": "Are employees cross-trained in roles and responsibilities? Evidence could include training materials; policies and procedures relating to training requirements; PowerPoint deck", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "string", "linkId": "964464322774", "text": "Please provide the URL to share the evidence of anonymous hotline in place (whistleblower line)", "enableWhen": [{"question": "472692576006", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "915687599356", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, portal or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "381837758240", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have an evidence of anonymous hotline in place (whistleblower line), you can refer our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/policies/common/information-technology-quality-infrastructure/whistleblower-policy', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "472692576006", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "792435048661", "type": "display", "text": "You can provide the evidence of anonymous hotline in place (whistleblower line)", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "472692576006", "code": [{"code": "FII-SCF-HRS-0005.1"}], "text": "Is there an employee hotline in place?", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "string", "linkId": "277562966358", "text": "Please provide the URL to share the evidence of policies and procedures for reporting unethical behavior, emails related to reporting unethical behavior, a PowerPoint presentation, or a website.", "enableWhen": [{"question": "765376425719", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "294030648493", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, portal or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "851950410887", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have an evidence of policies and procedures for reporting unethical behavior, emails related to reporting unethical behavior, a PowerPoint presentation, or a website, you can refer our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/policies/common/human-resource-quality-infrastructure/employee-handbook', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "765376425719", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "863237236184", "type": "display", "text": "You can provide evidence of policies and procedures for reporting unethical behavior, emails related to reporting unethical behavior, a PowerPoint presentation, or a website.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "765376425719", "code": [{"code": "FII-SCF-HRS-0006.1"}], "text": "Are employees, third-parties, and customers directed on how to report unethical behaviour in a confidential manner?", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "string", "linkId": "391935446035", "text": "Please provide the URL to share the evidence of Internal Controls Matrix.", "enableWhen": [{"question": "122839727725", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "558564812687", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, portal or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "783281060178", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have an evidence of Internal Controls Matrix, you can refer our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/assets/internal-controls-matrix.xlsx', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "122839727725", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "222072239307", "type": "display", "text": "You can provide evidence of Internal Controls Matrix", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "122839727725", "code": [{"code": "FII-SCF-HRS-0011"}], "text": "Does management maintain an Internal Controls Matrix that includes a list of implemented controls within the environment and technology infrastructure, ownership and operation of each control, control type (manual, automated, preventive, detective, corrective), frequency of operation (daily, weekly, monthly, quarterly, yearly, multiple times a day), and documentation of the relationship between business processes, relevant technologies, and controls securing those processes?", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "string", "linkId": "796056107528", "text": "Please provide the URL to share the evidence of operational reports that show the operational/system performance and internal controls effectiveness.", "enableWhen": [{"question": "387467035956", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "657338254165", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, portal or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "509793170498", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have an evidence of operational reports that show the operational/system performance and internal controls effectiveness, you can refer our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\" onclick=\"window.open('https://suite.opsfolio.com/assets/management-review-meeting.docx', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "387467035956", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "619936506280", "type": "display", "text": "You can provide evidence of operational reports(Meeting Minutes) that show the operational/system performance and internal controls effectiveness", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "387467035956", "code": [{"code": "FII-SCF-GOV-0001.1"}], "text": "Does management meet on a regular basis to discuss organizational goals and objectives? (This could be operations management, IT management, executive management, or a combination of these.)", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}], "type": "group", "linkId": "671921888775", "text": "Common Criteria Related to Control Environment"}, {"item": [{"item": [{"type": "string", "linkId": "562687850605", "text": "Please provide the URL to share the Description of system/services..", "enableWhen": [{"question": "683163909840", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "529001882135", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, Portal, or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "521500592845", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have an description of system/services documentation, you can refer our sample version  <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/assets/server-list.xlsx', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "683163909840", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "592330886531", "type": "display", "text": "As evidence, you can provide the Description of system/services..", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "683163909840", "code": [{"code": "FII-SCF-AST-0004"}], "text": "Has management documented the description of their system for the services provided to their customers? A system description would include the following components used to provide services to their customers: Infrastructure (facilities, hardware, equipment), Software, People, Procedures, and Data.", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "string", "linkId": "542612221042", "text": "Please provide the URL to share the Organizational structure.", "enableWhen": [{"question": "490498907662", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "210258347995", "type": "display", "text": "As evidence, you may include a URL along with details from Dropbox, Box, Google Drive, Portal, JIRA, Confluence, GitHub, or any other storage platform that holds the documentation confirming management published the company's organizational structure.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "769306935484", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have an Organizational structure in place, you can refer our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/assets/org_structure.drawio.svg', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "490498907662", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "401090108606", "type": "display", "text": "As evidence, you may include a URL along with details from Dropbox, Box, Google Drive, Portal, JIRA, Confluence, GitHub, or any other storage platform that holds the documentation confirming management published the company's organizational structure.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "490498907662", "code": [{"code": "FII-SCF-NET-0006.1"}], "text": "Has management published the company's organizational structure?", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "string", "linkId": "946994226499", "text": "Please provide the URL to share the Privacy Policy.", "enableWhen": [{"question": "471582030921", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "776998638474", "type": "display", "text": "As evidence, you may include a URL along with details from Dropbox, Box, Google Drive, Portal, JIRA, Confluence, GitHub, or any other storage platform that holds the Privacy Notice posted on your website and software or the Privacy Policy.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "129196039837", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have an Privacy notice posted on website and software, you can refer the our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/policies/common/information-technology-quality-infrastructure/privacy-management-policy', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "471582030921", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "884907262455", "type": "display", "text": "As evidence, you may include a URL along with details from Dropbox, Box, Google Drive, Portal, JIRA, Confluence, GitHub, or any other storage platform that holds the Privacy Notice posted on your website and software or the Privacy Policy", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "471582030921", "code": [{"code": "FII-SCF-PRI-0001"}], "text": "How are the entity's security/confidentiality responsibilities communicated to customers?", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"type": "string", "linkId": "138462999411", "code": [{"code": "FII-SCF-IRO-0010"}], "text": "How do customers report system failures and security breaches?", "item": [{"linkId": "216619441762", "type": "display", "text": "As evidence, you may include a URL along with details from Dropbox, Box, Google Drive, Portal, JIRA, Confluence, GitHub, or any other storage platform that holds documentation demonstrating that customers receive procedures for reporting system failures and security breaches (if not included in the contract) or a feedback and support form/email evidence.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"item": [{"type": "string", "linkId": "736507258806", "text": "Please provide the URL to share the Incident Response Policies and Procedures.", "enableWhen": [{"question": "296807821886", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "173286528419", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, Portal, or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "342714132382", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have an Incident Response Policies and Procedures, you can refer the our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/policies/common/information-technology-quality-infrastructure/incident-response-policy', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "296807821886", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "725469154963", "type": "display", "text": "As evidence, you can provide that Incident Response Policies and Procedures.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "296807821886", "code": [{"code": "FII-SCF-IRO-0001"}], "text": "Has management documented a formal Incident Response Policy?", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"type": "string", "linkId": "305589023699", "code": [{"code": "FII-SCF-CHG-0002"}], "text": "How are system changes communicated to employees?", "item": [{"linkId": "209695595928", "type": "display", "text": "As evidence, you may include a URL along with details from Dropbox, Box, Google Drive, Portal, JIRA, Confluence, GitHub, or any other storage platform that holds documentation (e.g., JIRA/Open Project/GitHub tickets) demonstrating that system changes are communicated to employees.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "string", "linkId": "960471447612", "code": [{"code": "FII-SCF-CHG-0005"}], "text": "How are system changes communicated to customers?", "item": [{"linkId": "340832084694", "type": "display", "text": "As evidence, you may include a URL along with details from Dropbox, Box, Google Drive, Portal, JIRA, Confluence, GitHub, or any other storage platform that holds documentation (e.g., release notes, Jira/Open Project/GitHub tickets) or email demonstrating that system changes are communicated to customers.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "140276124407", "code": [{"code": "FII-SCF-AST-0004"}], "text": "Data flow diagrams, process flow charts, and narratives identifying how data flows within the environment including the relevant information sources and systems.", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}], "item": [{"linkId": "844806904891", "type": "display", "text": "As evidence, you may include a URL along with details from Dropbox, Box, Google Drive, Portal, JIRA, Confluence, GitHub, or any other storage platform that holds the Data Flow Diagram.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "string", "linkId": "876142325211", "code": [{"code": "FII-SCF-MON-0001.7"}], "text": "File integrity monitoring software configurations and example alert generated from the file integrity monitoring software.", "item": [{"linkId": "545264238729", "type": "display", "text": "As evidence, you may include a URL along with details from Dropbox, Box, Google Drive, Portal, JIRA, Confluence, GitHub, or any other storage platform that holds the file integrity monitoring log.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "string", "linkId": "948510289315", "code": [{"code": "FII-SCF-NET-0012.2"}], "text": "VPN authentication configurations including password settings", "item": [{"linkId": "271207369869", "type": "display", "text": "As evidence, you may include a URL along with details from Dropbox, Box, Google Drive, Portal, or any other storage platform that holds the VPN authentication configuration details.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"item": [{"type": "string", "linkId": "538892748284", "text": "Please provide the URL to share the Information Security Awareness Training new hire testing (sample) details..", "enableWhen": [{"question": "464222582092", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "696288934788", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, Portal, or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "944839850680", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have an Information Security Awareness Training new hire testing (sample) details as evidence, you can refer the our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/assets/professional-education-training-details.xlsx', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "464222582092", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "745566013229", "type": "display", "text": "As evidence, you can provide a Information Security Awareness Training new hire testing (sample) details.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "464222582092", "code": [{"code": "FII-SCF-SAT-0001"}], "text": "For a sample of new hires, evidence that information security and awareness training was performed upon hire. Evidence could include training completion forms, tracking tool/spreadsheet, or certificates", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "string", "linkId": "918204804074", "text": "Please provide the URL to share the Information Security Awareness Training current employee testing (sample) details.", "enableWhen": [{"question": "931792165707", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "153323392723", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, Portal, or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "952881964917", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have an Information Security Awareness Training current employee testing (sample) details as evidence, you can refer the our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/assets/professional-education-training-details.xlsx', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "931792165707", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "876384287973", "type": "display", "text": "As evidence, you can provide a Information Security Awareness Training current employee testing (sample) details.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "931792165707", "code": [{"code": "FII-SCF-SAT-0002"}], "text": "For a sample of current employees, evidence that information security and awareness training was performed annually. Evidence could include training completion forms, tracking tool/spreadsheet, or certificates", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "string", "linkId": "493170533519", "text": "Please provide the URL to share the Policies are available to vendors/contractors.", "enableWhen": [{"question": "747566513113", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "540219450058", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, Portal, or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "868908191237", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have a Policies are available to vendors/contractors as evidence, you can refer the our sample version of\n<span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/policies/common/information-technology-quality-infrastructure/incident-management-policy', '_blank')\">Incident Management Policy </span>, \n<span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/policies/common/information-technology-quality-infrastructure/change-management-policy', '_blank')\">Change Management Policy </span>, \n<span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/policies/common/information-technology-quality-infrastructure/information-security-policy', '_blank')\">Information Security Policy </span></p>"}]}, "enableWhen": [{"question": "747566513113", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "546481998366", "type": "display", "text": "As evidence, you can provide a Policies are available to vendors/contractors.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "747566513113", "code": [{"code": "FII-SCF-TPM-0002"}], "text": "Evidence showing key policies and procedures (e.g. Information Security, Change Management, Incident Management) were available to external parties (contractors, third parties, vendors, customers)) via company [website, shared drive, intranet, email, or contract/agreement, etc.]", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "string", "linkId": "880331464381", "text": "Please provide the URL to Escalation Policy details.", "enableWhen": [{"question": "172536737637", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "910238071164", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, Portal, or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "754017991471", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have an Escalation Policy details as evidence, you can refer the our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/policies/common/information-technology-quality-infrastructure/escalation-processes', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "172536737637", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "323472062929", "type": "display", "text": "As evidence, you can provide a Escalation Policy details.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "172536737637", "code": [{"code": "FII-SCF-IRO-0014"}], "text": "The Escalation policies and procedures for reporting failures, concerns, incidents, and complaints with revision history AND evidence that these escalation policies and procedures were available to external parties (contractors, third parties, vendors, customers) via company [website, shared drive, intranet, email, or contract/agreement, etc.]", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "string", "linkId": "316538566721", "text": "Please provide the URL to List of new customers.", "enableWhen": [{"question": "280402298184", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "656052030267", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, Portal, or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "953250311012", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have List of new customers as evidence, you can refer the our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/assets/list-of-new-customer.xlsx', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "280402298184", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "177534079321", "type": "display", "text": "As evidence, you can provide a List of new customers.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "280402298184", "code": [{"code": "FII-SCF-IAC-0002"}], "text": "Listing of new customers during the audit period", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"type": "string", "linkId": "777584296488", "code": [{"code": "FII-SCF-BCD-0010.1"}], "text": "Contract (service agreement) for a sample of new customers", "item": [{"linkId": "145378887626", "type": "display", "text": "As evidence, you can provide a URL along with details from Dropbox, Box, Google Drive, Portal, or other storage platforms that hold sample contracts for new customers.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}], "type": "group", "linkId": "688660921201", "text": "Common Criteria Related to Information and Communications "}, {"item": [{"item": [{"type": "string", "linkId": "840840352670", "text": "Please provide the URL to Risk Assessment/Management Policy details.", "enableWhen": [{"question": "************", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "884795523698", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, Portal, or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "254358462696", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have Risk Assessment/Management Policy details as evidence, you can refer the our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/policies/common/information-technology-quality-infrastructure/risk-management-policy', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "************", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "442642457190", "type": "display", "text": "As evidence, you can provide Risk Assessment/Management Policy details.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "************", "code": [{"code": "FII-SCF-RSK-0001"}], "text": "Please describe your annual risk assessment process in regards to your service under review. (i.e., Risk assessments should identify, quantify, and prioritize risks against criteria for risk acceptance and objectives relevant to the organization. The results should guide and determine the appropriate management action and priorities for managing information security risks and for implementing controls selected to protect against these risks.)", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "string", "linkId": "************", "text": "Please provide the URL to Documented (1) compliance manager job description (2) audit manager job description and (3) risk management job description details.", "enableWhen": [{"question": "************", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "************", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, Portal, or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "879384515419", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have Documented (1) compliance manager job description (2) audit manager job description and (3) risk management job description details as evidence, you can refer the our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/policies/common/information-technology-quality-infrastructure/general-role', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "************", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "222775951498", "type": "display", "text": "As evidence, you can provide Documented (1) compliance manager job description (2) audit manager job description and (3) risk management job description details.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "************", "code": [{"code": "FII-SCF-HRS-0003"}], "text": "Are there documented position descriptions for Risk Manager or Audit Manager?", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "string", "linkId": "955976295603", "text": "Please provide the URL to Organizational business plans, records of budgets kept, etc.", "enableWhen": [{"question": "622544475164", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "636270141315", "type": "display", "text": "As evidence, you may include a URL along with details from Dropbox, Box, Google Drive, Portal, JIRA, Confluence, GitHub, or any other storage platform that holds documentation such as organizational business plans (meeting minutes) and records of budgets kept.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "108912595610", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have Organizational business plans, records of budgets kept, etc. as evidence, you can refer the our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/assets/management-review-meeting.docx', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "622544475164", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "766528483769", "type": "display", "text": "As evidence, you may include a URL along with details from Dropbox, Box, Google Drive, Portal, JIRA, Confluence, GitHub, or any other storage platform that holds documentation such as organizational business plans (meeting minutes) and records of budgets kept.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "622544475164", "code": [{"code": "FII-SCF-BCD-0006"}], "text": "Does entity maintain organizational business plans and budgets?", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"type": "string", "linkId": "915675977204", "code": [{"code": "FII-SCF-CPL-0001"}], "text": "Is entity's control framework based on a recognized (NIST 800-53; COBIT; ISO; COSA) framework?", "item": [{"linkId": "977332566339", "type": "display", "text": "As evidence, you can provide report; certification; policies/procedures/ PowerPoint deck.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"item": [{"type": "string", "linkId": "880000230565", "text": "Please provide the URL to Up-to-date registry of regulatory, statutory, legislative and contractual requirements.", "enableWhen": [{"question": "129953107026", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "630913261567", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, Portal, or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "816423446395", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have Up-to-date registry of regulatory, statutory, legislative and contractual requirements as evidence, you can refer the our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/policies/common/information-technology-quality-infrastructure/compliance-regulatory-policy', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "129953107026", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "345172735619", "type": "display", "text": "As evidence, you can provide Up-to-date registry of regulatory, statutory, legislative and contractual requirements.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "129953107026", "code": [{"code": "FII-SCF-GOV-0006"}], "text": "Policies and procedures related to the relevant statutory, regulatory, legislative and contractual requirements", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "string", "linkId": "350361450291", "text": "Please provide the URL to Master list of system components (servers, Operating systems, databases, etc.)", "enableWhen": [{"question": "852930816404", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "180494544271", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, Portal, or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "896154705473", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have Master list of system components (servers, Operating systems, databases, etc.) as evidence, you can refer the our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/assets/asset-list.xlsx', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "852930816404", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "722608167626", "type": "display", "text": "As evidence, you can provide Master list of system components (servers, Operating systems, databases, etc.)", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "852930816404", "code": [{"code": "FII-SCF-HRS-0009.1"}], "text": "Does management maintain an asset inventory?", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "string", "linkId": "770288783839", "text": "Please provide the URL to Most recently completed risk assessment details.", "enableWhen": [{"question": "483977777111", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "847825518266", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, Portal, or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "351189429724", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have Most recently completed risk assessment details as evidence, you can refer the our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/assets/risk-rgister.xlsx', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "483977777111", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "723211870273", "type": "display", "text": "As evidence, you can provide Most recently completed risk assessment details.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "483977777111", "code": [{"code": "FII-SCF-RSK-0003"}], "text": "Can management provide the most recently completed risk assessment?", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "string", "linkId": "835534492455", "text": "Please provide the URL to Most recently completed fraud assessment details.", "enableWhen": [{"question": "782945372513", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "623631307653", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, Portal, or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "846103061828", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have Most recently completed fraud assessment details as evidence, you can refer the our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/assets/fraud-assessment-review.xlsx', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "782945372513", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "878288739022", "type": "display", "text": "As evidence, you can provide Most recently completed fraud assessment details.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "782945372513", "code": [{"code": "FII-SCF-RSK-0004.1"}], "text": "Can management provide the most recently completed risk assessment that includes fraud risk factors? including evidence that the assessment:- identifies and assesses the types of fraud that could impact business and operations (e.g. fraudulent reporting, loss of assets, unauthorized system access, overriding con", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"type": "string", "linkId": "154887827645", "code": [{"code": "FII-SCF-VPM-0006"}], "text": "Are vulnerability scans or penetration testing performed on a periodic basis?", "item": [{"linkId": "903261752170", "type": "display", "text": "As evidence, you may include a URL along with details from Dropbox, Box, Google Drive, Portal, JIRA, Confluence, GitHub, or any other storage platform that holds the most recent network internal and external vulnerability scan/penetration test reports.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"item": [{"type": "string", "linkId": "330220718247", "text": "Please provide the URL to Business Continuity/Disaster Recovery Plan details.", "enableWhen": [{"question": "572830451814", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "526646085881", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, Portal, or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "699960007077", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have Business Continuity/Disaster Recover Plan details as evidence, you can refer the our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/policies/common/information-technology-quality-infrastructure/business-continuity-plan', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "572830451814", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "922358583482", "type": "display", "text": "As evidence, you can provide Business Continuity/Disaster Recovery Plan details.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "572830451814", "code": [{"code": "FII-SCF-BCD-0001"}], "text": "Is a comprehensive disaster recovery and business continuity plan in place and communicated to relevant personnel?", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"type": "string", "linkId": "757635013794", "code": [{"code": "FII-SCF-BCD-0002.3"}], "text": "Is the disaster recovery program tested on a periodic basis to ensure adequate recovery?", "item": [{"linkId": "663733395217", "type": "display", "text": "As evidence, you may include a URL along with details from Dropbox, Box, Google Drive, Portal, JIRA, Confluence, GitHub, or any other storage platform that holds documentation for the most recently completed disaster recovery tests details.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}], "type": "group", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "177288750351", "text": "Common Criteria Related to Risk Assessment"}, {"item": [{"type": "string", "linkId": "883697782059", "code": [{"code": "FII-SCF-MON-0001"}], "text": "Are tools in place to monitor system performance, capacity, utilization and unusual system activity?", "item": [{"linkId": "608547454462", "type": "display", "text": "As evidence, you may include a URL along with details from Dropbox, Box, Google Drive, Portal, JIRA, Confluence, GitHub, or any other storage platform that holds documentation demonstrating a system for monitoring performance, capacity, utilization, and unusual system activity.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "string", "linkId": "251687735852", "code": [{"code": "FII-SCF-VPM-0001.1"}], "text": "Are vulnerability scans or penetration testing performed on a periodic basis?", "item": [{"linkId": "650226868798", "type": "display", "text": "As evidence, you may include a URL along with details from Dropbox, Box, Google Drive, Portal, JIRA, Confluence, GitHub, or any other storage platform that holds documentation for the most recently completed penetration test results details.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "string", "linkId": "320324981064", "code": [{"code": "FII-SCF-MON-0001.4"}], "text": "Is there file integrity monitoring software in place?", "item": [{"linkId": "885181295433", "type": "display", "text": "As evidence, you may include a URL along with details from Dropbox, Box, Google Drive, Portal, JIRA, Confluence, GitHub, or any other storage platform that holds documentation confirming file integrity monitoring software is in place, including evidence of monitoring alerts from the software or log.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}], "type": "group", "linkId": "785142250198", "text": "Common Criteria Related to Monitoring of Controls"}, {"item": [{"item": [{"type": "string", "linkId": "182220749412", "text": "Please provide the URL to Most recently completed risk assessment details.", "enableWhen": [{"question": "774380425039", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "793781088156", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, Portal, or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "695675007543", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have Most recently completed risk assessment details as evidence, you can refer the our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/assets/risk-rgister.xlsx', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "774380425039", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "983136320443", "type": "display", "text": "As evidence, you can provide Most recently completed risk assessment details.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "774380425039", "code": [{"code": "FII-SCF-RSK-0002"}], "text": "Are controls within the environment modified and implemented to mitigate identified vulnerabilities, deviations and control gaps?", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "string", "linkId": "343863405607", "text": "Please provide the URL to Sample incident response ticket details.", "enableWhen": [{"question": "891832847393", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "575560286466", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, Portal, or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "566162270051", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have <PERSON><PERSON> incident response ticket details as evidence, you can refer the our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/assets/security-incident.xlsx', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "891832847393", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "894748274098", "type": "display", "text": "As evidence, you can provide Sample incident response ticket details.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "891832847393", "code": [{"code": "FII-SCF-IRO-0002"}], "text": "Are performance of the internal controls implemented within the environment assigned to appropriate process owners and operational personnel based on roles and responsibilities?", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "string", "linkId": "337014092254", "text": "Please provide the URL to Incident Response Policies and Procedures.", "enableWhen": [{"question": "194565767265", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "965206994688", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, Portal, or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "300305068205", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have Incident Response Policies and Procedures as evidence, you can refer the our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/policies/common/information-technology-quality-infrastructure/incident-management-policy', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "194565767265", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "906359236789", "type": "display", "text": "As evidence, you can provide Incident Response Policies and Procedures.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "194565767265", "code": [{"code": "FII-SCF-IRO-0001"}], "text": "Are incidents formally documented?", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "string", "linkId": "898831616020", "text": "Please provide the URL to Sample incident response ticket details.", "enableWhen": [{"question": "815387559803", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "693296319467", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, Portal, or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "292720310693", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have <PERSON><PERSON> incident response ticket details as evidence, you can refer the our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/assets/security-incident.xlsx', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "815387559803", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "609262980156", "type": "display", "text": "As evidence, you can provide Sample incident response ticket details.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "815387559803", "code": [{"code": "FII-SCF-IRO-0004"}], "text": "Are incidents resolved in a timely manner?", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "string", "linkId": "197764326039", "text": "Please provide the URL to Incident Response Policies and Procedures details.", "enableWhen": [{"question": "144044243803", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "931673942256", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, Portal, or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "165569592534", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have Incident Response Policies and Procedures details as evidence, you can refer the our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/policies/common/information-technology-quality-infrastructure/incident-response-policy', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "144044243803", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "406108676917", "type": "display", "text": "As evidence, you can provide Incident Response Policies and Procedures details.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "144044243803", "code": [{"code": "FII-SCF-IRO-0004.1"}], "text": "Is the resolution and closure of incidents documented and communicated to affected users?", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}], "type": "group", "linkId": "880644372287", "text": "Common Criteria Related to Control Activities"}, {"item": [{"item": [{"type": "string", "linkId": "881063031004", "text": "Please provide the URL to Information security policies and procedures details.", "enableWhen": [{"question": "************", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "636799932582", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, Portal, or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "779954564096", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have Information security policies and procedures details as evidence, you can refer the our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/policies/common/information-technology-quality-infrastructure/information-security-policy', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "************", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "625947301201", "type": "display", "text": "As evidence, you can provide Information security policies and procedures details.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "************", "code": [{"code": "FII-SCF-IRO-0012"}], "text": "Are information security policies and procedures in place to communicate managements requirements with regards to user account security, appropriate handling of information systems data, privacy standards, etc.?", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "string", "linkId": "************", "text": "Please provide the URL to Listing of all systems, DBs, and applications details.", "enableWhen": [{"question": "************", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "************", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, Portal, or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "************", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have Listing of all systems, DBs, and applications\n details as evidence, you can refer the our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/assets/asset-list.xlsx', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "************", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "124328047105", "type": "display", "text": "As evidence, you can provide Listing of all systems, DBs, and applications details.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "************", "code": [{"code": "FII-SCF-BCD-0002"}], "text": "Does management have a policy defining the type of system and user events that should be logged for network devices, operating systems, databases and applications?", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"type": "string", "linkId": "391566808991", "code": [{"code": "FII-SCF-NET-0003"}], "text": "Are logs stored in a centralized log server and not accessible by system administrators? How are historic log files protected from modification and/or deletion? What is your log retention requirements?", "item": [{"linkId": "209896479599", "type": "display", "text": "As evidence, you may include a URL along with details from Dropbox, Box, Google Drive, Portal, JIRA, Confluence, GitHub, or any other storage platform that holds documentation demonstrating that logs are stored in a centralized log server with restricted access for system administrators, with measures in place to protect historic log files from modification or deletion, and outlining your log retention.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "string", "linkId": "697718405021", "code": [{"code": "FII-SCF-HRS-0001"}], "text": "Are user access requests formally documented?", "item": [{"linkId": "800062972968", "type": "display", "text": "As evidence, you may include a URL along with details from Dropbox, Box, Google Drive, Portal, JIRA, Confluence, GitHub, or any other storage platform that holds the ticket for the access request or email communication.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "310373706161", "code": [{"code": "FII-SCF-HRS-0002.1"}], "text": "Are user access requests approved? Is the approval documented?", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}], "item": [{"linkId": "235146140127", "type": "display", "text": "As evidence, you may include a URL along with details from Dropbox, Box, Google Drive, Portal, JIRA, Confluence, GitHub, or any other storage platform that holds the ticket for the access request or email communication.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"item": [{"type": "string", "linkId": "278161001290", "text": "Please provide the URL to Most recently completed user access review details.", "enableWhen": [{"question": "445712920281", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "405862167842", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, Portal, or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "610685832629", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have Most recently completed user access review details as evidence, you can refer the our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/assets/access-reviews-checklist.xlsx', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "445712920281", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "201335682306", "type": "display", "text": "As evidence, you can provide Most recently completed user access review details.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "445712920281", "code": [{"code": "FII-SCF-IAC-0017"}], "text": "Are user access reviews performed regularly (e.g., quarterly, monthly, etc.)", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "string", "linkId": "138893667153", "text": "Please provide the URL to Remote Access Policies and Procedures details.", "enableWhen": [{"question": "471353269509", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "189669410358", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, Portal, or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "394089225444", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have Remote Access Policies and Procedures\n details as evidence, you can refer the our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/policies/common/information-technology-quality-infrastructure/remote-access-policy', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "471353269509", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "572880702409", "type": "display", "text": "As evidence, you can provide Remote Access Policies and Procedures details.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "471353269509", "code": [{"code": "FII-SCF-NET-0014"}], "text": "Provide a description of any remote access (i.e. VPN, Terminal Services, RADMIN) and the type of authentication required (two factor, RADIUS, TACAS, etc.)", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"type": "string", "linkId": "202189705879", "code": [{"code": "FII-SCF-IAC-0015"}], "text": "Is the administration of remote access technologies restricted to a specific department or group?", "item": [{"linkId": "702265897942", "type": "display", "text": "As evidence, you may include a URL along with details from Dropbox, Box, Google Drive, Portal, JIRA, Confluence, GitHub, or any other storage platform that holds documentation listing the application administrators/super users for the production systems under review.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "string", "linkId": "426644830399", "code": [{"code": "FII-SCF-NET-0002"}], "text": "Do network diagrams exist that illustrate the use of firewalls?", "item": [{"linkId": "269918908369", "type": "display", "text": "As evidence, you may include a URL along with details from Dropbox, Box, Google Drive, Portal, JIRA, Confluence, GitHub, or any other storage platform that holds the network diagrams inclusive of all firewalls protecting the production servers under review.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "string", "linkId": "100988522035", "code": [{"code": "FII-SCF-NET-0012.2"}], "text": "Are appropriate firewall rulesets in place to monitor and block traffic?", "item": [{"linkId": "112496772099", "type": "display", "text": "As evidence, you can provide firewall rule sets that demonstrate that the system is in place and configured to block certain traffic.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "string", "linkId": "313465354803", "code": [{"code": "FII-SCF-CRY-0003"}], "text": "Are web servers encrypted with SSL/TLS?", "item": [{"linkId": "587245714779", "type": "display", "text": "As evidence, you can provide URL for customer facing web portals w/valid certificate details.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"item": [{"type": "string", "linkId": "359469345355", "text": "Please provide the URL to Encryption policies and procedures details.", "enableWhen": [{"question": "509130895825", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "445426075129", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, Portal, or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "566641571043", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have Encryption policies and procedures details as evidence, you can refer the our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/policies/common/information-technology-quality-infrastructure/encryption-policy', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "509130895825", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "770832662404", "type": "display", "text": "As evidence, you can provide Encryption policies and procedures details.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "509130895825", "code": [{"code": "FII-SCF-CRY-0005"}], "text": "Do formal encryption policies exist?", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"type": "string", "linkId": "545642052991", "code": [{"code": "FII-SCF-DCH-0010"}], "text": "How are removable media devices disabled?", "item": [{"linkId": "800338269203", "type": "display", "text": "As evidence, you can provide Disabling of removable media devices if applicable.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}], "type": "group", "linkId": "940809000483", "text": "Common Criteria Related to Logical and Physical Access"}, {"item": [{"item": [{"type": "string", "linkId": "140275982976", "text": "Please provide the URL to Backup policies and procedures.", "enableWhen": [{"question": "555490812889", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "384449752172", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, Portal, or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "498747938997", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have Backup policies and procedures as evidence, you can refer the our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/policies/common/information-technology-quality-infrastructure/availability-policy', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "555490812889", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "800187110502", "type": "display", "text": "As evidence, you can provide Backup policies and procedures.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "555490812889", "code": [{"code": "FII-SCF-BCD-0011"}], "text": "Are backups of critical data performed regularly (e.g., daily, weekly, monthly)?", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "string", "linkId": "692081850212", "text": "Please provide the URL to Backup policies and procedures.", "enableWhen": [{"question": "501639742652", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "829427823362", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, Portal, or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "332041350641", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have Backup policies and procedures as evidence, you can refer the our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/policies/common/information-technology-quality-infrastructure/availability-policy', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "501639742652", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "770356421038", "type": "display", "text": "As evidence, you can provide Backup policies and procedures.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "501639742652", "code": [{"code": "FII-SCF-BCD-0011.2"}], "text": "Are backups replicated offsite either virtually or via tape in case of natural disaster?", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "728798644373", "code": [{"code": "FII-SCF-BCD-0011.1"}], "text": "Are backup restoration tests performed regularly to validate data can be restored if needed?", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}], "item": [{"linkId": "444580804970", "type": "display", "text": "As evidence, you may include a URL along with details from Dropbox, Box, Google Drive, Portal, JIRA, Confluence, GitHub, or any other storage platform that holds documentation demonstrating the successful backup restore test result.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "609902307765", "code": [{"code": "FII-SCF-MON-0001.5"}], "text": "Is there an IDS or IPS in place?", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}], "item": [{"linkId": "845563657266", "type": "display", "text": "As evidence, you may include a URL along with details from Dropbox, Box, Google Drive, Portal, JIRA, Confluence, GitHub, or any other storage platform that holds documentation showing the Intrusion Detection System/Intrusion Prevention System log or dashboard details.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}], "type": "group", "linkId": "325644455080", "text": "Common Criteria Related to Systems Operations"}, {"item": [{"item": [{"type": "string", "linkId": "607843464250", "text": "Please provide the URL to Change control policies and procedures.", "enableWhen": [{"question": "927060497847", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "605937613288", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, Portal, or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "492623197367", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have Change control policies and procedures as evidence, you can refer the our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/policies/common/information-technology-quality-infrastructure/change-control-policy', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "927060497847", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "871820494771", "type": "display", "text": "As evidence, you can provide Change control policies and procedures details.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "927060497847", "code": [{"code": "FII-SCF-CHG-0002"}], "text": "Are changes formally documented?", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "string", "linkId": "868638645364", "text": "Please provide the URL to Full population of internal changes (infrastructure, network, database, application) during audit period.", "enableWhen": [{"question": "209488248070", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "611569606707", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, Portal, or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "864117421500", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have Full population of internal changes (infrastructure, network, database, application) during audit period as evidence, you can refer the our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/assets/change-management-sheet.xlsx', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "209488248070", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "603700834060", "type": "display", "text": "As evidence, you can provide Full population of internal changes (infrastructure, network, database, application) during audit period.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "209488248070", "code": [{"code": "FII-SCF-CHG-0002.1"}], "text": "Are changes formally tested and approved, and is that testing and approval documented?", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "588840768818", "code": [{"code": "FII-SCF-CHG-0001"}], "text": "Are changes developed and implemented to the production environment by different individuals/teams?", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}], "item": [{"linkId": "213240266309", "type": "display", "text": "As evidence, you may include a URL along with details from JIRA, or GitHub, or any other platform that holds a sample change management ticket verifying that the developer did not promote a change to the production environment.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "string", "linkId": "215135423373", "code": [{"code": "FII-SCF-AST-0004"}], "text": "Are there separate development/test environments from the production environment?", "item": [{"linkId": "732806058626", "type": "display", "text": "As evidence, you can provide Network diagrams.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}], "type": "group", "linkId": "731722894577", "text": "Common Criteria Related to Change Management"}, {"item": [{"item": [{"type": "string", "linkId": "594466765060", "text": "Please provide the URL to Vendor risk assessment policies and procedures with revision history.", "enableWhen": [{"question": "826888271950", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "907243918245", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, Portal, or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "964315957014", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have Vendor risk assessment policies and procedures with revision history as evidence, you can refer the our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/policies/common/information-technology-quality-infrastructure/vendor-management-policy', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "826888271950", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "951740074067", "type": "display", "text": "As evidence, you can provide Vendor risk assessment policies and procedures with revision history.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "826888271950", "code": [{"code": "FII-SCF-TPM-0001"}], "text": "Does a formal vendor management program exist?", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "string", "linkId": "386674879575", "text": "Please provide the URL to Most recently completed vendor risk assessment details.", "enableWhen": [{"question": "982746117636", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "326627126440", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, Portal, or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "863299263264", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have Most recently completed vendor risk assessment as evidence, you can refer the our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/assets/vendor-risk-assessment.xlsx', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "982746117636", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "924267733919", "type": "display", "text": "As evidence, you can provide Most recently completed vendor risk assessment details.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "982746117636", "code": [{"code": "FII-SCF-HRS-0010"}], "text": "Does management complete annual vendor risk assessments?", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}], "type": "group", "linkId": "527576574241", "text": "Common Criteria Related to Risk Mitigation"}, {"item": [{"item": [{"type": "string", "linkId": "175799447721", "text": "Please provide the URL to Confidentiality policy.", "enableWhen": [{"question": "406468720342", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "969142870719", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, Portal, or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "299957164239", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have Confidentiality policy\n as evidence, you can refer the our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/policies/common/information-technology-quality-infrastructure/confidentiality-policy', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "406468720342", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "892920914989", "type": "display", "text": "As evidence, you can provide Confidentiality policy.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "406468720342", "code": [{"code": "FII-SCF-PRI-0004"}], "text": "Are there confidentiality policies and procedures with revision history that describe: - defining, identifying and designating information as confidential; - protecting confidential information from erasure or destruction; - storing confidential data; - retaining confidential information for only as long as is required to achieve the purpose for which the confidential data was collected and processed - processes in place to delete conï¬dential information in accordance with speciï¬c retention requirements", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "string", "linkId": "498533976457", "text": "Please provide the URL to Confidential Asset register.", "enableWhen": [{"question": "291400123841", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "428072368278", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, Portal, or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "955733667060", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have Confidential Asset register\n as evidence, you can refer the our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/policies/common/information-technology-quality-infrastructure/availability-policy', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "291400123841", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "217326151084", "type": "display", "text": "As evidence, you can provide Confidential Asset register.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "291400123841", "code": [{"code": "FII-SCF-BCD-0002"}], "text": "Do you maintain an inventory listing noting assets that maintain confidential data?", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "string", "linkId": "854424922328", "text": "Please provide the URL to Population of Confidential data.", "enableWhen": [{"question": "330952154280", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "145979135287", "type": "display", "text": "As evidence, you may include a URL along with details from Dropbox, Box, Google Drive, Portal, JIRA, Confluence, GitHub, or any other storage platform that holds the inventory listing of confidential data, including its classification and retention period.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "625782737065", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have Population of Confidential data\n as evidence, you can refer the our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/policies/common/information-technology-quality-infrastructure/data-retention-policy', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "330952154280", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "805141757606", "type": "display", "text": "As evidence, you can provide Population of Confidential data.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "330952154280", "code": [{"code": "FII-SCF-PRI-0004.1"}], "text": "Will you be able to provide an inventory listing of confidential data within assets, including their classification and retention period?", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "string", "linkId": "766867466841", "text": "Please provide the URL to Confidential information maintenance policy.", "enableWhen": [{"question": "749007702018", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "952141019053", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, Portal, or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "638189727702", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have Confidential information maintenance policy as evidence, you can refer the our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/policies/common/information-technology-quality-infrastructure/confidentiality-policy', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "749007702018", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "490103653051", "type": "display", "text": "As evidence, you can provide Confidential information maintenance policy.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "749007702018", "code": [{"code": "FII-SCF-PRI-0005"}], "text": "Have you documented confidential information maintenance policies and procedures?", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"type": "string", "linkId": "137452157169", "code": [{"code": "FII-SCF-DCH-0022"}], "text": "During the audit we will select a sample of files/documents marked as confidential and evidence of the file / directory access permissions and the list of users with access to the confidential file/document were property limited.", "item": [{"linkId": "454907289554", "type": "display", "text": "As evidence, you may include a URL along with details from Dropbox, Box, Google Drive, Portal, JIRA, Confluence, GitHub, or any other storage platform that holds a sample of confidential files, access permissions, and the corresponding user list.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "string", "linkId": "918645500105", "code": [{"code": "FII-SCF-DCH-0021"}], "text": "During the audit we will ask for a population of confidential data that required disposal. For a sample of files/documents marked as confidential that required disposal, can you provide evidence that the file/document was disposed following the procedures documented in the Confidentiality and/or Data Disposal policies?", "item": [{"linkId": "169057933905", "type": "display", "text": "As evidence, you may include a URL along with details from Dropbox, Box, Google Drive, Portal, JIRA, Confluence, GitHub, or any other storage platform that holds sample records demonstrating proper disposal of confidential files/documents per your policies.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "string", "linkId": "315750093252", "code": [{"code": "FII-SCF-CRY-0005"}], "text": "Can you provide evidence that confidential information is stored in an encrypted, secure environment?", "item": [{"linkId": "219404963015", "type": "display", "text": "As evidence, you may include a URL along with details from Dropbox, Box, Google Drive, Portal, JIRA, Confluence, GitHub, or any other storage platform that holds documentation confirming that confidential information is stored in an encrypted, secure environment.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}], "type": "group", "linkId": "634968453971", "text": "Confidentiality"}, {"type": "group", "linkId": "121005996834", "text": "Availability", "item": [{"type": "string", "linkId": "457859126554", "code": [{"code": "FII-SCF-BCD-0012"}], "text": "Does the organization meet its availability requirements by conducting annual backup restoration testing?", "item": [{"linkId": "122325712848", "type": "display", "text": "As evidence, you may include a URL along with details from Dropbox, Box, Google Drive, Portal, JIRA, Confluence, GitHub, or any other storage platform that holds the annual backup restoration testing report.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "string", "linkId": "507712339527", "code": [{"code": "FII-SCF-TPM-0001"}], "text": "Third party data centers most recent SOC report", "item": [{"linkId": "188422931956", "type": "display", "text": "Please attach a URL along with details from any secure storage platform (e.g., Dropbox, Box, Google Drive, Portal, JIRA, Confluence, GitHub, etc.) where your Vendor SOC Reports are stored. This may include documents such as your AWS SOC2 certification report, ISO certification report, or similar evidence verifying the cloud server's compliance.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "string", "linkId": "679518609812", "code": [{"code": "FII-SCF-MON-0001.4"}], "text": "Does the organization meet its availability requirements by effectively utilizing hardware usage monitoring and usage alerts?", "item": [{"linkId": "701861460401", "type": "display", "text": "As evidence, you may include a URL along with details from Dropbox, Box, Google Drive, Portal, JIRA, Confluence, GitHub, or any other storage platform that holds documentation or screenshots demonstrating hardware usage monitoring and usage alerts.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}]}, {"type": "group", "linkId": "381630354537", "text": "Meta Information", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-style", "valueString": "display:none"}, {"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<div style=\"display:none\">\n<input  id=\"lhcFormMetaInformation\" value='{\n    \"createdAt\": \"2-10-2025\",\n    \"createdBy\": \"Arun KR\",\n    \"description\": \"The SOC 2 Certification Evidence Collection process involves gathering and documenting the necessary artifacts to demonstrate compliance with SOC 2 security, availability, processing integrity, confidentiality, and privacy principles. This includes policies, access logs, security configurations, risk assessments, incident response plans, and employee training records. Proper evidence collection ensures that an organization can effectively prove its adherence to SOC 2 requirements during audits, reducing compliance risks and strengthening customer trust.\",\n    \"title\": \"SOC2 Certification Evidence Collection\",\n    \"organization\": \"Netspective\"\n}'>\n</div>"}]}}]}