---
title: CaaS Plans for Your Business
summary: "Customer: Review CaaS Plans for Your Business"
home:
  ItGovernance:
    category: "it-governance"
  whatsNext:
    category: "whatsNext"
    order: 2
  poam:
    category: "poam"
    order: 1
description:  SOC 2 (Service Organization Control 2) is a widely recognized framework designed to evaluate and enhance the security, availability, processing integrity, confidentiality, and privacy of service organizations. Choose the right compliance plan for your business.
enableEditButton: true
---

<div class="grow">
<section class="py-8 bg-white dark:bg-gray-800 lg:py-24 mt-6" id="soc2-plans">
  <div class="px-4 mx-auto max-w-8xl lg:px-4">
    <h2 class="mb-4 text-4xl font-bold text-gray-900 lg:font-extrabold lg:text-4xl lg:leading-snug dark:text-white lg:text-center 2xl:px-48">
      Choose the right Compliance Plan for your Business
    </h2>
    <p class="mb-10 text-lg font-normal text-gray-500 dark:text-gray-300 lg:text-center lg:text-xl lg:mb-16">
      Find the perfect compliance solution tailored to your business needs, whether you're a startup, growing company, or an established enterprise.<br />Our flexible plans ensure you meet all necessary standards while fitting seamlessly into your existing structure.
    </p>
    <article class="lg:bg-gray-50 lg:dark:bg-gray-800 lg:p-8 rounded-t-lg dark:bg-gray-800">
      <div class="grid grid-cols-12 gap-6">
        <div class="col-span-12 xl:col-span-3 lg:col-span-6">
          <div class="block w-full p-5 border-gray-200 rounded-lg bg-gray-200 dark:border-gray-600 dark:bg-gray-700"><h3 class="mb-1 text-lg font-bold text-gray-900 dark:text-white">Not Sure Which Plan is Right for You?</h3>
                    <p class="mb-4 text-sm text-gray-500 dark:text-gray-300">If you're unsure which plan best fits your business needs, our sales team is here to help guide you in selecting the perfect compliance solution. Contact us today!</p>
            <a href="https://opsfolio.com/contact" target="_new">
            <button class="block w-full px-6 py-2 font-medium text-l text-center text-gray-900 bg-white  dark:bg-gray-800 dark:bg-gray-800  border border-gray-200 rounded-lg dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:hover:text-gray-100 hover:bg-gray-100 hover:text-gray-700 dark: dark:hover:bg-gray-700 focus:outline-none focus:z-10 focus:ring-2 focus:ring-gray-700 focus:text-gray-700" title="Contact Sales">Contact Sales</button></a></div>
        </div>
        <div class="col-span-12 xl:col-span-3 lg:col-span-6">
        <aside class="min-h-plan-1">
          <span
            class="block text-xl text-gray-900 dark:text-white"
            >Plan 1</span>
          <span
            class="block mb-4 text-3xl font-extrabold text-gray-900 dark:text-white"
            >Essentials</span
          >
          <h3 class="mb-1 text-lg font-bold text-gray-900 dark:text-white">
            Ideal for startups and small businesses.
          </h3>
          <p class="mb-4 text-sm text-gray-500 dark:text-gray-300">
            A budget-friendly option that provides our complete set of policies and procedures. Includes a fixed-price SOC 2 Type 1 audit letter and ongoing SOC 2 Type 2 coaching, mentoring, and monitoring.
          </p>
        </aside>
          <a href="/expectations/questionnaire/plan-1/general-information-evidence-collection.lhc-form.json/">
          <button
            class="items-center justify-center w-full px-6 py-2 mb-3 text-base font-medium text-center text-white bg-blue-700 dark:bg-blue-600 hover:bg-blue-800 rounded-lg focus:outline-none focus:ring-4 focus:ring-blue-300 dark:hover:bg-blue-700 md:mr-5 md:mb-0" title="Select Essentials"
          >
            Select Essentials</button></a>
        </div>
        <div class="col-span-12 xl:col-span-3 lg:col-span-6">
        <aside class="min-h-plan-1">
        <span
            class="block text-xl text-gray-900 dark:text-white"
            >Plan 2</span
          >
          <span class="block mb-4 text-3xl font-extrabold text-gray-900 dark:text-white"
            >Flex</span
          >
          <h3 class="mb-1 text-lg font-bold text-gray-900 dark:text-white">
            Balanced compliance for growing businesses.
          </h3>
          <p class="my-4 text-sm text-gray-500 dark:text-gray-300">
            Use our technical policies while retaining your own IT and HR policies for a flexible compliance approach.
          </p>
        </aside>        
          <a href="/expectations/questionnaire/plan-2/general-information-evidence-collection.lhc-form.json/">
          <button
            class="items-center justify-center w-full px-6 py-2 mb-3 text-base font-medium text-center text-white bg-purple-700 dark:bg-purple-600 hover:bg-purple-800 rounded-lg focus:outline-none focus:ring-4 focus:ring-purple-300 dark:hover:bg-purple-700 md:mr-5 md:mb-0" title="Select Flex"
          >
            Select Flex</button></a>
        </div>
        <div class="col-span-12 xl:col-span-3 lg:col-span-6">
        <aside class="min-h-plan-1">
        <span
            class="block text-xl text-gray-900 dark:text-white"
            >Plan 3</span
          >
          <span
            class="block mb-4 text-3xl font-extrabold text-gray-900 dark:text-white"
            >Enterprise</span>
          <h3 class="mb-1 text-lg font-bold text-gray-900 dark:text-white">
            Fully customizable for enterprises and organizations with existing frameworks.
          </h3>
          <p class="mb-4 text-sm text-gray-500 dark:text-gray-300">
            Bring your own policies, and we’ll build the required evidence around your existing compliance structure.
          </p>
        </aside>        
          <a href="/expectations/questionnaire/plan-3/general-information-evidence-collection.lhc-form.json/">
          <button
            class="items-center justify-center w-full px-6 py-2 mb-3 text-base font-medium text-center text-white bg-pink-700 dark:bg-pink-600 hover:bg-pink-800 rounded-lg focus:outline-none focus:ring-4 focus:ring-pink-300 dark:hover:bg-pink-700 md:mr-5 md:mb-0" title="Select Enterprise"
          >
            Select Enterprise</button></a>
        </div>
      </div>
    </article>
  <div>
  <div class="py-8 text-3xl font-bold text-gray-700 lg:text-center lg:text-xl dark:text-gray-300">Detailed Plan Comparison</div>
    <div class="overflow-auto">
      <table class="min-w-full table-auto border-collapse text-sm text-left text-gray-700 table-border-plan">
        <thead class="text-gray-900 bg-gray-100 dark:bg-gray-800  dark:text-white">
          <tr class="bg-gray-100">
            <th class="px-4 py-3 font-normal text-xl">Feature</th>
            <th class="px-4 py-3 font-bold text-xl">Plan 1: Essentials</th>
            <th class="px-4 py-3 font-bold text-xl">Plan 2: Flex</th> 
            <th class="px-4 py-3 font-bold text-xl">Plan 3: Enterprise</th>
          </tr>
        </thead>
        <tbody class="divide-y divide-gray-200">
          <tr class="text-sm text-gray-700">
            <td class="px-4 py-3 text-gray-500 dark:text-gray-300 font-bold">Best for</td>
            <td class="px-4 py-3 text-gray-500 dark:text-gray-300">Startups & Small Businesses</td>
            <td class="px-4 py-3 text-gray-500 dark:text-gray-300">Growing Businesses</td>
            <td class="px-4 py-3 text-gray-500 dark:text-gray-300">Enterprises & Custom Needs</td>
          </tr>
          <tr class="bg-gray-100">
            <td class="px-4 py-3 text-gray-500 dark:text-gray-300 bg-gray-100">Policies & Procedures</td>
            <td class="px-4 py-3 text-green-500 font-medium bg-gray-100">Full set provided</td>
            <td class="px-4 py-3 text-blue-500 font-medium bg-gray-100">Use our technical policies + <br />your IT/HR policies</td>
            <td class="px-4 py-3 text-red-500 font-medium bg-gray-100">Fully customizable (bring your own)</td>
          </tr>
          <tr>
            <td class="px-4 py-3 text-gray-500 dark:text-gray-300">SOC 2 Type 1 Audit Letter</td>
            <td class="px-4 py-3 text-gray-500 dark:text-gray-300">Fixed-price included</td>
            <td class="px-4 py-3 text-gray-500 dark:text-gray-300">Available as an add-on</td>
            <td class="px-4 py-3 text-gray-500 dark:text-gray-300">Available as an add-on</td>
          </tr>
          <tr>
            <td class="px-4 py-3 text-gray-500 dark:text-gray-300">SOC 2 Type 2 Support</td>
            <td class="px-4 py-3 text-gray-500 dark:text-gray-300">Coaching, mentoring & <br />monitoring</td>
            <td class="px-4 py-3 text-gray-500 dark:text-gray-300">Limited support</td>
            <td class="px-4 py-3 text-gray-500 dark:text-gray-300">Custom support based on your policies</td>
          </tr>
          <tr>
            <td class="px-4 py-3 text-gray-500 dark:text-gray-300">Customization</td>
            <td class="px-4 py-3 text-gray-500 dark:text-gray-300">None (predefined policies)</td>
            <td class="px-4 py-3 text-gray-500 dark:text-gray-300">Partial (your IT/HR policies)</td>
            <td class="px-4 py-3 text-gray-500 dark:text-gray-300">Full (use your own framework)</td>
          </tr>
          <tr>
            <td class="bg-gray-50 dark:bg-gray-800"><div class="p-6"></div></td>
            <td class="bg-gray-50 dark:bg-gray-800 text-center">
            <div class="py-6">
            <button class="btn-plan items-center justify-center px-6 py-2 mb-3 text-base font-medium text-center text-white bg-blue-700 dark:bg-blue-600 hover:bg-blue-800 rounded-lg focus:outline-none focus:ring-4 focus:ring-blue-300 dark:hover:bg-blue-700 md:mr-5 md:mb-0">Select Essentials</button>
            </div>
            </td>
            <td class="bg-gray-50 dark:bg-gray-800">
            <div class="py-6">
            <button class="btn-plan items-center justify-center px-6 py-2 mb-3 text-base font-medium text-center text-white bg-purple-700 dark:bg-purple-600 hover:bg-purple-800 rounded-lg focus:outline-none focus:ring-4 focus:ring-purple-300 dark:hover:bg-purple-700 md:mr-5 md:mb-0">Select Flex</button>
            </div>
            </td>
            <td class="bg-gray-50 dark:bg-gray-800">
            <div class="py-6">
            <button class="btn-plan items-center justify-center px-6 py-2 mb-3 text-base font-medium text-center text-white bg-pink-700 dark:bg-pink-600 hover:bg-pink-800 rounded-lg focus:outline-none focus:ring-4 focus:ring-pink-300 dark:hover:bg-pink-700 md:mr-5 md:mb-0">Select Enterprise</button>
            </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</section>
</div>    