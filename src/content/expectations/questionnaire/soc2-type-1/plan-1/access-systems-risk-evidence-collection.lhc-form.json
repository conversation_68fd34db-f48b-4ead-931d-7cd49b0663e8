{"resourceType": "Questionnaire", "title": "Logical and Physical Access, Systems Operations, Change Management, Risk Mitigation, Confidentiality and Availability Evidence Collection Form", "status": "draft", "item": [{"item": [{"item": [{"type": "string", "linkId": "219493393467", "text": "Please provide the URL to Listing of all systems, DBs, and applications details.", "enableWhen": [{"question": "121141662079", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "637327119355", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, Portal, or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "462623915752", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have Listing of all systems, DBs, and applications\n details as evidence, you can refer the our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/assets/asset-list.xlsx', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "121141662079", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "124328047105", "type": "display", "text": "As evidence, you can provide Listing of all systems, DBs, and applications details.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "121141662079", "code": [{"code": "FII-SCF-BCD-0002"}], "text": "Does management have a policy defining the type of system and user events that should be logged for network devices, operating systems, databases and applications?", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"type": "string", "linkId": "391566808991", "code": [{"code": "FII-SCF-NET-0003"}], "text": "Are logs stored in a centralized log server and not accessible by system administrators? How are historic log files protected from modification and/or deletion? What is your log retention requirements?", "item": [{"linkId": "209896479599", "type": "display", "text": "As evidence, you may include a URL along with details from Dropbox, Box, Google Drive, Portal, JIRA, Confluence, GitHub, or any other storage platform that holds documentation demonstrating that logs are stored in a centralized log server with restricted access for system administrators, with measures in place to protect historic log files from modification or deletion, and outlining your log retention.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "string", "linkId": "697718405021", "code": [{"code": "FII-SCF-HRS-0001"}], "text": "Are user access requests formally documented?", "item": [{"linkId": "800062972968", "type": "display", "text": "As evidence, you may include a URL along with details from Dropbox, Box, Google Drive, Portal, JIRA, Confluence, GitHub, or any other storage platform that holds the ticket for the access request or email communication.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "310373706161", "code": [{"code": "FII-SCF-HRS-0002.1"}], "text": "Are user access requests approved? Is the approval documented?", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}], "item": [{"linkId": "235146140127", "type": "display", "text": "As evidence, you may include a URL along with details from Dropbox, Box, Google Drive, Portal, JIRA, Confluence, GitHub, or any other storage platform that holds the ticket for the access request or email communication.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"item": [{"type": "string", "linkId": "278161001290", "text": "Please provide the URL to Most recently completed user access review details.", "enableWhen": [{"question": "445712920281", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "405862167842", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, Portal, or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "610685832629", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have Most recently completed user access review details as evidence, you can refer the our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/assets/access-reviews-checklist.xlsx', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "445712920281", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "201335682306", "type": "display", "text": "As evidence, you can provide Most recently completed user access review details.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "445712920281", "code": [{"code": "FII-SCF-IAC-0017"}], "text": "Are user access reviews performed regularly (e.g., quarterly, monthly, etc.)", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"type": "string", "linkId": "202189705879", "code": [{"code": "FII-SCF-IAC-0015"}], "text": "Is the administration of remote access technologies restricted to a specific department or group?", "item": [{"linkId": "702265897942", "type": "display", "text": "As evidence, you may include a URL along with details from Dropbox, Box, Google Drive, Portal, JIRA, Confluence, GitHub, or any other storage platform that holds documentation listing the application administrators/super users for the production systems under review.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "string", "linkId": "426644830399", "code": [{"code": "FII-SCF-NET-0002"}], "text": "Do network diagrams exist that illustrate the use of firewalls?", "item": [{"linkId": "269918908369", "type": "display", "text": "As evidence, you may include a URL along with details from Dropbox, Box, Google Drive, Portal, JIRA, Confluence, GitHub, or any other storage platform that holds the network diagrams inclusive of all firewalls protecting the production servers under review.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "string", "linkId": "100988522035", "code": [{"code": "FII-SCF-NET-0012.2"}], "text": "Are appropriate firewall rulesets in place to monitor and block traffic?", "item": [{"linkId": "112496772099", "type": "display", "text": "As evidence, you can provide firewall rule sets that demonstrate that the system is in place and configured to block certain traffic.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "string", "linkId": "313465354803", "code": [{"code": "FII-SCF-CRY-0003"}], "text": "Are web servers encrypted with SSL/TLS?", "item": [{"linkId": "587245714779", "type": "display", "text": "As evidence, you can provide URL for customer facing web portals w/valid certificate details.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "string", "linkId": "545642052991", "code": [{"code": "FII-SCF-DCH-0010"}], "text": "How are removable media devices disabled?", "item": [{"linkId": "800338269203", "type": "display", "text": "As evidence, you can provide Disabling of removable media devices if applicable.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}], "type": "group", "linkId": "940809000483", "text": "Common Criteria Related to Logical and Physical Access"}, {"item": [{"type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "728798644373", "code": [{"code": "FII-SCF-BCD-0011.1"}], "text": "Are backup restoration tests performed regularly to validate data can be restored if needed?", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}], "item": [{"linkId": "444580804970", "type": "display", "text": "As evidence, you may include a URL along with details from Dropbox, Box, Google Drive, Portal, JIRA, Confluence, GitHub, or any other storage platform that holds documentation demonstrating the successful backup restore test result.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "609902307765", "code": [{"code": "FII-SCF-MON-0001.5"}], "text": "Is there an IDS or IPS in place?", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}], "item": [{"linkId": "845563657266", "type": "display", "text": "As evidence, you may include a URL along with details from Dropbox, Box, Google Drive, Portal, JIRA, Confluence, GitHub, or any other storage platform that holds documentation showing the Intrusion Detection System/Intrusion Prevention System log or dashboard details.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}], "type": "group", "linkId": "325644455080", "text": "Common Criteria Related to Systems Operations"}, {"item": [{"item": [{"type": "string", "linkId": "868638645364", "text": "Please provide the URL to Full population of internal changes (infrastructure, network, database, application) during audit period.", "enableWhen": [{"question": "209488248070", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "611569606707", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, Portal, or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "864117421500", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have Full population of internal changes (infrastructure, network, database, application) during audit period as evidence, you can refer the our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/assets/change-management-sheet.xlsx', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "209488248070", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "603700834060", "type": "display", "text": "As evidence, you can provide Full population of internal changes (infrastructure, network, database, application) during audit period.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "209488248070", "code": [{"code": "FII-SCF-CHG-0002.1"}], "text": "Are changes formally tested and approved, and is that testing and approval documented?", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "588840768818", "code": [{"code": "FII-SCF-CHG-0001"}], "text": "Are changes developed and implemented to the production environment by different individuals/teams?", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}], "item": [{"linkId": "213240266309", "type": "display", "text": "As evidence, you may include a URL along with details from JIRA, or GitHub, or any other platform that holds a sample change management ticket verifying that the developer did not promote a change to the production environment.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "string", "linkId": "215135423373", "code": [{"code": "FII-SCF-AST-0004"}], "text": "Are there separate development/test environments from the production environment?", "item": [{"linkId": "732806058626", "type": "display", "text": "As evidence, you can provide Network diagrams.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}], "type": "group", "linkId": "731722894577", "text": "Common Criteria Related to Change Management"}, {"item": [{"item": [{"type": "string", "linkId": "386674879575", "text": "Please provide the URL to Most recently completed vendor risk assessment details.", "enableWhen": [{"question": "982746117636", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "326627126440", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, Portal, or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "863299263264", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have Most recently completed vendor risk assessment as evidence, you can refer the our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/assets/vendor-risk-assessment.xlsx', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "982746117636", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "924267733919", "type": "display", "text": "As evidence, you can provide Most recently completed vendor risk assessment details.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "982746117636", "code": [{"code": "FII-SCF-HRS-0010"}], "text": "Does management complete annual vendor risk assessments?", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}], "type": "group", "linkId": "527576574241", "text": "Common Criteria Related to Risk Mitigation"}, {"item": [{"item": [{"type": "string", "linkId": "498533976457", "text": "Please provide the URL to Confidential Asset register.", "enableWhen": [{"question": "291400123841", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "428072368278", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, Portal, or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "955733667060", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have Confidential Asset register\n as evidence, you can refer the our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/policies/common/information-technology-quality-infrastructure/availability-policy', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "291400123841", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "217326151084", "type": "display", "text": "As evidence, you can provide Confidential Asset register.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "291400123841", "code": [{"code": "FII-SCF-BCD-0002"}], "text": "Do you maintain an inventory listing noting assets that maintain confidential data?", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "string", "linkId": "854424922328", "text": "Please provide the URL to Population of Confidential data.", "enableWhen": [{"question": "330952154280", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "145979135287", "type": "display", "text": "As evidence, you may include a URL along with details from Dropbox, Box, Google Drive, Portal, JIRA, Confluence, GitHub, or any other storage platform that holds the inventory listing of confidential data, including its classification and retention period.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "625782737065", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have Population of Confidential data\n as evidence, you can refer the our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/policies/common/information-technology-quality-infrastructure/data-retention-policy', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "330952154280", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "805141757606", "type": "display", "text": "As evidence, you can provide Population of Confidential data.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "330952154280", "code": [{"code": "FII-SCF-PRI-0004.1"}], "text": "Will you be able to provide an inventory listing of confidential data within assets, including their classification and retention period?", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"type": "string", "linkId": "137452157169", "code": [{"code": "FII-SCF-DCH-0022"}], "text": "During the audit we will select a sample of files/documents marked as confidential and evidence of the file / directory access permissions and the list of users with access to the confidential file/document were property limited.", "item": [{"linkId": "454907289554", "type": "display", "text": "As evidence, you may include a URL along with details from Dropbox, Box, Google Drive, Portal, JIRA, Confluence, GitHub, or any other storage platform that holds a sample of confidential files, access permissions, and the corresponding user list.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "string", "linkId": "918645500105", "code": [{"code": "FII-SCF-DCH-0021"}], "text": "During the audit we will ask for a population of confidential data that required disposal. For a sample of files/documents marked as confidential that required disposal, can you provide evidence that the file/document was disposed following the procedures documented in the Confidentiality and/or Data Disposal policies?", "item": [{"linkId": "169057933905", "type": "display", "text": "As evidence, you may include a URL along with details from Dropbox, Box, Google Drive, Portal, JIRA, Confluence, GitHub, or any other storage platform that holds sample records demonstrating proper disposal of confidential files/documents per your policies.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "string", "linkId": "315750093252", "code": [{"code": "FII-SCF-CRY-0005"}], "text": "Can you provide evidence that confidential information is stored in an encrypted, secure environment?", "item": [{"linkId": "219404963015", "type": "display", "text": "As evidence, you may include a URL along with details from Dropbox, Box, Google Drive, Portal, JIRA, Confluence, GitHub, or any other storage platform that holds documentation confirming that confidential information is stored in an encrypted, secure environment.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}], "type": "group", "linkId": "634968453971", "text": "Confidentiality"}, {"item": [{"type": "string", "linkId": "457859126554", "code": [{"code": "FII-SCF-BCD-0012"}], "text": "Does the organization meet its availability requirements by conducting annual backup restoration testing?", "item": [{"linkId": "122325712848", "type": "display", "text": "As evidence, you may include a URL along with details from Dropbox, Box, Google Drive, Portal, JIRA, Confluence, GitHub, or any other storage platform that holds the annual backup restoration testing report.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "string", "linkId": "507712339527", "code": [{"code": "FII-SCF-TPM-0001"}], "text": "Third party data centers most recent SOC report", "item": [{"linkId": "188422931956", "type": "display", "text": "Please attach a URL along with details from any secure storage platform (e.g., Dropbox, Box, Google Drive, Portal, JIRA, Confluence, GitHub, etc.) where your Vendor SOC Reports are stored. This may include documents such as your AWS SOC2 certification report, ISO certification report, or similar evidence verifying the cloud server's compliance.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "string", "linkId": "679518609812", "code": [{"code": "FII-SCF-MON-0001.4"}], "text": "Does the organization meet its availability requirements by effectively utilizing hardware usage monitoring and usage alerts?", "item": [{"linkId": "701861460401", "type": "display", "text": "As evidence, you may include a URL along with details from Dropbox, Box, Google Drive, Portal, JIRA, Confluence, GitHub, or any other storage platform that holds documentation or screenshots demonstrating hardware usage monitoring and usage alerts.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}], "type": "group", "linkId": "121005996834", "text": "Availability"}, {"type": "group", "linkId": "************", "text": "Meta Information", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<div style=\"display:none\">\n<input  id=\"lhcFormMetaInformation\" value='{\n    \"createdAt\": \"2-10-2025\",\n    \"createdBy\": \"Arun KR\",\n    \"description\": \"As part of Plan 1 for the SOC 2 Type 1 audit, this questionnaire helps prepare for a fixed-price SOC 2 Type 1 audit and offers guidance for ongoing SOC 2 Type 2 coaching, mentoring, and monitoring to maintain continuous compliance and mitigate risks. Designed for startups and small businesses, it assists in evaluating existing policies, procedures, and security controls to ensure alignment with SOC 2 requirements. The following questionnaire is intended to collect Logical and Physical Access, Systems Operations, Change Management, Risk Mitigation, Confidentiality and Availability informations and assess readiness for SOC 2 compliance.\",\n    \"title\": \"Logical and Physical Access, Systems Operations, Change Management, Risk Mitigation, Confidentiality and Availability\",\n    \"organization\": \"Netspective Communication LLC\"\n}'>\n</div>"}]}}]}