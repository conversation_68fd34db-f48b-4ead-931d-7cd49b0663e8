{"resourceType": "Questionnaire", "title": "Common Criteria Related to Change Management", "status": "draft", "item": [{"item": [{"item": [{"type": "string", "linkId": "607843464250", "text": "Please provide the URL to Change control policies and procedures.", "enableWhen": [{"question": "927060497847", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "605937613288", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, Portal, or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "492623197367", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have Change control policies and procedures as evidence, you can refer the our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/policies/common/information-technology-quality-infrastructure/change-control-policy', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "927060497847", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "871820494771", "type": "display", "text": "As evidence, you can provide Change control policies and procedures details.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "927060497847", "code": [{"code": "FII-SCF-CHG-0002"}], "text": "Are changes formally documented?", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "string", "linkId": "868638645364", "text": "Please provide the URL to Full population of internal changes (infrastructure, network, database, application) during audit period.", "enableWhen": [{"question": "209488248070", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "611569606707", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, Portal, or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "864117421500", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have Full population of internal changes (infrastructure, network, database, application) during audit period as evidence, you can refer the our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/assets/change-management-sheet.xlsx', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "209488248070", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "603700834060", "type": "display", "text": "As evidence, you can provide Full population of internal changes (infrastructure, network, database, application) during audit period.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "209488248070", "code": [{"code": "FII-SCF-CHG-0002.1"}], "text": "Are changes formally tested and approved, and is that testing and approval documented?", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "588840768818", "code": [{"code": "FII-SCF-CHG-0001"}], "text": "Are changes developed and implemented to the production environment by different individuals/teams?", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}], "item": [{"linkId": "213240266309", "type": "display", "text": "As evidence, you may include a URL along with details from JIRA, or GitHub, or any other platform that holds a sample change management ticket verifying that the developer did not promote a change to the production environment.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "string", "linkId": "215135423373", "code": [{"code": "FII-SCF-AST-0004"}], "text": "Are there separate development/test environments from the production environment?", "item": [{"linkId": "732806058626", "type": "display", "text": "As evidence, you can provide Network diagrams.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}], "type": "group", "linkId": "731722894577", "text": "Common Criteria Related to Change Management"}, {"type": "group", "linkId": "685791569580", "text": "Meta Information", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<div style=\"display:none\">\n<input  id=\"lhcFormMetaInformation\" value='{\n    \"createdAt\": \"2-10-2025\",\n    \"createdBy\": \"Arun KR\",\n    \"description\": \"As part of Plan 2, this option provides a balanced compliance approach by allowing organizations to retain their own IT and HR policies while leveraging our technical policies to meet SOC 2 requirements. Designed for organizations seeking a structured yet flexible compliance framework, it ensures alignment with SOC 2 standards while maintaining existing operational policies. This approach helps streamline audit preparation by integrating proven technical controls with an organization’s established policies. The following questionnaire is intended to collect Change Management informations and assess readiness for SOC 2 compliance.\",\n    \"title\": \"Change Management\",\n    \"organization\": \"Netspective Communication LLC\"\n}'>\n</div>"}]}}]}