{"resourceType": "Questionnaire", "title": "Information and Communications  Evidence Collection Form", "status": "draft", "item": [{"item": [{"item": [{"type": "string", "linkId": "562687850605", "text": "Please provide the URL to share the Description of system/services..", "enableWhen": [{"question": "683163909840", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "529001882135", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, Portal, or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "521500592845", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have an description of system/services documentation, you can refer our sample version  <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/assets/server-list.xlsx', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "683163909840", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "592330886531", "type": "display", "text": "As evidence, you can provide the Description of system/services..", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "683163909840", "code": [{"code": "FII-SCF-AST-0004"}], "text": "Has management documented the description of their system for the services provided to their customers? A system description would include the following components used to provide services to their customers: Infrastructure (facilities, hardware, equipment), Software, People, Procedures, and Data.", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "string", "linkId": "542612221042", "text": "Please provide the URL to share the Organizational structure.", "enableWhen": [{"question": "490498907662", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "210258347995", "type": "display", "text": "As evidence, you may include a URL along with details from Dropbox, Box, Google Drive, Portal, JIRA, Confluence, GitHub, or any other storage platform that holds the documentation confirming management published the company's organizational structure.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "769306935484", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have an Organizational structure in place, you can refer our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/assets/org_structure.drawio.svg', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "490498907662", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "401090108606", "type": "display", "text": "As evidence, you may include a URL along with details from Dropbox, Box, Google Drive, Portal, JIRA, Confluence, GitHub, or any other storage platform that holds the documentation confirming management published the company's organizational structure.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "490498907662", "code": [{"code": "FII-SCF-NET-0006.1"}], "text": "Has management published the company's organizational structure?", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "string", "linkId": "946994226499", "text": "Please provide the URL to share the Privacy Policy.", "enableWhen": [{"question": "471582030921", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "776998638474", "type": "display", "text": "As evidence, you may include a URL along with details from Dropbox, Box, Google Drive, Portal, JIRA, Confluence, GitHub, or any other storage platform that holds the Privacy Notice posted on your website and software or the Privacy Policy.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "129196039837", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have an Privacy notice posted on website and software, you can refer the our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/policies/common/information-technology-quality-infrastructure/privacy-management-policy', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "471582030921", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "884907262455", "type": "display", "text": "As evidence, you may include a URL along with details from Dropbox, Box, Google Drive, Portal, JIRA, Confluence, GitHub, or any other storage platform that holds the Privacy Notice posted on your website and software or the Privacy Policy", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "471582030921", "code": [{"code": "FII-SCF-PRI-0001"}], "text": "How are the entity's security/confidentiality responsibilities communicated to customers?", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"type": "string", "linkId": "138462999411", "code": [{"code": "FII-SCF-IRO-0010"}], "text": "How do customers report system failures and security breaches?", "item": [{"linkId": "216619441762", "type": "display", "text": "As evidence, you may include a URL along with details from Dropbox, Box, Google Drive, Portal, JIRA, Confluence, GitHub, or any other storage platform that holds documentation demonstrating that customers receive procedures for reporting system failures and security breaches (if not included in the contract) or a feedback and support form/email evidence.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"item": [{"type": "string", "linkId": "736507258806", "text": "Please provide the URL to share the Incident Response Policies and Procedures.", "enableWhen": [{"question": "296807821886", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "173286528419", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, Portal, or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "342714132382", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have an Incident Response Policies and Procedures, you can refer the our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/policies/common/information-technology-quality-infrastructure/incident-response-policy', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "296807821886", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "725469154963", "type": "display", "text": "As evidence, you can provide that Incident Response Policies and Procedures.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "296807821886", "code": [{"code": "FII-SCF-IRO-0001"}], "text": "Has management documented a formal Incident Response Policy?", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"type": "string", "linkId": "305589023699", "code": [{"code": "FII-SCF-CHG-0002"}], "text": "How are system changes communicated to employees?", "item": [{"linkId": "209695595928", "type": "display", "text": "As evidence, you may include a URL along with details from Dropbox, Box, Google Drive, Portal, JIRA, Confluence, GitHub, or any other storage platform that holds documentation (e.g., JIRA/Open Project/GitHub tickets) demonstrating that system changes are communicated to employees.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "string", "linkId": "960471447612", "code": [{"code": "FII-SCF-CHG-0005"}], "text": "How are system changes communicated to customers?", "item": [{"linkId": "340832084694", "type": "display", "text": "As evidence, you may include a URL along with details from Dropbox, Box, Google Drive, Portal, JIRA, Confluence, GitHub, or any other storage platform that holds documentation (e.g., release notes, Jira/Open Project/GitHub tickets) or email demonstrating that system changes are communicated to customers.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "140276124407", "code": [{"code": "FII-SCF-AST-0004"}], "text": "Data flow diagrams, process flow charts, and narratives identifying how data flows within the environment including the relevant information sources and systems.", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}], "item": [{"linkId": "844806904891", "type": "display", "text": "As evidence, you may include a URL along with details from Dropbox, Box, Google Drive, Portal, JIRA, Confluence, GitHub, or any other storage platform that holds the Data Flow Diagram.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "string", "linkId": "876142325211", "code": [{"code": "FII-SCF-MON-0001.7"}], "text": "File integrity monitoring software configurations and example alert generated from the file integrity monitoring software.", "item": [{"linkId": "545264238729", "type": "display", "text": "As evidence, you may include a URL along with details from Dropbox, Box, Google Drive, Portal, JIRA, Confluence, GitHub, or any other storage platform that holds the file integrity monitoring log.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "string", "linkId": "948510289315", "code": [{"code": "FII-SCF-NET-0012.2"}], "text": "VPN authentication configurations including password settings", "item": [{"linkId": "271207369869", "type": "display", "text": "As evidence, you may include a URL along with details from Dropbox, Box, Google Drive, Portal, or any other storage platform that holds the VPN authentication configuration details.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"item": [{"type": "string", "linkId": "538892748284", "text": "Please provide the URL to share the Information Security Awareness Training new hire testing (sample) details..", "enableWhen": [{"question": "464222582092", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "696288934788", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, Portal, or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "944839850680", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have an Information Security Awareness Training new hire testing (sample) details as evidence, you can refer the our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/assets/professional-education-training-details.xlsx', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "464222582092", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "745566013229", "type": "display", "text": "As evidence, you can provide a Information Security Awareness Training new hire testing (sample) details.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "464222582092", "code": [{"code": "FII-SCF-SAT-0001"}], "text": "For a sample of new hires, evidence that information security and awareness training was performed upon hire. Evidence could include training completion forms, tracking tool/spreadsheet, or certificates", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "string", "linkId": "918204804074", "text": "Please provide the URL to share the Information Security Awareness Training current employee testing (sample) details.", "enableWhen": [{"question": "931792165707", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "153323392723", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, Portal, or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "952881964917", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have an Information Security Awareness Training current employee testing (sample) details as evidence, you can refer the our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/assets/professional-education-training-details.xlsx', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "931792165707", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "876384287973", "type": "display", "text": "As evidence, you can provide a Information Security Awareness Training current employee testing (sample) details.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "931792165707", "code": [{"code": "FII-SCF-SAT-0002"}], "text": "For a sample of current employees, evidence that information security and awareness training was performed annually. Evidence could include training completion forms, tracking tool/spreadsheet, or certificates", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "string", "linkId": "493170533519", "text": "Please provide the URL to share the Policies are available to vendors/contractors.", "enableWhen": [{"question": "747566513113", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "540219450058", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, Portal, or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "868908191237", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have a Policies are available to vendors/contractors as evidence, you can refer the our sample version of\n<span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/policies/common/information-technology-quality-infrastructure/incident-management-policy', '_blank')\">Incident Management Policy </span>, \n<span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/policies/common/information-technology-quality-infrastructure/change-management-policy', '_blank')\">Change Management Policy </span>, \n<span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/policies/common/information-technology-quality-infrastructure/information-security-policy', '_blank')\">Information Security Policy </span></p>"}]}, "enableWhen": [{"question": "747566513113", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "546481998366", "type": "display", "text": "As evidence, you can provide a Policies are available to vendors/contractors.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "747566513113", "code": [{"code": "FII-SCF-TPM-0002"}], "text": "Evidence showing key policies and procedures (e.g. Information Security, Change Management, Incident Management) were available to external parties (contractors, third parties, vendors, customers)) via company [website, shared drive, intranet, email, or contract/agreement, etc.]", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "string", "linkId": "880331464381", "text": "Please provide the URL to Escalation Policy details.", "enableWhen": [{"question": "172536737637", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "910238071164", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, Portal, or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "754017991471", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have an Escalation Policy details as evidence, you can refer the our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/policies/common/information-technology-quality-infrastructure/escalation-processes', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "172536737637", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "323472062929", "type": "display", "text": "As evidence, you can provide a Escalation Policy details.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "172536737637", "code": [{"code": "FII-SCF-IRO-0014"}], "text": "The Escalation policies and procedures for reporting failures, concerns, incidents, and complaints with revision history AND evidence that these escalation policies and procedures were available to external parties (contractors, third parties, vendors, customers) via company [website, shared drive, intranet, email, or contract/agreement, etc.]", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "string", "linkId": "316538566721", "text": "Please provide the URL to List of new customers.", "enableWhen": [{"question": "280402298184", "operator": "=", "answerCoding": {"display": "Yes"}}], "item": [{"linkId": "656052030267", "type": "display", "text": "Provide evidence as a URL along with details of Dropbox, Box, Google Drive, Portal, or other storage platforms.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "display", "linkId": "953250311012", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<p>If you don't have List of new customers as evidence, you can refer the our sample version <span style=\"color:blue; text-decoration:none; cursor:pointer;\"  onclick=\"window.open('https://suite.opsfolio.com/assets/list-of-new-customer.xlsx', '_blank')\">here</span></p>"}]}, "enableWhen": [{"question": "280402298184", "operator": "=", "answerCoding": {"display": "No"}}]}, {"linkId": "177534079321", "type": "display", "text": "As evidence, you can provide a List of new customers.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "280402298184", "code": [{"code": "FII-SCF-IAC-0002"}], "text": "Listing of new customers during the audit period", "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"type": "string", "linkId": "777584296488", "code": [{"code": "FII-SCF-BCD-0010.1"}], "text": "Contract (service agreement) for a sample of new customers", "item": [{"linkId": "145378887626", "type": "display", "text": "As evidence, you can provide a URL along with details from Dropbox, Box, Google Drive, Portal, or other storage platforms that hold sample contracts for new customers.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}], "type": "group", "linkId": "688660921201", "text": "Common Criteria Related to Information and Communications "}, {"type": "group", "linkId": "782061346593", "text": "Meta Information", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<div style=\"display:none\">\n<input  id=\"lhcFormMetaInformation\" value='{\n    \"createdAt\": \"2-10-2025\",\n    \"createdBy\": \"Arun KR\",\n    \"description\": \"As part of Plan 2, this option provides a balanced compliance approach by allowing organizations to retain their own IT and HR policies while leveraging our technical policies to meet SOC 2 requirements. Designed for organizations seeking a structured yet flexible compliance framework, it ensures alignment with SOC 2 standards while maintaining existing operational policies. This approach helps streamline audit preparation by integrating proven technical controls with an organization’s established policies. The following questionnaire is intended to collect Information and Communications informations and assess readiness for SOC 2 compliance.\",\n    \"title\": \"Information and Communications\",\n    \"organization\": \"Netspective Communication LLC\"\n}'>\n</div>"}]}}]}