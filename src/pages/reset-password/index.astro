---
import PasswordReset from "../../components/forgot-password/forgotPassword";
const url = Astro.request.url;
const params = url?.includes("?") ? decodeURIComponent(url.split("?")[1]) : "";
const queryParams = new URLSearchParams(params);

const code = queryParams.get("code") ?? undefined;
const userId = queryParams.get("userId") ?? undefined;
const email = queryParams.get("email") ?? undefined;
const envData = import.meta.env;
const enableOpenObserve = envData.ENABLE_OPEN_OBSERVE;
---

{
  enableOpenObserve == "true" && (
    <>
      <script
        type="module"
        crossorigin="anonymous"
        src="/assets/scripts/index.9987069c.js"
      />
      <link rel="modulepreload" href="/assets/scripts/vendor.4c9b4c60.js" />
    </>
  )
}

<link rel="stylesheet" href="/assets/styles/tailwind.min.css" />
<!-- <link rel="stylesheet" href="/assets/styles/basic.css" /> -->
<PasswordReset
  code={code ?? undefined}
  userID={userId ?? undefined}
  email={email ?? undefined}
  client:only="react"
/>
