---
import { Breadcrumbs } from "astro-breadcrumbs";
import TeamDetails from "../../components/teams/teamDetails";
import Layout from "../../layouts/Layout.astro";
import "astro-breadcrumbs/breadcrumbs.css";
import { teamDBConfig } from "../../utils/env";
import themeConfig from "../../../theme.config";
import DatabaseQueryRenderer from "../../components/database-query-Renderer/databaseQueryRenderer.astro";
const { organization } = themeConfig || {};

const url = new URL(Astro.request.url);
const userType = url.searchParams.get('userType');
---

<Layout title="Team">
  <div class="min-h-screen h-full">
    <div class="m-6 dark:text-gray-300">
      <Breadcrumbs
        linkTextFormat="capitalized"
        ariaLabel="Site navigation"
        separatorAriaHidden={false}
      >
        <svg
          slot="separator"
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
          ><polyline points="9 18 15 12 9 6"></polyline>
        </svg>
      </Breadcrumbs>
    </div>

    <TeamDetails client:only="react" userType= {userType} />

{
      teamDBConfig.isEnableTeamDB ? (
    <div class="team-list-views p-6 pt-4 mt-3 mx-auto">
      <!-- Toggle Buttons -->
      <div class="flex space-x-4 mb-4">
        <button
          id="gridViewBtn"
          title="Grid View"
          class="flex items-center font-semibold py-2 px-4 rounded-lg shadow-md border transition border-slate-300 active"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 16 16"
            fill="currentColor"
            class="w-4 h-4 mr-2"
          >
            <path
              fill-rule="evenodd"
              d="M15 11a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V5a2 2 0 1 1 2-2h10a2 2 0 1 1 2 2v6ZM7.25 7.5a.5.5 0 0 0-.5-.5H3a.5.5 0 0 0-.5.5V8a.5.5 0 0 0 .5.5h3.75a.5.5 0 0 0 .5-.5v-.5Zm1.5 3a.5.5 0 0 1 .5-.5H13a.5.5 0 0 1 .5.5v.5a.5.5 0 0 1-.5.5H9.25a.5.5 0 0 1-.5-.5v-.5ZM13.5 8v-.5A.5.5 0 0 0 13 7H9.25a.5.5 0 0 0-.5.5V8a.5.5 0 0 0 .5.5H13a.5.5 0 0 0 .5-.5Zm-6.75 3.5a.5.5 0 0 0 .5-.5v-.5a.5.5 0 0 0-.5-.5H3a.5.5 0 0 0-.5.5v.5a.5.5 0 0 0 .5.5h3.75Z"
              clip-rule="evenodd"></path>
          </svg>
          <span class="text-xs">Grid</span>
        </button>

        <button
          id="listViewBtn"
          title="List View"
          class="flex items-center font-semibold py-2 px-4 rounded-lg shadow-md border transition border-slate-300"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 16 16"
            fill="currentColor"
            class="w-4 h-4 mr-2"
          >
            <path
              d="M2 4a2 2 0 0 1 2-2h8a2 2 0 1 1 0 4H4a2 2 0 0 1-2-2ZM2 9.25a.75.75 0 0 1 .75-.75h10.5a.75.75 0 0 1 0 1.5H2.75A.75.75 0 0 1 2 9.25ZM2.75 12.5a.75.75 0 0 0 0 1.5h10.5a.75.75 0 0 0 0-1.5H2.75Z"
            ></path>
          </svg>
          <span class="text-xs">List</span>
        </button>
      </div>

      <!-- Content Views -->
      <div id="gridView" class="grid-view">
        <DatabaseQueryRenderer
            identifier="user_card"
            title={`${organization} Members`}
            layout="card"
            dbName={teamDBConfig.dbName}
            table="uniform_resource_user"
            fields={["Name AS title",'Role','Email','Organization']}
          />
      </div>

      <div id="listView" class="list-view hidden">
        <div class="mt-3 mx-auto team-table-content">
          <DatabaseQueryRenderer
            identifier="user_card"
            title={`${organization} Members`}
            layout="table"
            dbName={teamDBConfig.dbName}
            table="uniform_resource_user"
            fields={["*"]}
            orderBy="Name asc"
          />
        </div>
      </div>
    </div>
    ) : null
    }

  </div>
</Layout>

<script>
  const gridViewBtn = document.getElementById("gridViewBtn");
  const listViewBtn = document.getElementById("listViewBtn");
  const gridView = document.getElementById("gridView");
  const listView = document.getElementById("listView");

  // Check localStorage for saved view mode
  let isGridView = localStorage.getItem("viewMode") !== "list";

  function updateView() {
    if (isGridView) {
      gridView ? gridView.classList.remove("hidden") : "";
      listView ? listView.classList.add("hidden") : "";
      gridViewBtn ? gridViewBtn.classList.add("active") : "";
      listViewBtn ? listViewBtn.classList.remove("active") : "";
    } else {
      listView ? listView.classList.remove("hidden") : "";
      gridView ? gridView.classList.add("hidden") : "";
      listViewBtn ? listViewBtn.classList.add("active") : "";
      gridViewBtn ? gridViewBtn.classList.remove("active") : "";
    }
    localStorage.setItem("viewMode", isGridView ? "grid" : "list");
  }

  if (gridViewBtn)
    gridViewBtn.addEventListener("click", () => {
      isGridView = true;
      updateView();
    });

  if (listViewBtn)
    listViewBtn.addEventListener("click", () => {
      isGridView = false;
      updateView();
    });

  // Set initial view based on localStorage
  updateView();
</script>

<style>
  .hidden {
    display: none;
  }
  .active {
    background-color: #e2e8f0;
    color: #0f172a;
    border-color: #94a3b8;
  }

  :global(.team-list-views h2) {
    font-size: 1.5rem !important;
    line-height: 2rem !important;
    font-weight: 600 !important;
    color: #1f2937 !important;
    margin-bottom: 1rem !important;
  }
  :global(.team-table-content table) {
    --tw-border-opacity: 1 !important;
    background-color: #f9fafb !important;
    border-width: 1px !important;
    border-collapse: collapse !important;
    width: 100% !important;
  }
  :global(.team-table-content thead) {
  }
  :global(.team-table-content tr) {
    --tw-bg-opacity: 1;
    background-color: #f3f4f6 !important;
  }
  :global(.team-table-content th) {
    text-align: left;
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
    padding-left: 1rem !important;
    padding-right: 1rem !important;
    --tw-border-opacity: 1 !important;
    border-color: #d1d5db !important;
    border-width: 1px !important;
  }
  :global(.team-table-content td) {
  }
</style>
