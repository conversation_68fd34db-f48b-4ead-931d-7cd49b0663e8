---
import Layout from "../../layouts/Layout.astro";
import Sidebar from "../../components/Sidebar";
import { buildMenuTree } from "../../utils/helper.astro";
import { Breadcrumbs } from "astro-breadcrumbs";
import "astro-breadcrumbs/breadcrumbs.css";
import themeConfig from "../../../theme.config";
import LHCFormsWidget from "../../components/lhc-forms-widget/lhc-forms-widget.astro";
import LHCFormsResponseWidget from "../../components/lhc-forms-widget/ihc-form-response-widget.astro";
import SubmittedFormList from "../../components/lhc-forms-widget/submitted-form-list.astro";
import {slugify} from "../../components/lhc-forms-widget/service";

const { contentCollectionSort } = themeConfig || {};
const files = import.meta.glob("/src/content/lforms/**/*.{json,yml}", {
  eager: true,
});
const dirName = "lforms";
// Build the menu tree
const menuTree = buildMenuTree(files, dirName, contentCollectionSort, "asc");
function removeSubmissions(menuTree: any[]): any[] {
  return menuTree.filter((item) => item.name !== "Submissions");
}

const filteredMenuTree = removeSubmissions(menuTree);
const fileName =
  filteredMenuTree.length > 0 ? filteredMenuTree[0].path.split("/").pop() || "" : "";
const title = fileName.replace(/\.(json|yml)$/, "").replace(/[-_.]+/g, " ");
const { searchParams } = Astro.url;
const currentTab = searchParams.get("tab"); // Get 'tab' parameter
const submittedForm = searchParams.get("submitted-form");

const activeTabClass =
  " text-black border-b-2 border-black  active  dark:text-blue-500 dark:border-blue-500";
const inActiveTabClass =
  "hover:text-gray-600 hover:bg-gray-50 dark:hover:bg-gray-800 dark:hover:text-gray-300";
---

<Layout title="LForms">
  <main class="max-w-full mx-auto min-h-[70vh] grid grid-cols-12 gap-6">
    <div
      class="col-span-12 md:col-span-4 lg:col-span-3 xl:col-span-2 bg-white dark:bg-gray-800 p-3 shadow-xs"
    >
      <!-- Left Menu Content Goes Here -->
      <div id="starlight__sidebar" class="sidebar-pane">
        <div class="sidebar-content">
          <div class="sidebar sl-flex sidebar-left-menu font-medium text-base">
            <Sidebar menuTree={filteredMenuTree} slugval="" />
          </div>
        </div>
      </div>
    </div>
    <div
      class="col-span-12 md:col-span-8 lg:col-span-9 xl:col-span-10 pb-5 pt-0 md:pt-5 px-5 md:px-0 md:pr-5 dark:text-gray-300"
    >
      <Breadcrumbs
        linkTextFormat="capitalized"
        ariaLabel="Site navigation"
        separatorAriaHidden={false}
      >
        <svg
          slot="separator"
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
          ><polyline points="9 18 15 12 9 6"></polyline>
        </svg>
      </Breadcrumbs>
      <div class="border-b border-slate-300 dark:border-gray-600 pb-3">
        <h1 class="text-2xl lg:text-4xl font-bold text-slate-700 dark:text-gray-300 mt-4 mb-1">
          {title}
        </h1>
      </div>

      <div
        class="text-sm font-medium text-center text-gray-500 border-b border-gray-200 dark:border-gray-600 dark:text-gray-400"
      >
      </div>

      <ul
        class="flex flex-wrap text-sm font-medium text-center text-gray-500 border-b border-gray-200 dark:border-gray-600 dark:text-gray-400"
      >
        <li class="me-2">
          <a
            href="?tab=form"
            aria-current="page"
            class=`inline-block p-4 rounded-t-lgc ${currentTab=='form' || currentTab == "" || currentTab == null ? activeTabClass: inActiveTabClass}`
          >
            Dashboard
          </a>
        </li>
        <li class="me-2">
          <a
            href="?tab=data"
            class=`inline-block p-4 rounded-t-lg ${currentTab=='data'? activeTabClass: inActiveTabClass}`
          >
            Submitted Entries
          </a>
        </li>
      </ul>
      {
        currentTab == "form" || currentTab == "" || currentTab == null ? (
          <div class="p-4">
            <LHCFormsWidget fileName={fileName} folderPath="lforms"  pageSlug={`lforms-${slugify(fileName)}`}/>
          </div>
        ) : (
          ""
        )
      }

      {
        currentTab == "data" ? (
          <>
            {submittedForm ? (
              <div class="flex flex-col">
                <div class=" float-right">
                  <a
                    href="?tab=data"
                    type="button"
                    class=" text-white float-right w-[80px] bg-cyan-700 hover:bg-cyan-800 focus:ring-4 focus:ring-cyan-300 font-medium rounded-lg text-sm px-5 py-2.5  dark:bg-cyan-600 dark:hover:bg-cyan-700 focus:outline-none dark:focus:ring-cyan-800"
                  >
                    Back
                  </a>
                </div>
                <LHCFormsResponseWidget fileName={submittedForm} />
              </div>
            ) : <SubmittedFormList fileName={`lforms-${slugify(fileName)}.lform-submittion.json`} />}
          </>
        ) : (
          ""
        )
      }
    </div>
  </main>
</Layout>
