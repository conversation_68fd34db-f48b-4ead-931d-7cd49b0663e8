---
import Layout from "../../../../layouts/Layout.astro";
import { getGitHubIssue, postGitHubComment, closeGitHubIssue } from '../../../../lib/github.ts';
import themeConfig from '../../../../../theme.config';
import order from "../../../expectations/order.json";
import {convertQuestionnaire, replaceQuestionnaire} from "../../../expectations/questionnaireOrder"
import { buildMenuTree } from "../../../../utils/helper.astro";
// import { getPageFromSlug } from "../../../../components/lhc-forms-widget/service";
import Sidebar from "../../../../components/Sidebar";
import { Breadcrumbs } from "astro-breadcrumbs";
import "astro-breadcrumbs/breadcrumbs.css";
import yaml from 'js-yaml';
import ActivityLog from "../../../../components/activity-log/activityLog";

type FormData = {
  name?: string;
  email?: string;
  subject?: string;
  message?: string;
  submissionDate?: string;
  pageUrl?: string;
};


const { id } = Astro.params;
if (!id) {
  throw new Error('Issue ID is missing from URL.');
}

if (Astro.request.method === 'POST') {
  const formData = await Astro.request.formData();
  const action = new URL(Astro.url).searchParams.get('action');
  const comment = formData.get('comment')?.toString();

  try {
    if (action === 'comment' && comment) {
      await postGitHubComment(id, comment);
    } else if (action === 'close') {
      await closeGitHubIssue(id);
      return Astro.redirect(`/expectations/support-and-feedback/issues`); 

    }
  } catch (err) {
    console.error('❌ Failed to process GitHub action:', err);
  }
}

const issueData = await getGitHubIssue(id);
const files = import.meta.glob("/src/content/expectations/**/*.{md,mdx}", {
  eager: true,
});

// Extract YAML block from the body using RegEx
let formData: FormData = {};
const match = issueData.body.match(/```yaml\n([\s\S]*?)\n```/);

if (match && match[1]) {
  try {
    formData = yaml.load(match[1]);
  } catch (error) {
    console.error("YAML parsing error:", error);
  }
} else {
  console.error("No YAML block found in issue body.");
}
const { contentCollectionSort } = themeConfig || {}; 
const orderJson = order;
const questionnaireOrder = convertQuestionnaire(orderJson as Questionnaire);
const dirName = "expectations";

const menuTree = buildMenuTree(files, dirName, contentCollectionSort, "asc");
const updatedMenuTree = replaceQuestionnaire(menuTree, questionnaireOrder);
//const pageSlug = getPageFromSlug(id as string);
const slugval = "/expectations/support-and-feedback/issues";
const pageUrl=Astro.url.href;
function timeSince(dateStr: string) {
  const date = new Date(dateStr);
  const now = new Date();

  const diffMs = now.getTime() - date.getTime();
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  const diffMonths = now.getMonth() - date.getMonth() + 12 * (now.getFullYear() - date.getFullYear());
  const isSameYear = now.getFullYear() === date.getFullYear();

  if (diffDays === 0) return 'today';
  if (diffDays === 1) return 'yesterday';
  if (diffDays < 30) return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
  if (diffMonths === 1) return 'last month';
  if (diffMonths < 12) return `${diffMonths} month${diffMonths > 1 ? 's' : ''} ago`;

  const options: Intl.DateTimeFormatOptions = {
    month: 'short',
    day: 'numeric',
    ...(isSameYear ? {} : { year: 'numeric' }),
  };
  return `on ${date.toLocaleDateString('en-US', options)}`;
}
const owner = import.meta.env.PUBLIC_GITHUB_OWNER;
const repo = import.meta.env.PUBLIC_GITHUB_REPO;

---

<Layout title="">
  <main class="max-w-full mx-auto min-h-[70vh] grid grid-cols-12 gap-6 flex">
    <div class="col-span-12 md:col-span-4 lg:col-span-2 xl:col-span-2 bg-white dark:bg-gray-800 p-3 shadow-xs">
      <div id="starlight__sidebar" class="sidebar-pane">
        <div class="sidebar-content">
          <div class="sidebar sl-flex sidebar-left-menu font-medium text-base">
            <Sidebar menuTree={updatedMenuTree} slugval={slugval} />
          </div>
        </div>
      </div>
    </div>
   
    <section class="col-span-12 md:col-span-6 lg:col-span-7 xl:col-span-7 pb-5 pt-0 md:pt-5 px-5 md:px-0 md:pr-5 dark:text-gray-300">
      <div class="pb-5">
        <Breadcrumbs
          linkTextFormat="capitalized"
          ariaLabel="Site navigation"
          separatorAriaHidden={false}
          customizeLinks={[
            { index: 2, "aria-disabled": true },
            { index: 4, "aria-disabled": true },
          ]}
        >
          <svg slot="separator" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
            fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <polyline points="9 18 15 12 9 6"></polyline>
          </svg>
        </Breadcrumbs>
      </div>

      <h1 class="text-3xl font-bold text-gray-900 mb-3">
        {issueData.title}
      </h1>

      <p class="text-sm text-gray-500 mb-4 hidden">
        Created by
        <a href={issueData.user.html_url} target="_blank" class="text-blue-600 hover:underline">
          {issueData.user.login}
        </a>
      </p>

      <div class="bg-gray-50 p-4 rounded border text-gray-800 mb-6 shadow-md">      
         
          {formData.name ? (
            <ul class="space-y-1 mb-3">
              <h3 class="text-sm font-normal mb-4 text-end"><span class="text-gray-500 font-medium">Submitted By :  </span><strong class="text-gray-500">{formData.name}</strong> - <span class="text-gray-500">{timeSince(formData.submissionDate ?? "")}</span></h3>
              {/* <li><strong>Name:</strong> {formData.name}</li> */}
              {/* <li><strong>Email:</strong> {formData.email}</li> */}
              <li class="text-sm mb-4">{formData.message}</li>
              {/* <li><strong>Submitted On:</strong> {timeSince(formData.submissionDate ?? "")}</li> */}
              {/* <li><strong>Page URL:</strong> <a href={formData.pageUrl}>{formData.pageUrl}</a></li> */}
            </ul>
          ) : (
            <p>No valid YAML data found.</p>
          )}
          <img
           src={`https://raw.githubusercontent.com/${owner}/${repo}/main/uploads/${issueData.number}.png`}
          alt={`Screenshot for issue #${issueData.number}`}
          />
      </div>

      <h2 class="text-xl font-semibold text-gray-800 mb-2">Add a Comment</h2>
      <form method="post" class="space-y-4" onsubmit="return validateComment()">
        <textarea
          name="comment"
          id="comment"
          rows="4"
          class="w-full border border-gray-300 rounded p-3 text-sm focus:ring-2 focus:ring-blue-400 focus:outline-none"
          placeholder="Write your comment here..."
        ></textarea>
        <p id="commentError" class="text-red-600 text-sm hidden">Comment cannot be empty.</p>
        <div class="flex gap-3">
          <button
            type="submit"
            formaction="?action=comment"
            class="bg-blue-600 hover:bg-blue-700 text-white px-5 py-2 rounded-md text-sm font-medium transition"
          >
            Comment
          </button>

          <!-- Trigger modal for confirmation -->
          <button
            type="button"
            class="bg-red-600 hover:bg-red-700 text-white px-5 py-2 rounded-md text-sm font-medium transition"
            onclick="document.getElementById('confirmModal').showModal()"
          >
            Close Issue
          </button>
        </div>
      </form>

      <!-- Modal for confirmation -->
      <dialog id="confirmModal" class="rounded-lg p-6 shadow-lg max-w-md w-full border border-gray-300 backdrop:bg-black/50">
        <form method="post" action="?action=close">
          <h3 class="text-lg font-semibold mb-4 text-gray-800">Confirm Close Issue</h3>
          <p class="text-sm text-gray-600 mb-6">Are you sure you want to close this issue?</p>

          <div class="flex justify-end gap-3">
            <button
              type="button"
              class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-4 py-2 rounded-md"
              onclick="document.getElementById('confirmModal').close()"
            >
              Cancel
            </button>
            <button
              type="submit"
              class="bg-black hover:bg-black text-white px-4 py-2 rounded-md"
            >
              Confirm
            </button>
          </div>
        </form>
      </dialog>
    </section>
    <div class="col-span-12 md:col-span-3 lg:col-span-3 xl:col-span-3 pb-5 pt-0 md:pt-5 px-5 md:px-0 md:pr-5 dark:text-gray-300">
     <aside style="display: block; padding-top:20px; height:700px ">
        <div class="flow-root">
          <ul
            class="flex flex-wrap -mb-px font-medium text-center"
            id="history-tab"
            data-tabs-toggle="#default-tab-content"
          >         
            <li class="ml-4 mt-3" >
              <button
                class="inline-block p-4 border-b-2 rounded-t-lg hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300"
                id="activity-tab"
                data-tabs-target="#activity"
                type="button"
                role="tab"
                aria-controls="activity"
                aria-selected="false"
                aria-label="Activity"
              >
                Activity
              </button>
            </li>
          </ul>
          <div id="default-tab-content">        
            
            <div
              class="hidden p-4 rounded-lg bg-gray-50 dark:bg-gray-800"
              id="activity"
              role="tabpanel"
              aria-labelledby="activity-tab"
            >
             <ActivityLog
            pageUrl={pageUrl}
            recordsLimit={5}
            showViewMoreButton={false}
            hoursToFetch={8,760}
             client:only="react"
      />
            </div>
          </div>
        </div>
      </aside>
    </div>
  </main>
</Layout>
<script is:inline>
  function validateComment() {
    const comment = document.getElementById('comment').value.trim();
    const error = document.getElementById('commentError');

    if (comment === '') {
      error.classList.remove('hidden');
      return false; // Block form submission
    } else {
      error.classList.add('hidden');
      return true; // Allow form submission
    }
  }
</script>