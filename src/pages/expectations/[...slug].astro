---
import { render, getEntry } from "astro:content";
import Layout from "../../layouts/Layout.astro";
import Sidebar from "../../components/Sidebar";
import { buildMenuTree } from "../../utils/helper.astro";
import { Breadcrumbs } from "astro-breadcrumbs";
import "astro-breadcrumbs/breadcrumbs.css";
import LHCFormsWidget from "../../components/lhc-forms-widget/lhc-forms-widget.astro";
import LHCFormsResponseWidget from "../../components/lhc-forms-widget/ihc-form-response-widget.astro";
import themeConfig from "../../../theme.config";
import SubmittedFormList from "../../components/lhc-forms-widget/submitted-form-list.astro";
import LatestCommit from "../../components/git-commit/latestCommit.astro";
import type { Questionnaire, OutputNode } from "./typs";
import { convertQuestionnaire, replaceQuestionnaire } from "./questionnaireOrder"
import { getPageFromSlug, slugify } from "../../components/lhc-forms-widget/service";
import order from "./order.json";
import CommitHistory from "../../components/git-commit/commitHistory.tsx";
import LatestMoreCommit from "../../components/git-commit/latestMoreCommit.astro";
import ActivityLog from "../../components/activity-log/activityLog";
import SurveilrCommits from "../../components/git-commit/surveilrCommit.tsx";
import Reaction from "../../components/page-reaction/reaction.astro";
import Comment from "../../components/comment/comment.astro";
import MarkdownEditor from "../../components/MarkdownEditor";
import fs from "fs/promises";
import path from "path";

const orderJson = order;
const questionnaireOrder = convertQuestionnaire(orderJson as Questionnaire);

const { contentCollectionSort } = themeConfig || {};
const { enablePageHistory } = themeConfig || {};
const files = import.meta.glob("/src/content/expectations/**/*.{md,mdx,json}", {
  eager: true,
});
const dirName = "expectations";

// Build the menu tree
const menuTree = buildMenuTree(files, dirName, contentCollectionSort, "asc");
const updatedMenuTree = replaceQuestionnaire(menuTree, questionnaireOrder);

function hasJsonOrYamlExtension(url: string): boolean {
  return /\.(json|ya?ml)(\?.*|#.*|$)/i.test(url);
}

const slug = Astro.params.slug;
const pageSlug = getPageFromSlug(slug as string);
let isLform = false;
if (hasJsonOrYamlExtension(`expectations/${pageSlug}`)) {
  isLform = true;
}
const { searchParams } = Astro.url;

let entry;
let entryid;
let Content;
let headings;
let remarkPluginFrontmatter;
let fileName;
let currentTab;
let url;
let submittedForm;
let activeTabClass;
let inActiveTabClass;
let title;
let checkPageHistory;

const slugval = "/expectations/" + pageSlug;
let folderPath = slugval.replace(/\/[^/]+$/, "").replace(/^\/+/, "");
let fullFilePath = "";

if (isLform) {
  fileName = slugval.split("/").pop() || "";
  currentTab = searchParams.get("tab");
  url = Astro.url;
  submittedForm = searchParams.get("submitted-form");
  activeTabClass = " text-black border-b-2 border-black  active  dark:text-blue-500 dark:border-blue-500";
  inActiveTabClass = "hover:text-gray-600 hover:bg-gray-50 dark:hover:bg-gray-800 dark:hover:text-gray-300";
  title = fileName
    .replace(/\.lhc-form/, "")
    .replace(/\.(json|yml)$/, "")
    .replace(/[-_.]+/g, " ")
    .split(" ")
    .map(word => word.toLowerCase() === "soc2" ? "SOC2" : word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(" ");
  fullFilePath = path.resolve(`./src/content/expectations/${pageSlug}`);
} else {
  entry = await getEntry("expectations", pageSlug);
  entryid = entry?.id;
  if (!entry) {
    throw new Error("Could not find data");
  }

  const { id } = entry;
  const filePath = path.resolve(`./src/content/expectations/${id}.md`);
  fullFilePath = filePath;   
  try {
    await fs.access(filePath.replace(/\.md$/, ".mdx"));
    fullFilePath = filePath.replace(/\.md$/, ".mdx");
  } catch {
    // keep .md
  }

  ({ Content, headings, remarkPluginFrontmatter } = await render(entry));
}
checkPageHistory = entry?.data.enablePageHistory;

const isSurveilr = pageSlug === "compliance-readiness/evidence-workflow";
const rawMarkdownContent = await fs.readFile(fullFilePath, "utf-8");
const pageUrl = Astro.url.href;
---


<Layout title={isLform ? title : entry.data.title} redirect={isLform ? "" : entry.data.redirect} enableEditButton={isLform ? false : entry.data.enableEditButton} rawMarkdownContent={rawMarkdownContent}>
  <main class="max-w-full mx-auto min-h-[70vh] grid grid-cols-12 gap-6 flex">
    <div
      class="col-span-12 md:col-span-4 lg:col-span-2 xl:col-span-2 bg-white dark:bg-gray-800 p-3 shadow-xs"
    >
      <div id="starlight__sidebar" class="sidebar-pane">
        <div class="sidebar-content">
          <div class="sidebar sl-flex sidebar-left-menu font-medium text-base">
            <Sidebar menuTree={updatedMenuTree} slugval={slugval} />
          </div>
        </div>
      </div>
    </div>
    <div
      class="col-span-12 md:col-span-8 lg:col-span-7 xl:col-span-7 pb-5 pt-0 md:pt-5 px-5 md:px-0 md:pr-5 dark:text-gray-300"
    >
      <Breadcrumbs
        linkTextFormat="capitalized"
        ariaLabel="Site navigation"
        separatorAriaHidden={false}
        customizeLinks={[
          { index: 2, "aria-disabled": true },
          { index: 3, "aria-disabled": true },
          { index: 4, "aria-disabled": true },
        ]}
      >
        <svg
          slot="separator"
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
          ><polyline points="9 18 15 12 9 6"></polyline>
        </svg>
      </Breadcrumbs>
      
      { entry?.data.enableReaction && <div class="flex justify-end w-full">
        <Reaction/>
      </div>}


      {isLform ? ( <div>
            <div class="border-b border-slate-300 dark:border-gray-700 pb-3">
              <h1 class="text-2xl lg:text-4xl font-bold text-slate-700 dark:text-gray-300 mt-4 mb-1">
                {title}
              </h1>
            </div>
            <div
        class="text-sm font-medium text-center text-gray-500 border-b border-gray-200 dark:text-gray-400 dark:border-gray-700"
      >
      </div>

      <ul
        class="flex flex-wrap text-sm font-medium text-center text-gray-500 border-b border-gray-200 dark:border-gray-700 dark:text-gray-400"
      >
        <li class="me-2">
          <a
            href="?tab=form"
            aria-current="page"
            class=`inline-block p-4 rounded-t-lgc ${currentTab=='form' || currentTab == "" || currentTab == null ? activeTabClass: inActiveTabClass}`
          >
            Dashboard
          </a>
        </li>
        <li class="me-2">
          <a
            href="?tab=data"
            class=`inline-block p-4 rounded-t-lg ${currentTab=='data'? activeTabClass: inActiveTabClass}`
          >
            Submitted Entries
          </a>
        </li>
      </ul>
      {
        currentTab == "form" || currentTab == "" || currentTab == null ? (
          <div class="p-4">
           <LHCFormsWidget
              fileName={fileName}
              folderPath={folderPath}
              pageSlug={slugify(pageSlug)}
            />
          </div>
        ) : (
          ""
        )
      }
      {
        currentTab == "data" ? (
          <>
            {submittedForm ? (
              <div class="flex flex-col">
                <div class=" float-right">
                  <a
                    href="?tab=data"
                    type="button"
                    class=" text-white float-right w-[80px] bg-cyan-700 hover:bg-cyan-800 focus:ring-4 focus:ring-cyan-300 font-medium rounded-lg text-sm px-5 py-2.5  dark:bg-cyan-600 dark:hover:bg-cyan-700 focus:outline-none dark:focus:ring-cyan-800"
                  >
                    Back
                  </a>
                </div>
                <LHCFormsResponseWidget fileName={submittedForm} />
              </div>
            ) : <SubmittedFormList fileName={`${slugify(pageSlug)}.lform-submittion.json`} />}
          </>
        ) : (
          ""
        )
      }

            
          </div>) :(  <article class="prose max-w-screen-xl withbullet markdown-content">
        {/* <span id="markdown-content" style="display:none">
          <MarkdownEditor bodyContent={rawMarkdownContent} client:only="react" />
        </span> */}
        <span id="html-content"> <Content /></span>
      </article> )}
      <Comment discussionEnabled={entry?.data.discussionsEnabled ? true : false} source="Demo"/>
    </div>
    
    
    <div class="col-span-12 md:col-span-12 lg:col-span-3 xl:col-span-3 lg:pb-5 pt-0 lg:pt-5 px-5 md:px-0 md:pr-5 dark:text-gray-300">
     <aside style="display: block; padding-top:20px;" class="lg:min-h-[700px]">
        <div class="flow-root">
          <ul
            class="flex flex-wrap -mb-px font-medium text-center"
            id="history-tab"
            data-tabs-toggle="#default-tab-content"
          >
          {!isLform && enablePageHistory && checkPageHistory && pageSlug !== 'support-and-feedback/issues' && (
              <li class="mt-3">
                <button
                  class="inline-block py-4 px-2 lg:px-2 xl:px-4 border-b-2 rounded-t-lg hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300"
                  id="dashboard-tab"
                  data-tabs-target="#dashboard"
                  type="button"
                  role="tab"
                  aria-controls="dashboard"
                  aria-selected="false"
                  aria-label="Commit History"
                >
                  Page History
                </button>
              </li>
            )}
        
          
            <li class="ml-4 mt-3" >
              <button
                class="inline-block py-4 px-2 lg:px-2 xl:px-4 border-b-2 rounded-t-lg hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300"
                id="activity-tab"
                data-tabs-target="#activity"
                type="button"
                role="tab"
                aria-controls="activity"
                aria-selected="false"
                aria-label="Activity"
              >
                Activity
              </button>
            </li>
          </ul>
          <div id="default-tab-content">
         
            <div
              class="hidden p-4 px-0 rounded-lg bg-gray-50 dark:bg-gray-800"
              id="dashboard"
              role="tabpanel"
              aria-labelledby="dashboard-tab"
            >
            <div>
              <LatestCommit filename={entryid} pageTitle={entry?.data.title} />
            </div>
            <div>
              {isSurveilr
                ? <SurveilrCommits filename={entryid} client:only="react" />
                : <CommitHistory filename={entryid} client:only="react" />
              }
            </div>               
            </div>
            <div
              class="hidden py-4 rounded-lg bg-gray-50 dark:bg-gray-800 mt-2"
              id="activity"
              role="tabpanel"
              aria-labelledby="activity-tab"
            >
             <ActivityLog
            pageUrl={pageUrl}
            recordsLimit={5}
            showViewMoreButton={false}
            hoursToFetch={8,760}
             client:only="react"
      />
            </div>
          </div>
        </div>
      </aside>
    </div>
    
    <!-- <LatestMoreCommit filename={entryid} pageTitle={entry?.data.title} /> -->
  
  </main>
</Layout>
