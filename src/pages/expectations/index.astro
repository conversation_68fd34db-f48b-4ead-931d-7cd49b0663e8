---
import Layout from "../../layouts/Layout.astro";
import Sidebar from "../../components/Sidebar";
import { buildMenuTree } from "../../utils/helper.astro";
import { Breadcrumbs } from "astro-breadcrumbs";
import "astro-breadcrumbs/breadcrumbs.css";
import themeConfig from "../../../theme.config";
import type { Questionnaire } from "./typs";
import {
  convertQuestionnaire,
  replaceQuestionnaire,
} from "./questionnaireOrder";
import order from "./order.json";

const orderJson = order;
const questionnaireOrder = convertQuestionnaire(orderJson as Questionnaire);

const { contentCollectionSort } = themeConfig || {};

const files = import.meta.glob("/src/content/expectations/**/*.{md,mdx,json}", {
  eager: true,
});
const dirName = "expectations";
// Build the menu tree
const menuTree = buildMenuTree(files, dirName, contentCollectionSort, "asc");

const updatedMenuTree = replaceQuestionnaire(menuTree, questionnaireOrder);
---

<Layout title="Expectations">
  <main class="max-w-full mx-auto min-h-[70vh] grid grid-cols-12 gap-6">
    <div
      class="col-span-12 md:col-span-4 lg:col-span-3 xl:col-span-2 bg-white dark:bg-gray-800 p-3 shadow-xs"
    >
      <!-- Left Menu Content Goes Here -->
      <div id="starlight__sidebar" class="sidebar-pane">
        <div class="sidebar-content">
          <div class="sidebar sl-flex sidebar-left-menu font-medium text-base">
            <Sidebar menuTree={updatedMenuTree} slugval="" />
          </div>
        </div>
      </div>
    </div>
    <div
      class="col-span-12 md:col-span-8 lg:col-span-9 xl:col-span-10 pb-5 pt-0 md:pt-5 px-5 md:px-0 md:pr-5 dark:text-gray-300"
    >
      <Breadcrumbs
        linkTextFormat="capitalized"
        ariaLabel="Site navigation"
        separatorAriaHidden={false}
      >
        <svg
          slot="separator"
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
          ><polyline points="9 18 15 12 9 6"></polyline>
        </svg>
      </Breadcrumbs>
      <div class="border-b border-slate-300 dark:border-gray-600 pb-3">
        <h1
          class="text-2xl lg:text-4xl font-bold text-slate-700 dark:text-gray-300 mt-4 mb-1"
        >
          Expectations Overview
        </h1>
      </div>

      <section class="mt-4 grid grid-cols-1 lg:grid-cols-12 gap-4">
        {
          menuTree.map((page, index) => {
            // Check if it's a folder and has children
            const firstFilePath =
              !page.isFile && page.children
                ? page.children.find((child: { isFile: any; }) => child.isFile)?.path // Get the path of the first file
                : page.path;
            const firstFileDescription =
              !page.isFile && page.children
                ? page.children.find((child: { isFile: any; }) => child.isFile)?.description // Get the path of the first file
                : page.description;

            return (
              <a
                class="md:col-span-6 md:min-h-80 bg-white dark:bg-gray-800 shadow rounded-lg p-4"
                href={page.name === "Questionnaire" ? "/expectations/questionnaire/pre-audit/opsfolio-caas-onboarding-questionnaire.lhc-form.json/" : firstFilePath || ""}
              >
                <div class="flex items-start gap-3">
                  <div class="w-[24px] flex-shrink-0">
                    <img
                      src="/assets/images/fi-rr-document-2.svg"
                      class="w-full mt-1"
                      alt="Icon"
                    />
                  </div>
                  <div>
                    <h3 class="text-lg font-semibold text-slate-700 dark:text-gray-300 mb-2">
                      {page.name}
                    </h3>
                    <aside>{firstFileDescription}</aside>
                  </div>
                </div>
              </a>
            );
          })
        }
      </section>
    </div>
  </main>
</Layout>
