---
import Layout from "../../layouts/Layout.astro";
import Sidebar from "../../components/Sidebar";
import { Breadcrumbs } from "astro-breadcrumbs";
import MailBox from "../../components/imap-mail-box/mailBox.astro";
import "astro-breadcrumbs/breadcrumbs.css";
import { sql } from "drizzle-orm";
import { imapDBPath, isIMAPViewEnabled } from "../../utils/env";
import { connectionDB } from "../../db/db";
import { urIngestSessionImapAccount } from "./schema";

console.log(isIMAPViewEnabled);
interface MenuNode {
  name: string;
  path: string;
  children?: MenuNode[];
  isFile: boolean;
}
const db = connectionDB(imapDBPath);
// console.log(db);
let menuTree: MenuNode[] = [] as MenuNode[];
if (db) {
  const accounts = await db
    .select({
      path: sql`'?account=' || ${urIngestSessionImapAccount.urIngestSessionImapAccountId}|| '&view=list'`,
      name: urIngestSessionImapAccount.email,
      isFile: sql`TRUE`, // Ensure correct SQL boolean representation
    })
    .from(urIngestSessionImapAccount);
  menuTree = accounts as MenuNode[];
}
---

<Layout>
  <main class="max-w-full mx-auto min-h-[70vh] grid grid-cols-12 gap-6">
    <div
      class="col-span-12 md:col-span-4 lg:col-span-3 xl:col-span-2 bg-white p-3 shadow-xs"
    >
      <!-- Left Menu Content Goes Here -->
      <div id="starlight__sidebar" class="sidebar-pane">
        <div class="sidebar-content">
          <div class="sidebar sl-flex sidebar-left-menu font-medium text-base">
            {menuTree ? <Sidebar menuTree={menuTree} slugval="" /> : null}
          </div>
        </div>
      </div>
    </div>
    <div
      class="col-span-12 md:col-span-8 lg:col-span-9 xl:col-span-10 pb-5 pt-0 md:pt-5 px-5 md:px-0 md:pr-5"
    >
      <Breadcrumbs
        linkTextFormat="capitalized"
        ariaLabel="Site navigation"
        separatorAriaHidden={false}
      >
        <svg
          slot="separator"
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
          ><polyline points="9 18 15 12 9 6"></polyline>
        </svg>
      </Breadcrumbs>
      {
        isIMAPViewEnabled && imapDBPath ? (
          <MailBox />
        ) : (
          <p>
            Configurations need to be enabled for getting email data... Please
            check with administrator
          </p>
        )
      }
    </div>
  </main>
</Layout>
