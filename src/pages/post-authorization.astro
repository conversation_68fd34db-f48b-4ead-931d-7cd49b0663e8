---
import { Authentication } from "../components/zitadel-authentication";
const envData = import.meta.env;
const clientId = envData.PUBLIC_ZITADEL_CLIENT_ID;
const authority = envData.PUBLIC_ZITADEL_AUTHORITY;
const redirectUri = envData.PUBLIC_ZITADEL_REDIRECT_URI;
const postLogoutRedirectUri = envData.PUBLIC_ZITADEL_LOGOUT_REDIRECT_URI;
const organizationId = envData.PUBLIC_ZITADEL_ORGANIZATION_ID;
const projectId = envData.PUBLIC_ZITADEL_PROJECT_ID;
const enableOpenObserve = envData.ENABLE_OPEN_OBSERVE;
---

{
  enableOpenObserve == "true" && (
    <>
      <script
        type="module"
        crossorigin="anonymous"
        src="/assets/scripts/index.9987069c.js"
      />
      <link rel="modulepreload" href="/assets/scripts/vendor.4c9b4c60.js" />
    </>
  )
}

<Authentication
  clientId={clientId}
  authority={authority}
  redirectUri={redirectUri}
  postLogoutRedirectUri={postLogoutRedirectUri}
  organizationId={organizationId}
  projectId={projectId}
  operation="login"
/>
