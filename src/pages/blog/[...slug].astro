---
import Layout from "../../layouts/Layout.astro";
import { render, getEntry } from "astro:content";
import { Breadcrumbs } from "astro-breadcrumbs";
import "astro-breadcrumbs/breadcrumbs.css";
import fs from "fs/promises";
import path from "path";

const slug = Astro.params.slug;
const entry = await getEntry("blog", slug);
if (!entry) {
  // Handle Error, for example:
  throw new Error("Could not find data");
}

// After getting the entry:
const { id } = entry; // id will be something like "section/page-name"
const filePath = path.resolve(`./src/content/blog/${id}.md`);
// Check if .mdx exists instead
let fullFilePath = filePath;
try {
  await fs.access(filePath.replace(/\.md$/, ".mdx"));
  fullFilePath = filePath.replace(/\.md$/, ".mdx");
} catch {
  // Stick with .md
}

const { Content } = await render(entry);
const rawMarkdownContent = await fs.readFile(fullFilePath, "utf-8");
// const pageUrl=Astro.url.href;


let formattedDate = '';
if (entry.data.date) {
  formattedDate = new Date(entry.data.date).toLocaleDateString("en-US", {
    month: "short",
    day: "numeric",
    year: "numeric",
  });
}
---

<Layout {...entry.data} enableEditButton={true}   rawMarkdownContent={rawMarkdownContent} 
>
  <div class="w-full mx-auto flex flex-col min-h-screen bg-gray-50 dark:bg-gray-900 dark:text-gray-300">
    <main
      id="main-content" class="max-w-screen-2xl mx-auto px-4 sm:px-6 lg:px-8 py-6 min-h-[70vh]"
    >
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mb-6">
      <Breadcrumbs
        linkTextFormat="capitalized"
        ariaLabel="Site navigation"
        separatorAriaHidden={false}
      >
        <svg
          slot="separator"
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <polyline points="9 18 15 12 9 6"></polyline>
        </svg>
      </Breadcrumbs>
    </div>
    {entry.data.featuredImage && (
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mb-6 mt-6">
        <div class="w-full h-64 sm:h-80 md:h-96 lg:h-[450px] rounded-lg overflow-hidden shadow">
          <img
            src={entry.data.featuredImage}
            alt={entry.data.title}
            class="w-full h-full object-cover"
          />
        </div>
      </div>
    )}

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <article class="prose max-w-screen-xl withbullet markdown-content">
        {formattedDate && (
        <span class="text-sm flex justify-end">
          Posted On: {formattedDate}
        </span>
        )}
        <span id="html-content"> <Content /></span>
      </article>
    </div>
    </main>
  </div>
</Layout>
