---
import Layout from "../../layouts/Layout.astro";
import { render, getEntry } from "astro:content";
import { Breadcrumbs } from "astro-breadcrumbs";
import "astro-breadcrumbs/breadcrumbs.css";

const slug = Astro.params.slug;
const entry = await getEntry("blog", slug);
if (!entry) {
  // Handle Error, for example:
  throw new Error("Could not find data");
}
const { Content } = await render(entry);
---

<Layout {...entry.data} enableEditButton={true}>
  <div class="w-full mx-auto flex flex-col min-h-screen bg-gray-50 dark:bg-gray-900 dark:text-gray-300">
    <!-- Main Content -->
    <main
      class="max-w-screen-2xl mx-auto px-4 sm:px-6 lg:px-8 py-6 min-h-[70vh]"
    >
      <Breadcrumbs
        linkTextFormat="capitalized"
        ariaLabel="Site navigation"
        separatorAriaHidden={false}
      >
        <svg
          slot="separator"
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
          ><polyline points="9 18 15 12 9 6"></polyline>
        </svg>
      </Breadcrumbs>
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <article class="prose max-w-screen-xl withbullet markdown-content">
          <Content />
        </article>
      </div>
    </main>
  </div>
</Layout>
