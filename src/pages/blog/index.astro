---
import Layout from '../../layouts/Layout.astro';
import { getCollection } from 'astro:content';
import { Breadcrumbs } from "astro-breadcrumbs";
import "astro-breadcrumbs/breadcrumbs.css";
import BlogViewToggle from '../../components/blog/BlogViewToggle';

const posts = await getCollection('blog');
const formattedPosts = posts.map((post) => ({
  id: post.id,
  date:post.data.date,
  title: post.data.title,
  description: post.data.description,
  featuredImage: post.data.featuredImage,
  thumbImage: post.data.thumbImage,
}));
---
<Layout title="Blog">
  <div class="w-full mx-auto flex flex-col min-h-screen bg-gray-50 dark:bg-gray-900 dark:text-gray-300">
    <main class="px-4 sm:px-6 lg:px-8 py-6 min-h-[70vh]">
      <div class="px-4 sm:px-6 lg:px-8 mb-6">
        <Breadcrumbs linkTextFormat="capitalized" ariaLabel="Site navigation" separatorAriaHidden={false}>
          <svg slot="separator" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
            stroke-linecap="round" stroke-linejoin="round">
            <polyline points="9 18 15 12 9 6"></polyline>
          </svg>
        </Breadcrumbs>
      </div>

      <h1 class="text-4xl font-bold text-center mb-12 text-slate-700 dark:text-gray-300">Our Blog</h1>

      <BlogViewToggle posts={formattedPosts} client:only="react" />
    </main>
  </div>
</Layout>