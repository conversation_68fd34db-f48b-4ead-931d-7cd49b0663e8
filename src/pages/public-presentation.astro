---
import themeConfig from '../../theme.config';

const { logo } = themeConfig || {}; 
const bgColor = themeConfig?.presentationBgColor || '#FFFFFF'; // Default fallback

---
<html style={`--presentation-bg: ${bgColor};`}>
  <head>
    <meta charset="UTF-8">
    <title>Presentation</title>
    <link rel="icon" type="image/svg+xml" href="/assets/images/favicon.svg" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/reveal.js/dist/reveal.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/reveal.js/dist/theme/black.css">
    
    <link rel="stylesheet" href="/assets/styles/presentation.css">
    <!-- Include Reveal.js -->
    <script src="https://cdn.jsdelivr.net/npm/reveal.js/dist/reveal.js"></script>
  </head>
  <body>
    <div class="logo-card flex w-full px-1 py-2 text-center mb-2 justify-center bg-white drop-shadow-md">
      <img src={logo} class="h-14" alt="logo" />
    </div>
    <div class="reveal">
        <div class="slides">

            <section>
                <h3>EOH Astro 5 Public Presentation Demo</h3>
                <p>The EOH Astro 5 Theme is designed to support dynamic content creation, including blog collections, landing pages, static pages, Expectations & Outcomes Collection and GitHub Discussions integration. It provides customizable components to cater to various needs.</p>
                <h3>Key Features</h3>
                <div class="flex justify-center flex-wrap">
                <div class="w-48 h-48 flex flex-wrap items-center flex-col bg-[#0e222c] p-5 m-5 rounded-full">
                  <img src="/assets/images/ico-star.svg" alt="icon" class="size-12" />
                  <span class="text-xl">Expectations</span>
                </div>
                <div class="w-48 h-48 flex flex-wrap items-center flex-col bg-[#0e222c] p-5 m-5 rounded-full">
                  <img src="/assets/images/ico-edit.svg" alt="icon" class="size-12" />
                  <span class="text-xl">Outcomes</span>
                </div>
                <div class="w-48 h-48 flex flex-wrap items-center flex-col bg-[#0e222c] p-5 m-5 rounded-full">
                  <img src="/assets/images/ico-progressing.png" alt="icon" class="size-12" />
                  <span class="text-xl">Progress</span>
                </div>

                <div class="w-48 h-48 flex flex-wrap items-center flex-col bg-[#0e222c] p-5 m-5 rounded-full">
                  <img src="/assets/images/ico-quality.svg" alt="icon" class="size-12" />
                  <span class="text-xl">Qualityfolio</span>
                </div>
                </div>
            </section>
            <section>
              <h3>Dashboard</h3>
             <!-- <img src="/assets/images/theme-dashboard.png"  alt="" /> -->
              <div class="grid grid-cols-1 md:grid-cols-12 gap-4 items-center">
                <div class="col-span-1 md:col-span-6">
                    <img src="/assets/images/content-imge-22.jpg" alt="icon" class="w-[85%] rounded-lg" />
                </div>
                <div class="col-span-1 md:col-span-6">
                  <p class="text-2xl">The homepage provides a high-level summary of the product. Displays the project timeline, team members, priority, and latest updates.</p>
                  <h4>Key Sections</h4>
                  <aside class="flex gap-3 mb-4">
                    <div class="w-16 h-16 flex flex-wrap items-center flex-col bg-[#0e222c] rounded-full text-base">
                  <img src="/assets/images/ico-tick.svg" alt="icon" class="size-6" />
                </div>
                    <div class="basbasis-full text-lg">
                      <div class="text-xl"><b>Active Project Details</b></div>
                      <div>Shows current stage, timeline, and team members.</div>
                    </div>
                  </aside>
                  <aside class="flex gap-3 mb-4">
                    <div class="w-16 h-16 flex flex-wrap items-center flex-col bg-[#0e222c] rounded-full text-base">
                  <img src="/assets/images/ico-tick.svg" alt="icon" class="size-6" />
                </div>
                    <div class="basbasis-full text-lg">
                      <div class="text-xl"><b>Latest Updates</b></div>
                      <div>Lists recent reports and document submissions.</div>
                    </div>
                  </aside>
                  <aside class="flex gap-3 mb-4">
                    <div class="w-16 h-16 flex flex-wrap items-center flex-col bg-[#0e222c] rounded-full text-base">
                  <img src="/assets/images/ico-tick.svg" alt="icon" class="size-6" />
                </div>
                    <div class="basbasis-full text-lg">
                      <div class="text-xl"><b>Accomplishments</b></div>
                      <div>Highlights completed milestones and achievements.</div>
                    </div>
                  </aside>
                  <aside class="flex gap-3 mb-4">
                    <div class="w-16 h-16 flex flex-wrap items-center flex-col bg-[#0e222c] rounded-full text-base">
                  <img src="/assets/images/ico-tick.svg" alt="icon" class="size-6" />
                </div>
                    <div class="basbasis-full text-lg">
                      <div class="text-xl"><b>Trackers & Meeting Notes</b></div>
                      <div>Links to bug tracking and meeting documentation.</div>
                    </div>
                  </aside>
                </div>

              </div>
            </section>
            <section>
                <h3>GitHub Discussions Integration</h3>
                <p>This theme demonstrates integration with GitHub Discussions using the github-discussions-blog-loader component.</p>
                
                <h3>Setup</h3>
                <ul>
                    <li>Copy Environment Variables: <code>cp .env.example .env</code></li>
                    <li class="fragment">Configure environment variables in <code>.env</code> file.</li>
                    <li class="fragment">Use the GitHub Discussion Loader Component in templates.</li>
                </ul>
            </section>

            <section>
                <h3>Getting Started</h3>
                <p>Clone the repository, install dependencies, and run the development server to get started with the EOH Astro 5 Theme.</p>
                
                <h3>Commands</h3>
                <ul>
                    <li>Clone the Repository: <code>git clone <repository-url></code></li>
                    <li class="fragment">Install Dependencies: <code>pnpm install</code></li>
                    <li class="fragment">Start the Development Server: <code>pnpm run dev</code></li>
                    <li class="fragment">Build the Site: <code>pnpm run build</code></li>
                </ul>
            </section>
            <section>
                <p>Enjoy Building with the EOH Astro 5 Theme!</p>
            </section>
        </div>
    </div>
  </body>
</html>
<script>
  document.addEventListener("DOMContentLoaded", function () {
    Reveal.initialize();
  });
</script>