---
import Layout from "../layouts/Layout.astro";
import LatestUpdates from "../components/LatestUpdates.astro";
import themeConfig from "../../theme.config";
import SkipTo from "../components/home/<USER>";
import KeyResources from "../components/home/<USER>";
import MeetingMinutes from "../components/home/<USER>";
import LatestBlogs from "../components/home/<USER>";
import Accomplishments from "../components/home/<USER>";
import ItGovernance from "../components/home/<USER>";
import Teams from "../components/teams/teams";
import ActivityLog from "../components/activity-log/activityLog";
import WhatsNext from "../components/home/<USER>";
import ProgressNotes from "../components/home/<USER>";
import POAMList from "../components/home/<USER>";
import Trackers from "../components/home/<USER>";



const { title, description, activeProject } = themeConfig || {};
---

<Layout title="Expectations and Outcomes Hub Theme">
  <main class="max-w-full mx-auto px-3 sm:px-4 lg:px-4 pt-6 min-h-[70vh]">
    <div class="border-b border-slate-300 dark:border-gray-600 pb-3">
      <h1
        class="text-2xl lg:text-4xl font-bold text-slate-700 dark:text-gray-300 mt-2"
      >
        Welcome to {title}
      </h1>
      <p class="text-slate-500 dark:text-gray-300 mt-1 font-semibold">
        {description}
      </p>
      <p class="text-slate-500 dark:text-gray-300 mt-3 font-semibold">
        New to the Hub? <a target="_blank" href="/presentation">Click here</a> to
        view the overview presentation
      </p>
    </div>

    <section class="grid grid-cols-1 lg:grid-cols-12 gap-4 mt-6">
      <div
        class="lg:col-span-6 bg-white dark:bg-gray-800 shadow rounded-lg p-4"
      >
        <span class="font-semibold">Active Project: </span>{activeProject}
        <aside
          class="grid grid-cols-12 gap-4 flex items-center mb-3 mt-3 dark:text-gray-300"
        >
          <div class="col-span-12 md:col-span-4">
            <div class="flex gap-2.5 items-center text-sm">
              <span
                ><img
                  src="/assets/images/fi-rr-calendar.svg"
                  class="w-4 h-4"
                  alt=""
                /></span
              >
              <span>Timeline</span>
            </div>
          </div>
          <div class="col-span-12 md:col-span-8">
            <div class="flex gap-1.5 font-semibold flex-wrap">
              <span
                class="bg-gray-100 dark:bg-gray-700 text-sm px-2 py-1 rounded"
                >Q1 2025</span
              >
              <span class="text-sm px-2 py-1 rounded">Q2 2025</span>
            </div>
          </div>
        </aside>

        <aside
          class="grid grid-cols-12 gap-4 flex items-center mb-3 dark:text-gray-300"
        >
          <div class="col-span-12 md:col-span-4">
            <div class="flex gap-2.5 items-center text-sm">
              <span
                ><img
                  src="/assets/images/fi-rr-list-check.svg"
                  class="w-4 h-4"
                  alt=""
                /></span
              >
              <span>Stage</span>
            </div>
          </div>
          <div class="col-span-12 md:col-span-8">
            <div class="flex space-x-2 font-semibold">
              <span
                class="bg-orange-100 dark:bg-gray-700 text-sm px-2 py-1 rounded flex space-x-2 items-center"
              >
                <span><img src="/assets/images/ico-progress.svg" alt="" /></span
                >
                <span>In Progress</span>
              </span>
            </div>
          </div>
        </aside>

        <aside
          class="grid grid-cols-12 gap-4 flex items-center mb-3 dark:text-gray-300"
        >
          <div class="col-span-12 md:col-span-4">
            <a href="/team?userType=customer">
              <div class="flex gap-2.5 items-center text-sm">
                <span
                  ><img
                    src="/assets/images/fi-rr-rec.svg"
                    class="w-4 h-4"
                    alt=""
                  /></span
                >
                <span>{activeProject} Teams</span>
              </div>
            </a>
          </div>
          <div class="col-span-12 md:col-span-8">
            <div class="flex gap-3 font-semibold flex-wrap">
              <Teams client:only="react" userType="customer" />
            </div>
          </div>
        </aside>

        <aside
          class="grid grid-cols-12 gap-4 flex items-center mb-3 dark:text-gray-300"
        >
          <div class="col-span-12 md:col-span-4">
            <a href="/team?userType=netspective">
              <div class="flex gap-2.5 items-center text-sm">
                <span
                  ><img
                    src="/assets/images/fi-rr-rec.svg"
                    class="w-4 h-4"
                    alt=""
                  /></span
                >
                <span>Netspective Teams</span>
              </div>
            </a>
          </div>
          <div class="col-span-12 md:col-span-8">
            <div class="flex gap-3 font-semibold flex-wrap">
              <Teams client:only="react" userType="netspective" />
            </div>
          </div>
        </aside>

        <aside
          class="grid grid-cols-12 gap-4 flex items-center mb-3 dark:text-gray-300"
        >
          <div class="col-span-12 md:col-span-4">
            <div class="flex gap-2.5 items-center text-sm">
              <span
                ><img
                  src="/assets/images/fi-rr-record-vinyl.svg"
                  class="w-4 h-4"
                  alt=""
                /></span
              >
              <span>Priority</span>
            </div>
          </div>
          <div class="col-span-12 md:col-span-8">
            <div class="flex gap-1.5 font-semibold flex-wrap">
              <span
                class="bg-orange-100 dark:bg-gray-700 text-sm px-2 py-1 rounded"
                >P1</span
              >
            </div>
          </div>
        </aside>

        <aside
          class="grid grid-cols-12 gap-4 flex items-center mb-3 dark:text-gray-300"
        >
          <div class="col-span-12 md:col-span-4">
            <div class="flex gap-2.5 items-center text-sm">
              <span
                ><img
                  src="/assets/images/fi-rr-plus.svg"
                  class="w-4 h-4"
                  alt=""
                /></span
              >
              <span>Add a property</span>
            </div>
          </div>
          <div class="col-span-12 md:col-span-8"></div>
        </aside>
      </div>
      <WhatsNext showViewMoreButton={true} compact={false} />

    </section>

    <section class="mt-4 grid grid-cols-1 lg:grid-cols-12 gap-4">
      <SkipTo showViewMoreButton={true} compact={false} />

      <KeyResources showViewMoreButton={true} compact={false}/>
      <POAMList showViewMoreButton={true} compact={false}/>

      <ItGovernance showViewMoreButton={true} compact={false}/>


      <ProgressNotes showViewMoreButton={true} compact={false}/>

      <LatestUpdates showViewMoreButton={true} compact={false}/>

      <Trackers  showViewMoreButton={true} compact={false}/>

      <MeetingMinutes showViewMoreButton={true} compact={false} />
      <Accomplishments showViewMoreButton={true} />
      <LatestBlogs showViewMoreButton={true} />
      <ActivityLog
        title={title}
        recordsLimit={5}
        showViewMoreButton={true}
        hoursToFetch={1}
        client:only="react"
      />
    </section>
  </main>
</Layout>
