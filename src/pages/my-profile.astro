---
import Layout from "../layouts/Layout.astro";
import Profile from "../components/profile/profile";
import { Breadcrumbs } from "astro-breadcrumbs";
import "astro-breadcrumbs/breadcrumbs.css";
---

<Layout title="My Profile"
>
  <div class="w-full mx-auto flex">
    <main class="w-full p-7">
      <div class="md:col-span-12 dark:text-gray-300">
        <Breadcrumbs
          linkTextFormat="capitalized"
          ariaLabel="Site navigation"
          separatorAriaHidden={false}
        >
          <svg
            slot="separator"
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            ><polyline points="9 18 15 12 9 6"></polyline>
          </svg>
        </Breadcrumbs>
      </div>
      <Profile client:only="react" />
    </main>
  </div>
  <span class="text-muted-foreground hidden"></span>
</Layout>
