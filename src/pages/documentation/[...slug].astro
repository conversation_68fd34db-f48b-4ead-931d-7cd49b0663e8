---
import { render, getEntry } from "astro:content";
import Layout from "../../layouts/Layout.astro";
import Sidebar from "../../components/Sidebar";
import { buildMenuTree } from "../../utils/helper.astro";
import { Breadcrumbs } from "astro-breadcrumbs";
import "astro-breadcrumbs/breadcrumbs.css";
import themeConfig from '../../../theme.config';
import MarkdownEditor from "../../components/ContentEditor";
import EditButton from "../../components/EditMarkdownButton";

const { contentCollectionSort } = themeConfig || {}; 

const files = import.meta.glob('/src/content/documentation/**/*.{md,mdx}', { eager: true });
const dirName = "documentation";

// Filter out draft files
const publishedFiles = Object.fromEntries(
  Object.entries(files).filter(([_, file]) => !file.frontmatter?.draft)
);

// Build the menu tree with only published files
const menuTree = buildMenuTree(publishedFiles, dirName, contentCollectionSort, "asc");
//const menuTree = buildMenuTree(files, "documentation");

const slug = Astro.params.slug;
const entry = slug? await getEntry("documentation", slug) : null;
let slugval = "/documentation/" + slug;
if (!entry) {
  // Handle Error, for example:
  throw new Error("Could not find data");
}
const { Content } = await render(entry);
const rawMarkdownContent = entry.body; // Adjust if raw content is in another property
---

<Layout title={entry.data.title} enableEditButton={entry.data.enableEditButton}>
  <main class="max-w-full mx-auto min-h-[70vh] grid grid-cols-12 gap-6">
    <div
      class="col-span-12 md:col-span-4 lg:col-span-3 xl:col-span-2 bg-white dark:bg-gray-800 p-3 shadow-xs"
    >
      <div id="starlight__sidebar" class="sidebar-pane">
        <div class="sidebar-content">
          <div class="sidebar sl-flex sidebar-left-menu font-medium text-base">
            <Sidebar menuTree={menuTree} slugval={slugval} />
          </div>
        </div>
      </div>
    </div>
    <div
      class="col-span-12 md:col-span-8 lg:col-span-9 xl:col-span-10 pb-5 pt-0 md:pt-5 px-5 md:px-0 md:pr-5 dark:text-gray-300"
    >
      <Breadcrumbs
        linkTextFormat="capitalized"
        ariaLabel="Site navigation"
        separatorAriaHidden={false}
        customizeLinks={[
        { index: 2, "aria-disabled": true },
        { index: 3, "aria-disabled": true },
        { index: 4, "aria-disabled": true },
        ]}
      >
        <svg
          slot="separator"
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
          ><polyline points="9 18 15 12 9 6"></polyline>
        </svg>
      </Breadcrumbs>     
      
        <div class="">
        <div class="flex items-center justify-end"> 
          <EditButton client:only="react" />
        </div>
      </div>

      {
        (
          <article class="prose max-w-screen-xl withbullet markdown-content-doc">
            <span id="markdown-content" style="display:none">
              <MarkdownEditor
                bodyContent={rawMarkdownContent}
                client:only="react"
              />

              <div class="md:col-span-3 text-right">
                <button
                  type="button"
                  title="Save"
                  class="relative mt-3 inline-flex items-center gap-x-1.5 rounded text-white px-3 py-2 font-semibold ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-10 text-sm"
                  style="background-color: black;"
                >
                  Save
                </button>
              </div>
            </span>
            <span id="html-content">
              
              <Content />
            </span>
          </article>
        )
      }      
    </div>
  </main>
</Layout>
