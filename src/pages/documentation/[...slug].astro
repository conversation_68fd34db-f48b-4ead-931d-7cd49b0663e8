---
import { render, getEntry } from "astro:content";
import Layout from "../../layouts/Layout.astro";
import Sidebar from "../../components/Sidebar";
import { buildMenuTree } from "../../utils/helper.astro";
import { Breadcrumbs } from "astro-breadcrumbs";
import "astro-breadcrumbs/breadcrumbs.css";
import themeConfig from '../../../theme.config';
import fs from "fs/promises";
import path from "path";

const { contentCollectionSort } = themeConfig || {}; 

const files = import.meta.glob('/src/content/documentation/**/*.{md,mdx}', { eager: true });
const dirName = "documentation";

// Filter out draft files
const publishedFiles = Object.fromEntries(
  Object.entries(files).filter(([_, file]) => !file.frontmatter?.draft)
);

// Build the menu tree with only published files
const menuTree = buildMenuTree(publishedFiles, dirName, contentCollectionSort, "asc");
//const menuTree = buildMenuTree(files, "documentation");

const slug = Astro.params.slug;
const entry = slug? await getEntry("documentation", slug) : null;
let slugval = "/documentation/" + slug;
if (!entry) {
  // Handle Error, for example:
  throw new Error("Could not find data");
}

// After getting the entry:
const { id } = entry; // id will be something like "section/page-name"
const filePath = path.resolve(`./src/content/documentation/${id}.md`);
console.log(filePath,"filePath");
// Check if .mdx exists instead
let fullFilePath = filePath;
try {
  await fs.access(filePath.replace(/\.md$/, ".mdx"));
  fullFilePath = filePath.replace(/\.md$/, ".mdx");
} catch {
  // Stick with .md
}

const { Content } = await render(entry);
const rawMarkdownContent = await fs.readFile(fullFilePath, "utf-8");

---

<Layout title={entry.data.title} enableEditButton={entry.data.enableEditButton} rawMarkdownContent={rawMarkdownContent}>
  <main class="max-w-full mx-auto min-h-[70vh] grid grid-cols-12 gap-6">
    <div
      class="col-span-12 md:col-span-4 lg:col-span-3 xl:col-span-2 bg-white dark:bg-gray-800 p-3 shadow-xs"
    >
      <div id="starlight__sidebar" class="sidebar-pane">
        <div class="sidebar-content">
          <div class="sidebar sl-flex sidebar-left-menu font-medium text-base">
            <Sidebar menuTree={menuTree} slugval={slugval} />
          </div>
        </div>
      </div>
    </div>
    <div
      class="col-span-12 md:col-span-8 lg:col-span-9 xl:col-span-10 pb-5 pt-0 md:pt-5 px-5 md:px-0 md:pr-5 dark:text-gray-300"
    >
      <Breadcrumbs
        linkTextFormat="capitalized"
        ariaLabel="Site navigation"
        separatorAriaHidden={false}
        customizeLinks={[
        { index: 2, "aria-disabled": true },
        { index: 3, "aria-disabled": true },
        { index: 4, "aria-disabled": true },
        ]}
      >
        <svg
          slot="separator"
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
          ><polyline points="9 18 15 12 9 6"></polyline>
        </svg>
      </Breadcrumbs>     

      {
        (
          <article class="prose max-w-screen-xl withbullet markdown-content-doc">
            <span id="html-content">
              <Content />
            </span>
          </article>
        )
      }      
    </div>
  </main>
</Layout>
