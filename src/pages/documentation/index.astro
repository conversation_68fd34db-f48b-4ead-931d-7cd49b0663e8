---
import Layout from '../../layouts/Layout.astro';
import MyContent from '../../content/documentation/theme-documentation/documentation.md';
import Sidebar from '../../components/Sidebar';
import { buildMenuTree } from '../../utils/helper.astro';
import { Breadcrumbs } from "astro-breadcrumbs";
import "astro-breadcrumbs/breadcrumbs.css";
import themeConfig from '../../../theme.config';
import fs from "fs/promises";

const { contentCollectionSort } = themeConfig || {}; 

const files = import.meta.glob('/src/content/documentation/**/*.{md,mdx}', { eager: true });
const dirName = "documentation";

// Filter out draft files
const publishedFiles = Object.fromEntries(
  Object.entries(files).filter(([_, file]) => !file.frontmatter?.draft)
);

// Build the menu tree with only published files
const menuTree = buildMenuTree(publishedFiles, dirName, contentCollectionSort, "asc");
// Read raw markdown content
const rawMarkdownContent = await fs.readFile(
  "src/content/documentation/theme-documentation/documentation.md",
  "utf-8"
);
---
<Layout title='Documentation' enableEditButton={true} rawMarkdownContent={rawMarkdownContent}>	
	<main class="max-w-full mx-auto min-h-[70vh] grid grid-cols-12 gap-6 flex">
    <div class="col-span-12 md:col-span-4 lg:col-span-3 xl:col-span-2 bg-white dark:bg-gray-800 p-3 shadow-xs">
      <!-- Left Menu Content Goes Here -->
      <div id="starlight__sidebar" class="sidebar-pane">
        <div class="sidebar-content">
          <div class="sidebar sl-flex sidebar-left-menu font-medium text-base">            
          <Sidebar menuTree={menuTree} />
          </div>
        </div>
      </div>
    </div>
    <div class="col-span-12 md:col-span-8 lg:col-span-9 xl:col-span-10 pb-5 pt-0 md:pt-5 px-5 md:px-0 md:pr-5 dark:text-gray-300">
      <Breadcrumbs linkTextFormat="capitalized"  ariaLabel="Site navigation" separatorAriaHidden={false}
      >
      <svg
        slot="separator"
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
        ><polyline points="9 18 15 12 9 6"></polyline>
      </svg>
      </Breadcrumbs>

    <article class="prose max-w-screen-xl withbullet markdown-content-doc mt-5">
      <MyContent />
    </article> 
    </div>
  </main>
</Layout>

