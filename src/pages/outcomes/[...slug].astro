---
import { type CollectionEntry, getCollection } from "astro:content";
import { render, getEntry } from "astro:content";
import Layout from "../../layouts/Layout.astro";
import Sidebar from "../../components/Sidebar";
import { buildMenuTree } from "../../utils/helper.astro";
import { Breadcrumbs } from "astro-breadcrumbs";
import "astro-breadcrumbs/breadcrumbs.css";
import themeConfig from '../../../theme.config';
import LatestCommit from "../../components/git-commit/latestCommit.astro";
import CommitHistory from "../../components/git-commit/commitHistory.tsx";
import LatestMoreCommit from "../../components/git-commit/latestMoreCommit.astro";
import ActivityLog from "../../components/activity-log/activityLog";
import MarkdownEditor from "../../components/MarkdownEditor";
import fs from "fs/promises";
import path from "path";

const { contentCollectionSort } = themeConfig || {}; 
const { enablePageHistory } = themeConfig || {};

const files = import.meta.glob("/src/content/outcomes/**/*.{md,mdx}", {
  eager: true,
});

const dirName = "outcomes";

// Build the menu tree
const menuTree = buildMenuTree(files, dirName,contentCollectionSort,"asc");
//const menuTree = buildMenuTree(files, "outcomes");

const slug = Astro.params.slug;
const entry = await getEntry("outcomes", slug);
const entryid = entry?.id;
let slugval = "/outcomes/" + slug;
if (!entry) {
  // Handle Error, for example:
  throw new Error("Could not find data");
}
// After getting the entry:
const { id } = entry; // id will be something like "section/page-name"
const filePath = path.resolve(`./src/content/outcomes/${id}.md`);
// Check if .mdx exists instead
let fullFilePath = filePath;
try {
  await fs.access(filePath.replace(/\.md$/, ".mdx"));
  fullFilePath = filePath.replace(/\.md$/, ".mdx");
} catch {
  // Stick with .md
}

const { Content } = await render(entry);
const rawMarkdownContent = await fs.readFile(fullFilePath, "utf-8");
const pageUrl=Astro.url.href;

---

<Layout title={entry.data.title} enableEditButton={entry.data.enableEditButton} rawMarkdownContent={rawMarkdownContent}>
  <main id="main-content" class="max-w-full mx-auto min-h-[70vh] grid grid-cols-12 gap-6 flex">
    <div
      class="col-span-12 md:col-span-4 lg:col-span-2 xl:col-span-2 bg-white  dark:bg-gray-800 p-3 shadow-xs"
    >
      <div id="starlight__sidebar" class="sidebar-pane">
        <div class="sidebar-content">
          <div class="sidebar sl-flex sidebar-left-menu font-medium text-base">
            <Sidebar menuTree={menuTree} slugval={slugval} />
          </div>
        </div>
      </div>
    </div>
    <div
      class="col-span-12 md:col-span-8 lg:col-span-7 xl:col-span-7 pb-5 pt-0 md:pt-5 px-5 md:px-0 md:pr-5"
    >
      <Breadcrumbs
        linkTextFormat="capitalized"
        ariaLabel="Site navigation"
        separatorAriaHidden={false}
        customizeLinks={[
        { index: 2, "aria-disabled": true },
        { index: 3, "aria-disabled": true },
        { index: 4, "aria-disabled": true },
        ]}
      >
        <svg
          slot="separator"
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
          ><polyline points="9 18 15 12 9 6"></polyline>
        </svg>
      </Breadcrumbs>
       <article class="prose max-w-screen-xl withbullet markdown-content">
        <span id="html-content"> <Content /></span>
      </article>
    </div>
    
    
      <div class="col-span-12 md:col-span-12 lg:col-span-3 xl:col-span-3 lg:pb-5 pt-0 lg:pt-5 px-5 md:px-0 md:pr-5 dark:text-gray-300">
      <aside style="display: block; padding-top:20px;" class="lg:min-h-[700px]">
        <div class="flow-root">
          <ul
            class="flex flex-wrap -mb-px font-medium text-center"
            id="history-tab"
            data-tabs-toggle="#default-tab-content"
          >
          { enablePageHistory ? (
            <li class="mt-3">
              <button
                class="inline-block py-4 px-2 lg:px-2 xl:px-4 border-b-2 rounded-t-lg hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300"
                id="dashboard-tab"
                data-tabs-target="#dashboard"
                type="button"
                role="tab"
                aria-controls="dashboard"
                aria-selected="false"
                aria-label="Commit History"
              >
                Page History
              </button>
            </li>
            ) : "" }
            <li class="ml-4 mt-3">
              <button
                class="inline-block py-4 px-2 lg:px-2 xl:px-4 border-b-2 rounded-t-lg hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300"
                id="activity-tab"
                data-tabs-target="#activity"
                type="button"
                role="tab"
                aria-controls="activity"
                aria-selected="false"
                aria-label="Activity"
              >
                Activity
              </button>
            </li>
          </ul>
          <div id="default-tab-content">
         
            <div
              class="hidden p-4 px-0 rounded-lg bg-gray-50 dark:bg-gray-800"
              id="dashboard"
              role="tabpanel"
              aria-labelledby="dashboard-tab"
            >              
            <div>
              <LatestCommit filename={entryid} pageTitle={entry?.data.title} />
            </div>
            <div>
              <CommitHistory filename={entryid} client:only="react"/>   
            </div>            
            </div>
            <div
              class="hidden py-4 rounded-lg bg-gray-50 dark:bg-gray-800 mt-2"
              id="activity"
              role="tabpanel"
              aria-labelledby="activity-tab"
            >
             <ActivityLog
            pageUrl={pageUrl}
            recordsLimit={5}
            showViewMoreButton={false}
            hoursToFetch={8,760}
             client:only="react"
      />
            </div>
          </div>
        </div>
      </aside>
      </div>
      
      <!-- <LatestMoreCommit filename={entryid} pageTitle={entry?.data.title} /> -->
  </main>
</Layout>
