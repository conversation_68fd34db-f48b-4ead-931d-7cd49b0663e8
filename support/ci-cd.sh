#echo -e "PUBLIC_GITHUB_TOKEN=${PUBLIC_GITHUB_TOKEN}\nPUBLIC_GITHUB_REPO_NAME=${PUBLIC_GITHUB_REPO_NAME}\nPUBLIC_GITHUB_OWNER_NAME=${PUBLIC_GITHUB_OWNER_NAME}" > .env
echo -e "PUBLIC_GITHUB_TOKEN=${PUBLIC_GITHUB_TOKEN}\nPUBLIC_GITHUB_REPO_NAME=${PUBLIC_GITHUB_REPO_NAME}\nPUBLIC_GITHUB_OWNER_NAME=${PUBLIC_GITHUB_OWNER_NAME}\nPUBLIC_ZITADEL_CLIENT_ID=${PUBLIC_ZITADEL_CLIENT_ID}\nPUBLIC_ZITADEL_AUTHORITY=${PUBLIC_ZITADEL_AUTHORITY}\nPUBLIC_ZITADEL_REDIRECT_URI=${PUBLIC_ZITADEL_REDIRECT_URI}\nPUBLIC_ZITADEL_LOGOUT_REDIRECT_URI=${PUBLIC_ZITADEL_LOGOUT_REDIRECT_URI}\nPUBLIC_ZITADEL_ORGANIZATION_ID=${PUBLIC_ZITADEL_ORGANIZATION_ID}\nPUBLIC_ZITADEL_PROJECT_ID=${PUBLIC_ZITADEL_PROJECT_ID}\nENABLE_DEFAULT_AUTH=${ENABLE_DEFAULT_AUTH}\nENABLE_ZITADEL_AUTH=${ENABLE_ZITADEL_AUTH}\nPUBLIC_ZITADEL_API_TOKEN=${PUBLIC_ZITADEL_API_TOKEN}\nPUBLIC_QUALITYFOLIO_URL=${PUBLIC_QUALITYFOLIO_URL}\nENABLE_OPEN_OBSERVE=${ENABLE_OPEN_OBSERVE}\nENABLE_DB_TEAM=${ENABLE_DB_TEAM}\nPUBLIC_TEAM_DB=${PUBLIC_TEAM_DB}\nPUBLIC_LFORM_DB=${PUBLIC_LFORM_DB}\nENABLE_IMAP_DB_PREPARE=${ENABLE_IMAP_DB_PREPARE}\nENABLE_IMAP_VIEW=${ENABLE_IMAP_VIEW}\nPUBLIC_IMAP_DB=${PUBLIC_IMAP_DB}\nIMAP_FOLDER=${IMAP_FOLDER}\nIMAP_USER_NAME=${IMAP_USER_NAME}\nIMAP_PASS=${IMAP_PASS}\nIMAP_HOST=${IMAP_HOST}\nPUBLIC_OPENOBSERVE_URL=${PUBLIC_OPENOBSERVE_URL}\nPUBLIC_OPENOBSERVE_TOKEN=${PUBLIC_OPENOBSERVE_TOKEN}\nPUBLIC_NMAP_DB=${PUBLIC_NMAP_DB}\nPUBLIC_RSSD_DB=${PUBLIC_RSSD_DB}\nPUBLIC_NOVU_API_URL=${PUBLIC_NOVU_API_URL}\nPUBLIC_NOVU_SUBSCRIBER_ID=${PUBLIC_NOVU_SUBSCRIBER_ID}\nPUBLIC_NOVU_API_KEY=${PUBLIC_NOVU_API_KEY}\nPUBLIC_NOVU_CONTACTUS_TEMPLATE=${PUBLIC_NOVU_CONTACTUS_TEMPLATE}\nPUBLIC_NOVU_CONTACTUS_ADMIN_EMAIL=${PUBLIC_NOVU_CONTACTUS_ADMIN_EMAIL}\nPUBLIC_NOVU_ADMIN_TEMPLATE=${PUBLIC_NOVU_ADMIN_TEMPLATE}\nPUBLIC_GITHUB_PAT=${PUBLIC_GITHUB_PAT}\nPUBLIC_GITHUB_OWNER=${PUBLIC_GITHUB_OWNER}\nPUBLIC_GITHUB_REPO=${PUBLIC_GITHUB_REPO}\nENABLE_SUPPORT_AND_FEEDBACK=${ENABLE_SUPPORT_AND_FEEDBACK}\nPUBLIC_PRODUCTION_URL=${PUBLIC_PRODUCTION_URL}\nPUBLIC_NOVU_COMMENT_NOTIFICATION_TEMPLATE=${PUBLIC_NOVU_COMMENT_NOTIFICATION_TEMPLATE}\nPUBLIC_SQLMAP_DB=${PUBLIC_SQLMAP_DB}\nPUBLIC_FLEETFOLIO_URL=${PUBLIC_FLEETFOLIO_URL}\nPUBLIC_NOTIFICATION_FOR_ALL_MEMBERS=${PUBLIC_NOTIFICATION_FOR_ALL_MEMBERS}\nPUBLIC_ZAP_DB=${PUBLIC_ZAP_DB}\nBOX_CLIENT_ID=${BOX_CLIENT_ID}\nBOX_CLIENT_SECRET=${BOX_CLIENT_SECRET}\nBOX_ACCESS_TOKEN=${BOX_ACCESS_TOKEN}\nBOX_REFRESH_TOKEN=${BOX_REFRESH_TOKEN}\nBOX_FOLDER_ID=${BOX_FOLDER_ID}\nPUBLIC_BOX_API_URL=${PUBLIC_BOX_API_URL}\nGDRIVE_JSON_PATH=${GDRIVE_JSON_PATH}\nENABLE_QUALITYFOLIO_PREPARE=${ENABLE_QUALITYFOLIO_PREPARE}\nPUBLIC_QUALITYFOLIO_DB=${PUBLIC_QUALITYFOLIO_DB}\nPUBLIC_RELEASE_NOTES_GITHUB_TOKEN=${PUBLIC_RELEASE_NOTES_GITHUB_TOKEN}\nPUBLIC_CONTENT_GITHUB_OWNER=${PUBLIC_CONTENT_GITHUB_OWNER}\nPUBLIC_CONTENT_GITHUB_REPO=${PUBLIC_CONTENT_GITHUB_REPO}" > .env
