# Enable/Disabl default Authentication Features
<PERSON>NA<PERSON>E_DEFAULT_AUTH=false # Set to 'true' to enable default authentication

# Enable/Disable ZITADEL Authentication Features
ENABLE_ZITADEL_AUTH=true  # Set to 'true' to enable ZITADEL authentication

# ZITADEL Configuration
PUBLIC_ZITADEL_CLIENT_ID="your-zitadel-client-id" # client ID of ZITADEL application
PUBLIC_ZITADEL_AUTHORITY="https://your-zitadel-authority-url" #base URL of  ZITADEL instance
PUBLIC_ZITADEL_REDIRECT_URI="http://redirecturi/post-authorization/" #URL where users are redirected after logging in with ZITADEL
PUBLIC_ZITADEL_LOGOUT_REDIRECT_URI="http://redirecturi/" #URL where users are redirected after logging out
PUBLIC_ZITADEL_ORGANIZATION_ID="your-zitadel-organization-id" #ID of organization within ZITADEL
PUBLIC_ZITADEL_PROJECT_ID="your-zitadel-project-id" #ID of the ZITADEL project associated with authentication system
PUBLIC_ZITADEL_API_TOKEN=xxxxxxxxxxxxxx #organization specific ZITADEL token
# GITHub
PUBLIC_GITHUB_TOKEN=ghp_xxxxxxxxxxxxxxxxxxx
PUBLIC_GITHUB_REPO_NAME=xxxxxxxxxxx
PUBLIC_GITHUB_OWNER_NAME=xxxxx


#Qualityfolio
PUBLIC_QUALITYFOLIO_URL="xxxxxxx"
ENABLE_QUALITYFOLIO_PREPARE=true # Set to 'true' to enable qualityfolio data preparation
PUBLIC_QUALITYFOLIO_DB="src/content/db/qualityfolio/resource-surveillance.sqlite.db" # Path to qualityfolio database

#Fleetfolio
PUBLIC_FLEETFOLIO_URL="xxxxxxx"

# TEAM DB Configuration
PUBLIC_TEAM_DB="dbpath/file.db"
ENABLE_DB_TEAM=true # Set to 'true' to enable

# LForm DB Configuration
PUBLIC_LFORM_DB = "src/content/db/lforms/resource-surveillance.sqlite.db"
PUBLIC_RSSD_DB = "src/content/db/rssd/resource-surveillance.sqlite.db"
#I-map details

ENABLE_IMAP_DB_PREPARE=true # Enable IMAP database preparation
ENABLE_IMAP_VIEW=true # Enable IMAP view to ingest and display mail content
PUBLIC_IMAP_DB="src/content/db/imap-mail-db/resource-surveillance.sqlite.db" # Path to the SQLite database file (Do not change this path)
# IMAP Configuration
IMAP_FOLDER=Inbox
IMAP_USER_NAME=<EMAIL>
IMAP_PASS=your-password
IMAP_HOST=imap.example.com

PUBLIC_OPENOBSERVE_URL=xxxxxxxxxxx
PUBLIC_OPENOBSERVE_TOKEN=xxxxxxxxxxx

# Support and Feedback widget
PUBLIC_NOVU_API_URL="novu-api-url"
PUBLIC_NOVU_SUBSCRIBER_ID="your-subscriber-id"
PUBLIC_NOVU_API_KEY="your-api-key"
PUBLIC_NOVU_CONTACTUS_TEMPLATE="contact-us-template"
PUBLIC_NOVU_CONTACTUS_ADMIN_EMAIL="admin-email"
PUBLIC_NOVU_ADMIN_TEMPLATE="novu-admin-template-id"
PUBLIC_GITHUB_PAT = "your-personal-access-token"
PUBLIC_GITHUB_OWNER = "your-github-username"
PUBLIC_GITHUB_REPO = "your-repository-name"
ENABLE_SUPPORT_AND_FEEDBACK=true

#comment env
PUBLIC_PRODUCTION_URL="https://demo.hub.opsfolio.com" # url of respective site
PUBLIC_NOVU_COMMENT_NOTIFICATION_TEMPLATE="eoh-comment-notification" # template for comment notification
PUBLIC_NOTIFICATION_FOR_ALL_MEMBERS=false #If set to "true," the comment notification will be sent to all members; otherwise, it will be sent only to the mentioned members.
# Nmap -security Report DB Configuration
PUBLIC_NMAP_DB="src/content/db/nmap/nmap.db"
#PUBLIC_NMAP_DB="src/content/db/nmap/nmap.db"  # Path to the SQLite database file for nmap (Do not change this path)
PUBLIC_SQLMAP_DB="src/content/db/sqlmap/resource-surveillance.sqlite.db" #Path to the SQLite database file for sqlmap (Do not change this path)
PUBLIC_SUBFINDER_DB="src/content/db/subfinder/resource-surveillance.sqlite.db" #Path to the SQLite database file for subfinder (Do not change this path)
PUBLIC_NUCLEI_DB="src/content/db/nuclei/resource-surveillance.sqlite.db" #Path to the SQLite database file for nuclei (Do not change this path)
PUBLIC_DIRB_DB="src/content/db/dirb/resource-surveillance.sqlite.db"


# Box folder list items
BOX_CLIENT_ID=your_client_id
BOX_CLIENT_SECRET=your_client_secret
BOX_FOLDER_ID=your_target_folder_id

# Google drive list items
GDRIVE_JSON_PATH = 'src/keys/eoh-gdrive-d5652cb28ca5.json'